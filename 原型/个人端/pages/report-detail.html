<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告详情 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f5f5f7; height: 100vh; display: flex; flex-direction: column; }
        .status-bar { height: 44px; background-color: #ffffff; display: flex; align-items: center; padding: 0 16px; font-size: 14px; color: #000; }
        .nav-bar { height: 44px; background-color: #ffffff; display: flex; align-items: center; justify-content: space-between; padding: 0 16px; border-bottom: 1px solid #e5e5e5; }
        .content { flex: 1; padding: 16px; overflow-y: auto; }
        .card { background: white; border-radius: 16px; padding: 24px; }
        .title { font-size: 20px; font-weight: 700; margin-bottom: 12px; }
        .date { font-size: 12px; color: #999; margin-bottom: 16px; }
        .desc { color: #444; font-size: 15px; }
        .metrics-box {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
        }
        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .metric-row:last-child {
            margin-bottom: 0;
        }
        .metric-label {
            color: #666;
            font-size: 15px;
        }
        .metric-value {
            color: #007AFF;
            font-size: 16px;
            font-weight: 600;
        }
        .conclusion {
            color: #333;
            font-size: 16px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="status-bar">
        <div class="flex justify-between w-full">
            <span>9:41</span>
            <div class="flex space-x-2">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
    </div>
    <div class="nav-bar">
        <a href="reports.html" class="text-blue-500"><i class="fas fa-chevron-left"></i> 返回</a>
        <span class="font-semibold">报告详情</span>
        <div style="width: 24px;"></div>
    </div>
    <div class="content">
        <div class="card">
            <div class="title">不宁腿制动试验报告</div>
            <div class="date">报告日期：2025-06-01</div>
            <div class="metrics-box">
                <div class="metric-row"><span class="metric-label">试验日期</span><span class="metric-value">2025/05/1</span></div>
                <div class="metric-row"><span class="metric-label">疼痛评分-0分钟</span><span class="metric-value">1</span></div>
                <div class="metric-row"><span class="metric-label">疼痛评分-10分钟</span><span class="metric-value">2</span></div>
                <div class="metric-row"><span class="metric-label">疼痛评分-20分钟</span><span class="metric-value">3</span></div>
                <div class="metric-row"><span class="metric-label">疼痛评分-30分钟</span><span class="metric-value">3</span></div>
                <div class="metric-row"><span class="metric-label">疼痛评分-40分钟</span><span class="metric-value">3</span></div>
                <div class="metric-row"><span class="metric-label">疼痛评分-50分钟</span><span class="metric-value">3</span></div>
                <div class="metric-row"><span class="metric-label">疼痛评分-60分钟</span><span class="metric-value">3</span></div>
            </div>
            <div class="conclusion">患者第一次制动试验后睡着，终止试验</div>
        </div>
    </div>
</body>
</html> 