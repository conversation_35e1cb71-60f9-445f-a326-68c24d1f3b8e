<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f7;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
        }
        .content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }
        .tab-bar {
            height: 83px;
            background-color: #ffffff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8e8e93;
            text-decoration: none;
        }
        .tab-item.active {
            color: #007AFF;
        }
        .tab-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        .task-flow {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
        }
        .task-step {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            position: relative;
        }
        .task-step:last-child {
            margin-bottom: 0;
        }
        .step-number {
            width: 24px;
            height: 24px;
            background: #e5e5e5;
            color: #666;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            position: relative;
            z-index: 1;
        }
        .step-number.completed {
            background: #007AFF;
            color: white;
        }
        .step-text {
            color: #666;
        }
        .step-text.completed {
            color: #007AFF;
            font-weight: 500;
        }
        .step-line {
            width: 2px;
            height: 24px;
            background: #e5e5e5;
            margin-left: 12px;
            margin-bottom: -16px;
            position: relative;
            z-index: 0;
        }
        .step-line.completed {
            background: #007AFF;
        }
        .questionnaire-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .questionnaire-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        .questionnaire-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 16px;
        }
        .btn-start {
            background: #007AFF;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex justify-between w-full">
            <span>9:41</span>
            <div class="flex space-x-2">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="task-flow">
            <h2 class="text-lg font-semibold mb-4">任务流程</h2>
            <div class="task-step">
                <div class="step-number completed">1</div>
                <div class="step-text completed">完善个人信息</div>
            </div>
            <div class="step-line completed"></div>
            <div class="task-step">
                <div class="step-number">2</div>
                <div class="step-text">填写问卷</div>
            </div>
            <div class="step-line"></div>
            <div class="task-step">
                <div class="step-number">3</div>
                <div class="step-text">筛查</div>
            </div>
            <div class="step-line"></div>
            <div class="task-step">
                <div class="step-number">4</div>
                <div class="step-text">诊断</div>
            </div>
            <div class="step-line"></div>
            <div class="task-step">
                <div class="step-number">5</div>
                <div class="step-text">治疗</div>
            </div>
            <div class="step-line"></div>
            <div class="task-step">
                <div class="step-number">6</div>
                <div class="step-text">随访</div>
            </div>
            <div class="step-line"></div>
            <div class="task-step">
                <div class="step-number">7</div>
                <div class="step-text">治疗结束</div>
            </div>
        </div>

        <div class="questionnaire-card">
            <div class="questionnaire-title">GSAQ 睡眠状况问卷</div>
            <div class="questionnaire-desc">请认真填写以下问卷，帮助我们更好地了解您的睡眠状况</div>
            <div class="btn-start">开始填写</div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <a href="home.html" class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="profile.html" class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html> 