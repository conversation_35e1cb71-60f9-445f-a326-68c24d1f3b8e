<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
        }
        .register-container {
            flex: 1;
            padding: 32px 24px;
        }
        .input-group {
            position: relative;
            margin-bottom: 24px;
        }
        .input-group input {
            width: 100%;
            padding: 16px;
            border: 1px solid #e5e5e5;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s;
        }
        .input-group input:focus {
            border-color: #007AFF;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0,122,255,0.1);
        }
        .btn-primary {
            width: 100%;
            padding: 16px;
            background-color: #007AFF;
            color: white;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary:hover {
            background-color: #0066CC;
        }
        .btn-verify {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            padding: 8px 16px;
            background-color: #007AFF;
            color: white;
            border-radius: 8px;
            font-size: 14px;
            border: none;
            cursor: pointer;
        }
        .login-link {
            text-align: center;
            margin-top: 24px;
            color: #007AFF;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex justify-between w-full">
            <span>9:41</span>
            <div class="flex space-x-2">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
    </div>

    <div class="register-container">
        <div class="flex justify-center mb-8">
            <img src="../images/logo.jpg" alt="Logo" class="w-32 h-32 object-contain">
        </div>
        <div class="flex justify-center mb-4">
            <span class="bg-blue-500 text-white text-xs px-4 py-1 rounded-full shadow">个人端</span>
        </div>
        <h1 class="text-3xl font-bold mb-4 text-center">居家睡眠远程管理平台</h1>
        <p class="text-gray-600 mb-8 text-center">注册账号</p>

        <form id="registerForm">
            <div class="input-group">
                <input type="tel" placeholder="请输入手机号" pattern="[0-9]{11}" required>
            </div>
            <div class="input-group">
                <input type="text" placeholder="请输入验证码" required>
                <button type="button" class="btn-verify">获取验证码</button>
            </div>
            <div class="input-group">
                <input type="password" placeholder="请设置密码" required>
            </div>
            <div class="input-group">
                <input type="password" placeholder="请确认密码" required>
            </div>
            <button type="submit" class="btn-primary">注册</button>
        </form>

        <a href="login.html" class="login-link block mt-6">已有账号？立即登录</a>
    </div>

    <script>
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            window.location.href = 'home.html';
        });

        // 验证码倒计时
        let countdown = 60;
        const verifyBtn = document.querySelector('.btn-verify');
        verifyBtn.addEventListener('click', function() {
            if (countdown === 60) {
                this.disabled = true;
                const timer = setInterval(() => {
                    if (countdown > 0) {
                        this.textContent = `${countdown}秒后重试`;
                        countdown--;
                    } else {
                        this.textContent = '获取验证码';
                        this.disabled = false;
                        countdown = 60;
                        clearInterval(timer);
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html> 