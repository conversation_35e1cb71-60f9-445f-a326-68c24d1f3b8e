<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
        }
        .login-container {
            flex: 1;
            padding: 32px 24px;
        }
        .input-group {
            position: relative;
            margin-bottom: 24px;
        }
        .input-group input {
            width: 100%;
            padding: 16px;
            border: 1px solid #e5e5e5;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s;
        }
        .input-group input:focus {
            border-color: #007AFF;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0,122,255,0.1);
        }
        .btn-primary {
            width: 100%;
            padding: 16px;
            background-color: #007AFF;
            color: white;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary:hover {
            background-color: #0066CC;
        }
        .register-link {
            text-align: center;
            margin-top: 24px;
            color: #007AFF;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex justify-between w-full">
            <span>9:41</span>
            <div class="flex space-x-2">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
    </div>

    <div class="login-container">
        <div class="flex justify-center mb-8">
            <img src="../images/logo.jpg" alt="Logo" class="w-32 h-32 object-contain">
        </div>
        <div class="flex justify-center mb-4">
            <span class="bg-blue-500 text-white text-xs px-4 py-1 rounded-full shadow">个人端</span>
        </div>
        <h1 class="text-3xl font-bold mb-4 text-center">居家睡眠远程管理平台</h1>
        <p class="text-gray-600 mb-8 text-center">欢迎登录</p>

        <form id="loginForm">
            <div class="input-group">
                <input type="tel" placeholder="请输入手机号" pattern="[0-9]{11}" required>
            </div>
            <div class="input-group">
                <input type="password" placeholder="请输入密码" required>
            </div>
            <button type="submit" class="btn-primary">登录</button>
        </form>

        <a href="register.html" class="register-link block mt-6">还没有账号？立即注册</a>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            window.location.href = 'home.html';
        });
    </script>
</body>
</html> 