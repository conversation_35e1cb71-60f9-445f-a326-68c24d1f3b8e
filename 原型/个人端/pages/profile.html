<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f7;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
        }
        .content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }
        .tab-bar {
            height: 83px;
            background-color: #ffffff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #8e8e93;
            text-decoration: none;
        }
        .tab-item.active {
            color: #007AFF;
        }
        .tab-item i {
            font-size: 24px;
            margin-bottom: 4px;
        }
        .profile-header {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }
        .avatar {
            width: 64px;
            height: 64px;
            border-radius: 32px;
            background: #e5e5e5;
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #666;
        }
        .menu-list {
            background: white;
            border-radius: 16px;
            overflow: hidden;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f5f5f7;
        }
        .menu-item:last-child {
            border-bottom: none;
        }
        .menu-item i {
            width: 24px;
            margin-right: 12px;
            color: #007AFF;
        }
        .menu-item span.menu-item-text {
            /* flex: 1; 这个规则将从这里移除，并应用到新的容器 */
        }
        .menu-item .flex-grow-container {
            flex: 1; /* 新的容器负责伸展 */
            display: flex;
            align-items: center;
        }
        .menu-item .arrow {
            color: #c7c7cc;
        }
        .menu-item .badge-ios {
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent; /* 背景透明 */
            border: 1px solid #c7c7cc; /* 浅灰色细边框 */
            color: #007AFF;
            border-radius: 9999px;
            font-size: 13px;
            font-weight: 600;
            width: 22px !important;
            height: 22px !important;
            min-width: 22px !important; /* 确保不会缩小 */
            max-width: 22px !important; /* 确保不会拉伸 */
            margin-left: 8px;
            box-shadow: none;
            padding: 0;
            flex: 0 0 22px !important; /* 明确设置 Flex 基础尺寸，并使用!important */
            box-sizing: border-box; /* 显式声明盒模型 */
            white-space: nowrap; /* 防止文本换行 */
            overflow: hidden; /* 隐藏溢出内容 */
        }
        .logout-btn-ios {
            width: 100%;
            margin-top: 32px;
            padding: 12px 0;
            background: #fff;
            color: #333;
            border: 1px solid #e5e5e5;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            box-shadow: 0 1px 2px rgba(0,0,0,0.03);
            transition: background 0.2s, color 0.2s;
        }
        .logout-btn-ios:hover {
            background: #f5f5f7;
            color: #007AFF;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex justify-between w-full">
            <span>9:41</span>
            <div class="flex space-x-2">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="profile-header">
            <div class="avatar">
                <i class="fas fa-user"></i>
            </div>
            <div>
                <h2 class="text-xl font-semibold">张先生</h2>
                <p class="text-gray-500">手机号：138****8888</p>
            </div>
        </div>

        <div class="menu-list">
            <a href="edit-profile.html" class="menu-item">
                <i class="fas fa-user-circle"></i>
                <div class="flex-grow-container">
                    <span class="menu-item-text">个人基本信息</span>
                </div>
                <i class="fas fa-chevron-right arrow"></i>
            </a>
            <a href="questionnaires.html" class="menu-item">
                <i class="fas fa-file-alt"></i>
                <div class="flex-grow-container">
                    <span class="menu-item-text">我的问卷</span>
                </div>
                <span class="badge-ios">2</span>
                <i class="fas fa-chevron-right arrow ml-2"></i>
            </a>
            <a href="reports.html" class="menu-item">
                <i class="fas fa-notes-medical"></i>
                <div class="flex-grow-container">
                    <span class="menu-item-text">诊断报告</span>
                </div>
                <span class="badge-ios">1</span>
                <i class="fas fa-chevron-right arrow ml-2"></i>
            </a>
            <a href="notifications.html" class="menu-item">
                <i class="fas fa-bell"></i>
                <div class="flex-grow-container">
                    <span class="menu-item-text">消息通知</span>
                </div>
                <i class="fas fa-chevron-right arrow"></i>
            </a>
        </div>
        <button onclick="logout()" class="logout-btn-ios">退出登录</button>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <a href="home.html" class="tab-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="profile.html" class="tab-item active">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
    function logout() {
        window.location.href = 'login.html';
    }
    </script>
</body>
</html> 