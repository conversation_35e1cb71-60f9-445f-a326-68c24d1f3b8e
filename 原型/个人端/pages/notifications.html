<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息通知 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f7;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            border-bottom: 1px solid #e5e5e5;
        }
        .content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }
        .message-list {
            background: white;
            border-radius: 16px;
            overflow: hidden;
        }
        .message-item {
            padding: 16px;
            border-bottom: 1px solid #f5f5f7;
            display: flex;
            align-items: flex-start;
        }
        .message-item:last-child {
            border-bottom: none;
        }
        .message-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;
        }
        .message-content {
            flex: 1;
        }
        .message-title {
            font-weight: 600;
            margin-bottom: 4px;
        }
        .message-text {
            color: #666;
            font-size: 14px;
            margin-bottom: 4px;
        }
        .message-time {
            color: #999;
            font-size: 12px;
        }
        .unread {
            background-color: #f0f7ff;
        }
        .unread .message-title {
            color: #007AFF;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex justify-between w-full">
            <span>9:41</span>
            <div class="flex space-x-2">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <a href="profile.html" class="text-blue-500">
            <i class="fas fa-chevron-left"></i> 返回
        </a>
        <span class="font-semibold">消息通知</span>
        <div style="width: 24px;"></div>
    </div>

    <div class="content">
        <div class="message-list">
            <div class="message-item unread">
                <div class="message-icon">
                    <i class="fas fa-file-medical text-blue-500"></i>
                </div>
                <div class="message-content">
                    <div class="message-title">问卷填写提醒</div>
                    <div class="message-text">您有一份睡眠状况问卷待填写，请及时完成。</div>
                    <div class="message-time">10分钟前</div>
                </div>
            </div>

            <div class="message-item">
                <div class="message-icon">
                    <i class="fas fa-user-md text-green-500"></i>
                </div>
                <div class="message-content">
                    <div class="message-title">医生回复</div>
                    <div class="message-text">您的睡眠报告已生成，医生建议您注意作息规律。</div>
                    <div class="message-time">2小时前</div>
                </div>
            </div>

            <div class="message-item">
                <div class="message-icon">
                    <i class="fas fa-calendar-check text-purple-500"></i>
                </div>
                <div class="message-content">
                    <div class="message-title">随访预约</div>
                    <div class="message-text">您已成功预约下周三的随访，请准时参加。</div>
                    <div class="message-time">昨天</div>
                </div>
            </div>

            <div class="message-item">
                <div class="message-icon">
                    <i class="fas fa-bell text-orange-500"></i>
                </div>
                <div class="message-content">
                    <div class="message-title">系统通知</div>
                    <div class="message-text">您的个人信息已更新成功。</div>
                    <div class="message-time">3天前</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 