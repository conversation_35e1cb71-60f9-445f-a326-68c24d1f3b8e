<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人信息编辑 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f7;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .status-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            border-bottom: 1px solid #e5e5e5;
        }
        .content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }
        .form-group {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            margin-bottom: 16px;
        }
        .form-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f5f5f7;
        }
        .form-item:last-child {
            border-bottom: none;
        }
        .form-label {
            width: 100px;
            color: #333;
        }
        .form-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
        }
        .form-select {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            background: transparent;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }
        .btn-save {
            width: 100%;
            padding: 16px;
            background-color: #007AFF;
            color: white;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 24px;
        }
        .btn-save:hover {
            background-color: #0066CC;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex justify-between w-full">
            <span>9:41</span>
            <div class="flex space-x-2">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <a href="profile.html" class="text-blue-500">
            <i class="fas fa-chevron-left"></i> 返回
        </a>
        <span class="font-semibold">个人信息编辑</span>
        <div style="width: 24px;"></div>
    </div>

    <div class="content">
        <form id="profileForm">
            <div class="form-group">
                <div class="form-item">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" placeholder="请输入姓名">
                </div>
                <div class="form-item">
                    <div class="form-label">手机号</div>
                    <input type="tel" class="form-input" placeholder="请输入手机号" pattern="[0-9]{11}">
                </div>
                <div class="form-item">
                    <div class="form-label">性别</div>
                    <select class="form-select">
                        <option value="">请选择</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                    </select>
                </div>
                <div class="form-item">
                    <div class="form-label">出生日期</div>
                    <input type="date" class="form-input">
                </div>
                <div class="form-item">
                    <div class="form-label">民族</div>
                    <select class="form-select">
                        <option value="">请选择</option>
                        <option value="han">汉族</option>
                        <option value="other">其他</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <div class="form-item">
                    <div class="form-label">身高(cm)</div>
                    <input type="number" class="form-input" placeholder="请输入身高">
                </div>
                <div class="form-item">
                    <div class="form-label">体重(kg)</div>
                    <input type="number" class="form-input" placeholder="请输入体重">
                </div>
                <div class="form-item">
                    <div class="form-label">BMI</div>
                    <div class="form-input text-gray-500">自动计算</div>
                </div>
            </div>

            <button type="submit" class="btn-save">保存</button>
        </form>
    </div>

    <script>
        document.getElementById('profileForm').addEventListener('submit', function(e) {
            e.preventDefault();
            window.location.href = 'profile.html';
        });

        // BMI自动计算
        const heightInput = document.querySelector('input[placeholder="请输入身高"]');
        const weightInput = document.querySelector('input[placeholder="请输入体重"]');
        const bmiDisplay = document.querySelector('.form-input.text-gray-500');

        function calculateBMI() {
            const height = parseFloat(heightInput.value) / 100; // 转换为米
            const weight = parseFloat(weightInput.value);
            
            if (height && weight) {
                const bmi = (weight / (height * height)).toFixed(1);
                bmiDisplay.textContent = bmi;
            } else {
                bmiDisplay.textContent = '自动计算';
            }
        }

        heightInput.addEventListener('input', calculateBMI);
        weightInput.addEventListener('input', calculateBMI);
    </script>
</body>
</html> 