<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>患者详情 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
    // BMI自动计算
    function calcBMI() {
        const height = parseFloat(document.getElementById('height').value);
        const weight = parseFloat(document.getElementById('weight').value);
        let bmi = '';
        if (height > 0 && weight > 0) {
            bmi = (weight / ((height / 100) * (height / 100))).toFixed(1);
        }
        document.getElementById('bmi').value = bmi;
    }
    // 切换Tab
    function switchTab(tabName) {
        // 隐藏所有内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.add('hidden');
        });
        // 移除所有tab的激活状态
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('bg-blue-50', 'text-blue-600', 'border-blue-600');
            button.classList.add('text-gray-600', 'border-transparent');
        });
        // 显示选中的内容
        document.getElementById(tabName + '-content').classList.remove('hidden');
        // 激活选中的tab
        document.getElementById(tabName + '-tab').classList.remove('text-gray-600', 'border-transparent');
        document.getElementById(tabName + '-tab').classList.add('bg-blue-50', 'text-blue-600', 'border-blue-600');
    }
    // 显示修改患者信息弹窗
    function showEditPatientModal() {
        document.getElementById('editPatientModal').classList.remove('hidden');
    }
    // 隐藏修改患者信息弹窗
    function hideEditPatientModal() {
        document.getElementById('editPatientModal').classList.add('hidden');
    }
    // 显示修改治疗方案弹窗
    function showEditTreatmentModal() {
        document.getElementById('editTreatmentModal').classList.remove('hidden');
    }
    // 隐藏修改治疗方案弹窗
    function hideEditTreatmentModal() {
        document.getElementById('editTreatmentModal').classList.add('hidden');
    }
    // 隐藏提醒信息
    function hideAlert() {
        document.getElementById('alertBox').classList.add('hidden');
    }
    // 显示上转人民医院弹窗
    function transferToHospital() {
        document.getElementById('transferToHospitalModal').classList.remove('hidden');
    }
    // 隐藏上转人民医院弹窗
    function hideTransferToHospitalModal() {
        document.getElementById('transferToHospitalModal').classList.add('hidden');
    }
    </script>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 头部栏 -->
    <div class="fixed top-0 left-0 right-0 h-16 bg-white shadow flex items-center justify-between z-40 px-6 w-full border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <img src="../../images/logo.png" alt="logo" class="h-10 w-auto">
            <div class="flex items-center">
                <span class="text-xl font-bold text-gray-800">居家睡眠远程管理平台</span>
                <span class="ml-2 px-2 py-0.5 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">社区端</span>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <i class="fas fa-user-circle text-2xl text-purple-700"></i>
            <span class="text-base text-gray-700 font-medium">community</span>
        </div>
    </div>
    <!-- 左侧菜单栏 -->
    <div class="sidebar fixed top-16 left-0 bg-gray-50 shadow-lg z-30 border-t border-gray-200" style="width:250px;height:900px;">
        <nav class="mt-4">
            <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-home w-6"></i>
                <span>首页</span>
            </a>
            <a href="patients.html" class="flex items-center px-4 py-3 text-blue-600 bg-blue-50 border-l-4 border-blue-600">
                <i class="fas fa-users w-6"></i>
                <span>患者管理</span>
            </a>
            <a href="questionnaires.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-clipboard-list w-6"></i>
                <span>问卷管理</span>
            </a>
            <a href="reports.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-file-medical w-6"></i>
                <span>报告管理</span>
            </a>
            <a href="messages.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-envelope w-6"></i>
                <span>消息管理</span>
            </a>
            <a href="system.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-cog w-6"></i>
                <span>系统管理</span>
            </a>
        </nav>
    </div>
    <!-- 内容区 -->
    <div class="content p-6 space-y-4" style="margin-top:64px; margin-left:250px;">
        <!-- 业务流程图 -->
        <div class="bg-white rounded-lg shadow p-4 flex items-center justify-between mb-4">
            <div class="flex-1 flex items-center justify-between">
                <div class="flex flex-col items-center">
                    <div class="w-6 h-6 rounded-full" style="background-color: #12e970;"></div>
                    <span class="text-xs mt-1">信息完善</span>
                </div>
                <div class="flex-1 h-1" style="background-color: #12e970;"></div>
                <div class="flex flex-col items-center">
                    <div class="w-6 h-6 rounded-full" style="background-color: #12e970;"></div>
                    <span class="text-xs mt-1">填写问卷</span>
                </div>
                <div class="flex-1 h-1 bg-gradient-to-r" style="background-image: linear-gradient(to right, #12e970, #e5e7eb);"></div>
                <div class="flex flex-col items-center">
                    <div class="w-6 h-6 rounded-full" style="background: linear-gradient(90deg, #12e970 50%, #e5e7eb 50%);"></div>
                    <span class="text-xs mt-1">筛查</span>
                </div>
                <div class="flex-1 h-1 bg-gray-300 mx-1"></div>
                <div class="flex flex-col items-center">
                    <div class="w-6 h-6 rounded-full bg-gray-300"></div>
                    <span class="text-xs mt-1">诊断</span>
                </div>
                <div class="flex-1 h-1 bg-gray-300 mx-1"></div>
                <div class="flex flex-col items-center">
                    <div class="w-6 h-6 rounded-full bg-gray-300"></div>
                    <span class="text-xs mt-1">治疗</span>
                </div>
                <div class="flex-1 h-1 bg-gray-300 mx-1"></div>
                <div class="flex flex-col items-center">
                    <div class="w-6 h-6 rounded-full bg-gray-300"></div>
                    <span class="text-xs mt-1">随访</span>
                </div>
                <div class="flex-1 h-1 bg-gray-300 mx-1"></div>
                <div class="flex flex-col items-center">
                    <div class="w-6 h-6 rounded-full bg-gray-300"></div>
                    <span class="text-xs mt-1">治疗结束</span>
                </div>
            </div>
        </div>

        <!-- 提醒信息 -->
        <div id="alertBox" class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
            <div class="flex justify-between items-start">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-500 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-blue-700 font-medium">提醒：您需要对患者的诊断报告进行评估，及时给出治疗方案</p>
                    </div>
                </div>
                <button onclick="hideAlert()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- 患者基本信息 -->
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-4">
                    <h2 class="text-lg font-bold">患者基本信息</h2>
                    <!-- 上转提示标签 -->
                    <div class="flex items-center bg-yellow-50 px-3 py-1 rounded-full">
                        <i class="fas fa-exclamation-circle text-yellow-500 mr-2"></i>
                        <span class="text-yellow-700 text-sm">如需专业治疗，可上转人民医院</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button onclick="transferToHospital()" class="bg-green-100 text-green-700 px-4 py-2 rounded-lg hover:bg-green-200 transition-colors">
                        <i class="fas fa-hospital mr-2"></i>上转人民医院
                    </button>
                    <button onclick="showEditPatientModal()" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-edit mr-1"></i>修改
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-4 gap-4">
                <div>
                    <span class="text-gray-500">患者编号：</span>
                    <span class="text-gray-800">P20240001</span>
                </div>
                <div>
                    <span class="text-gray-500">姓名：</span>
                    <span class="text-gray-800">张三</span>
                </div>
                <div>
                    <span class="text-gray-500">性别：</span>
                    <span class="text-gray-800">男</span>
                </div>
                <div>
                    <span class="text-gray-500">年龄：</span>
                    <span class="text-gray-800">45岁</span>
                </div>
                <div>
                    <span class="text-gray-500">手机号：</span>
                    <span class="text-gray-800">138****8888</span>
                </div>
                <div>
                    <span class="text-gray-500">民族：</span>
                    <span class="text-gray-800">汉族</span>
                </div>
                <div>
                    <span class="text-gray-500">身高：</span>
                    <span class="text-gray-800">170cm</span>
                </div>
                <div>
                    <span class="text-gray-500">体重：</span>
                    <span class="text-gray-800">70kg</span>
                </div>
                <div>
                    <span class="text-gray-500">BMI：</span>
                    <span class="text-gray-800">24.2</span>
                </div>
                <div>
                    <span class="text-gray-500">来源：</span>
                    <span class="text-gray-800">社区</span>
                </div>
                <div>
                    <span class="text-gray-500">备注：</span>
                    <span class="text-gray-800">需要持续关注</span>
                </div>
            </div>
        </div>

        <!-- 治疗方案 -->
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <h2 class="text-lg font-bold">治疗方案</h2>
                    <span class="ml-4 text-gray-500">下次随访日期：2024-07-01</span>
                </div>
                <button onclick="showEditTreatmentModal()" class="text-blue-600 hover:text-blue-800">

                    <i class="fas fa-edit mr-1"></i>修改
                </button>
            </div>
            <div class="text-gray-800">
                建议使用CPAP治疗，定期复查。患者需要每天使用CPAP设备6-8小时，保持规律的作息时间，避免睡前饮酒和咖啡因摄入。
            </div>
        </div>

        <!-- Tab导航 -->
        <div class="bg-white rounded-lg shadow">
            <div class="border-b border-gray-200">
                <nav class="flex -mb-px">
                    <button id="questionnaire-tab" onclick="switchTab('questionnaire')" class="tab-button py-4 px-6 text-blue-600 border-b-2 border-blue-600 bg-blue-50">
                        问卷信息 <span class="ml-2 bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full text-sm">2</span>
                    </button>
                    <button id="report-tab" onclick="switchTab('report')" class="tab-button py-4 px-6 text-gray-600 border-b-2 border-transparent">
                        诊断报告 <span class="ml-2 bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-sm">2</span>
                    </button>
                    <button id="followup-tab" onclick="switchTab('followup')" class="tab-button py-4 px-6 text-gray-600 border-b-2 border-transparent">
                        随访记录 <span class="ml-2 bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-sm">2</span>
                    </button>
                </nav>
            </div>

            <!-- 问卷信息内容 -->
            <div id="questionnaire-content" class="tab-content p-4">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">问卷名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结论</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">填写时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:underline cursor-pointer">(GSAQ)睡眠状况问卷</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">存在睡眠障碍</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-05-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:underline cursor-pointer">查看</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:underline cursor-pointer">失眠严重指数</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">严重失眠</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-05-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:underline cursor-pointer">查看</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 诊断报告内容 -->
            <div id="report-content" class="tab-content hidden p-4">
                <div class="flex justify-end mb-4">
                    <label class="flex items-center gap-2 cursor-pointer">
                        <input type="file" class="hidden" id="reportUpload" accept=".xls,.xlsx">
                        <span class="bg-green-100 text-green-700 px-3 py-1 rounded hover:bg-green-200 text-sm">
                            <i class="fas fa-upload mr-1"></i>上传报告
                        </span>
                    </label>
                </div>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报告名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结论</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报告日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:underline cursor-pointer">多导睡眠监测报告</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中度阻塞性睡眠呼吸暂停</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-05-12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:underline cursor-pointer">查看</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:underline cursor-pointer">HSAT报告</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一切正常</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-05-18</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:underline cursor-pointer">查看</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 随访记录内容 -->
            <div id="followup-content" class="tab-content hidden p-4">
                <div class="flex justify-end mb-4">
                    <button class="bg-blue-100 text-blue-700 px-3 py-1 rounded hover:bg-blue-200 text-sm">
                        <i class="fas fa-plus mr-1"></i>添加随访
                    </button>
                </div>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">随访医生</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结论</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预约日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">实际日期</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王医生</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">症状改善，建议继续治疗</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-01</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-01</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李医生</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">症状无明显变化</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-16</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 修改患者信息弹窗 -->
    <div id="editPatientModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-2/3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">修改患者信息</h3>
                <button onclick="hideEditPatientModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-gray-700 mb-1">患者编号</label>
                    <input type="text" class="w-full border border-gray-300 px-3 py-1" value="P20240001" readonly>
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">姓名</label>
                    <input type="text" class="w-full border border-gray-300 px-3 py-1" value="张三">
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">手机号</label>
                    <input type="text" class="w-full border border-gray-300 px-3 py-1" value="138****8888">
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">民族</label>
                    <input type="text" class="w-full border border-gray-300 px-3 py-1" value="汉族">
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">出生日期</label>
                    <input type="date" class="w-full border border-gray-300 px-3 py-1" value="1980-01-01">
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">身高(cm)</label>
                    <input type="number" id="height" class="w-full border border-gray-300 px-3 py-1" value="170">
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">体重(kg)</label>
                    <input type="number" id="weight" class="w-full border border-gray-300 px-3 py-1" value="70">
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">BMI</label>
                    <input type="text" id="bmi" class="w-full border border-gray-300 px-3 py-1 bg-gray-100" value="24.2" readonly>
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">来源</label>
                    <input type="text" class="w-full border border-gray-300 px-3 py-1" value="门诊">
                </div>
                <div class="col-span-2">
                    <label class="block text-gray-700 mb-1">备注</label>
                    <textarea class="w-full border border-gray-300 px-3 py-1" rows="2">无</textarea>
                </div>
                <div class="col-span-2 flex justify-end gap-2">
                    <button type="button" onclick="hideEditPatientModal()" class="px-4 py-1 border border-gray-300 rounded hover:bg-gray-100">取消</button>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-1 rounded hover:bg-blue-700">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 修改治疗方案弹窗 -->
    <div id="editTreatmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-2/3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">修改治疗方案</h3>
                <button onclick="hideEditTreatmentModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">患者编号</label>
                        <input type="text" class="w-full border border-gray-300 px-3 py-1 bg-gray-100" value="P20240001" readonly>
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-1">患者姓名</label>
                        <input type="text" class="w-full border border-gray-300 px-3 py-1 bg-gray-100" value="张三" readonly>
                    </div>
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">治疗方案</label>
                    <textarea class="w-full border border-gray-300 px-3 py-1" rows="4">建议使用CPAP治疗，定期复查。患者需要每天使用CPAP设备6-8小时，保持规律的作息时间，避免睡前饮酒和咖啡因摄入。</textarea>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">本次预约随访日期</label>
                        <input type="date" class="w-full border border-gray-300 px-3 py-1" value="2024-06-01">
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-1">实际随访日期</label>
                        <input type="date" class="w-full border border-gray-300 px-3 py-1" value="2024-06-01">
                    </div>
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">随访结论</label>
                    <textarea class="w-full border border-gray-300 px-3 py-1" rows="2">症状改善，建议继续治疗。</textarea>
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">下次随访日期</label>
                    <input type="date" class="w-full border border-gray-300 px-3 py-1" value="2024-07-01">
                </div>
                <div class="flex justify-end gap-2">
                    <button type="button" onclick="hideEditTreatmentModal()" class="px-4 py-1 border border-gray-300 rounded hover:bg-gray-100">取消</button>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-1 rounded hover:bg-blue-700">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 上转人民医院弹窗 -->
    <div id="transferToHospitalModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-1/2">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">上转人民医院</h3>
                <button onclick="hideTransferToHospitalModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">患者编号</label>
                        <input type="text" class="w-full border border-gray-300 px-3 py-1 bg-gray-100" value="P20240001" readonly>
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-1">患者姓名</label>
                        <input type="text" class="w-full border border-gray-300 px-3 py-1 bg-gray-100" value="张三" readonly>
                    </div>
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">目标医院</label>
                    <select class="w-full border border-gray-300 px-3 py-1">
                        <option>请选择医院</option>
                        <option>第一人民医院</option>
                        <option>第二人民医院</option>
                        <option>第三人民医院</option>
                    </select>
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">转诊原因</label>
                    <textarea class="w-full border border-gray-300 px-3 py-1" rows="3" placeholder="请详细说明转诊原因..."></textarea>
                </div>
                <div>
                    <label class="block text-gray-700 mb-1">患者当前状况</label>
                    <textarea class="w-full border border-gray-300 px-3 py-1" rows="3" placeholder="请描述患者当前状况..."></textarea>
                </div>
                <div class="flex justify-end gap-2">
                    <button type="button" onclick="hideTransferToHospitalModal()" class="px-4 py-1 border border-gray-300 rounded hover:bg-gray-100">取消</button>
                    <button type="submit" class="bg-green-600 text-white px-4 py-1 rounded hover:bg-green-700">确认上转</button>
                </div>
            </form>
        </div>
    </div>
</body>
</html> 