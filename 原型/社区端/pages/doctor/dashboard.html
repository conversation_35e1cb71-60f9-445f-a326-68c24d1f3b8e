<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医生端 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar {
            width: 250px;
            min-height: 100vh;
        }
        .content {
            margin-left: 250px;
        }
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 头部栏 -->
    <div class="fixed top-0 left-0 right-0 h-16 bg-white shadow flex items-center justify-between z-40 px-6 w-full border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <img src="../../images/logo.png" alt="logo" class="h-10 w-auto">
            <div class="flex items-center">
                <span class="text-xl font-bold text-gray-800">居家睡眠远程管理平台</span>
                <span class="ml-2 px-2 py-0.5 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">社区端</span>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <i class="fas fa-user-circle text-2xl text-purple-700"></i>
            <span class="text-base text-gray-700 font-medium">community</span>
        </div>
    </div>
    <!-- 左侧菜单栏 -->
    <div class="sidebar fixed top-16 left-0 bg-gray-50 shadow-lg z-30 border-t border-gray-200" style="height:calc(100vh-4rem);">
        <nav class="mt-4">
            <a href="dashboard.html" class="flex items-center px-4 py-3 text-blue-600 bg-blue-50 border-l-4 border-blue-600">
                <i class="fas fa-home w-6"></i>
                <span>首页</span>
            </a>
            <a href="patients.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-users w-6"></i>
                <span>患者管理</span>
            </a>
            <a href="questionnaires.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-clipboard-list w-6"></i>
                <span>问卷管理</span>
            </a>
            <a href="reports.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-file-medical w-6"></i>
                <span>报告管理</span>
            </a>
            <a href="messages.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-envelope w-6"></i>
                <span>消息管理</span>
            </a>
            <a href="system.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-cog w-6"></i>
                <span>系统管理</span>
            </a>
        </nav>
    </div>

    <!-- 右侧内容区 -->
    <div class="content p-8" style="margin-top:64px;">
        <!-- 统计信息 -->
        <div class="grid grid-cols-7 gap-4 mb-8">
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-user-edit"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm text-gray-500">信息完善</h3>
                        <p class="text-2xl font-semibold">12</p>
                    </div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm text-gray-500">填写问卷</h3>
                        <p class="text-2xl font-semibold">8</p>
                    </div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm text-gray-500">筛查中</h3>
                        <p class="text-2xl font-semibold">15</p>
                    </div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-stethoscope"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm text-gray-500">诊断中</h3>
                        <p class="text-2xl font-semibold">6</p>
                    </div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 text-red-600">
                        <i class="fas fa-pills"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm text-gray-500">治疗中</h3>
                        <p class="text-2xl font-semibold">20</p>
                    </div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-indigo-100 text-indigo-600">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm text-gray-500">随访中</h3>
                        <p class="text-2xl font-semibold">10</p>
                    </div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-gray-100 text-gray-600">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm text-gray-500">治疗结束</h3>
                        <p class="text-2xl font-semibold">25</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体区域：日历、最新消息、待随访患者 -->
        <div class="grid grid-cols-3 gap-8">
            <!-- 左侧：待办事项日历 + 已治疗患者趋势图 -->
            <div class="col-span-2 flex flex-col gap-8">
                <!-- 待办事项日历，高度与最新消息一致 -->
                <div class="bg-white p-6 rounded-lg shadow flex-1 min-h-[340px] flex flex-col justify-between">
                    <div>
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-lg font-semibold">待办事项日历</h2>
                            <span class="text-gray-400 text-sm">2025年6月</span>
                        </div>
                        <!-- 日历内容 -->
                        <div class="grid grid-cols-7 gap-2 text-center text-sm">
                            <div class="text-gray-400">日</div>
                            <div class="text-gray-400">一</div>
                            <div class="text-gray-400">二</div>
                            <div class="text-gray-400">三</div>
                            <div class="text-gray-400">四</div>
                            <div class="text-gray-400">五</div>
                            <div class="text-gray-400">六</div>
                            <div></div><div></div><div></div><div></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">1</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">2</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full bg-blue-100 text-blue-600 font-bold">3</span><span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span></div>
                            <div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">4</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">5</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">6</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">7</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">8</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">9</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full bg-blue-100 text-blue-600 font-bold">10</span><span class="absolute top-0 right-0 w-2 h-2 bg-yellow-400 rounded-full"></span></div>
                            <div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">11</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">12</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">13</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">14</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">15</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">16</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">17</span></div>
                            <div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">18</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">19</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">20</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">21</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">22</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">23</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">24</span></div>
                            <div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">25</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">26</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">27</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">28</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">29</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">30</span></div><div class="relative"><span class="inline-block w-8 h-8 leading-8 rounded-full">31</span></div>
                        </div>
                    </div>
                    <!-- 当天待办事项 -->
                    <div class="mt-4">
                        <h3 class="text-sm font-semibold mb-2 text-blue-700">今日待办事项</h3>
                        <ul class="list-disc pl-5 text-gray-700 text-sm">
                            <li>09:00 远程会诊 - 患者：张三</li>
                            <li>14:00 复诊随访 - 患者：李四</li>
                        </ul>
                    </div>
                </div>
                <!-- 已治疗患者趋势图，高度与待随访患者一致 -->
                <div class="bg-white p-6 rounded-lg shadow flex-1 min-h-[220px] flex flex-col justify-center">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold">已治疗患者趋势</h2>
                    </div>
                    <div class="h-40">
                        <canvas id="treatedTrendChart" style="width:100%;height:100%"></canvas>
                    </div>
                </div>
            </div>
            <!-- 右侧：最新消息 + 待随访患者 -->
            <div class="flex flex-col gap-8">
                <!-- 最新消息 -->
                <div class="bg-white p-6 rounded-lg shadow flex-1 min-h-[340px]">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold">最新消息</h2>
                        <a href="messages.html" class="text-blue-600 text-sm hover:underline">查看更多</a>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="p-2 rounded-full bg-blue-100 text-blue-600">
                                    <i class="fas fa-bell"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900">新患者注册</p>
                                <p class="text-sm text-gray-500">张三已完成注册</p>
                                <p class="text-xs text-gray-400">10分钟前</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="p-2 rounded-full bg-yellow-100 text-yellow-600">
                                    <i class="fas fa-exclamation-circle"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900">随访提醒</p>
                                <p class="text-sm text-gray-500">李四需要进行第3次随访</p>
                                <p class="text-xs text-gray-400">30分钟前</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="p-2 rounded-full bg-green-100 text-green-600">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900">问卷完成通知</p>
                                <p class="text-sm text-gray-500">王五已完成睡眠质量评估问卷</p>
                                <p class="text-xs text-gray-400">1小时前</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="p-2 rounded-full bg-purple-100 text-purple-600">
                                    <i class="fas fa-user-md"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900">系统通知</p>
                                <p class="text-sm text-gray-500">平台将于今晚维护</p>
                                <p class="text-xs text-gray-400">2小时前</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="p-2 rounded-full bg-red-100 text-red-600">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900">异常警告</p>
                                <p class="text-sm text-gray-500">患者赵六血压异常</p>
                                <p class="text-xs text-gray-400">3小时前</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 待随访患者 -->
                <div class="bg-white p-6 rounded-lg shadow flex-1 min-h-[220px] flex flex-col justify-center">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold">上转人民医院患者</h2>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-800 font-medium">张三</span>
                            <span class="text-gray-500 text-sm">2025-06-10</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-800 font-medium">李四</span>
                            <span class="text-gray-500 text-sm">2025-06-12</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-800 font-medium">王五</span>
                            <span class="text-gray-500 text-sm">2025-06-15</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-800 font-medium">赵六</span>
                            <span class="text-gray-500 text-sm">2025-06-18</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-800 font-medium">孙七</span>
                            <span class="text-gray-500 text-sm">2025-06-20</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        window.onload = function() {
            // 新增已治疗患者趋势图
            const treatedTrendCtx = document.getElementById('treatedTrendChart').getContext('2d');
            new Chart(treatedTrendCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '已治疗患者',
                        data: [20, 35, 40, 55, 60, 75],
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16,185,129,0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#10B981',
                        pointRadius: 4,
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: { display: false }
                        },
                        y: {
                            beginAtZero: true,
                            grid: { color: '#e5e7eb' }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 