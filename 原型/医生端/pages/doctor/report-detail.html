<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告详情 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            width: 250px;
            min-height: 100vh;
        }
        .content {
            margin-left: 250px;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 50;
        }
        .modal-content {
            position: relative;
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            width: 50%;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 头部栏 -->
    <div class="fixed top-0 left-0 right-0 h-16 bg-white shadow flex items-center justify-between z-40 px-6 w-full border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <img src="../../images/logo.png" alt="logo" class="h-10 w-auto">
            <div class="flex items-center">
                <span class="text-xl font-bold text-gray-800">居家睡眠远程管理平台</span>
                <span class="ml-2 px-2 py-0.5 bg-green-100 text-green-600 rounded-full text-sm font-medium">医生端</span>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <i class="fas fa-user-circle text-2xl text-green-700"></i>
            <span class="text-base text-gray-700 font-medium">doctor</span>
        </div>
    </div>
    <!-- 左侧菜单栏 -->
    <div class="sidebar fixed top-16 left-0 bg-gray-50 shadow-lg z-30 border-t border-gray-200" style="width:250px;height:900px;">
        <nav class="mt-4">
            <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-home w-6"></i>
                <span>首页</span>
            </a>
            <a href="patients.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-users w-6"></i>
                <span>患者管理</span>
            </a>
            <a href="questionnaires.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-clipboard-list w-6"></i>
                <span>问卷管理</span>
            </a>
            <a href="reports.html" class="flex items-center px-4 py-3 text-blue-600 bg-blue-50 border-l-4 border-blue-600">
                <i class="fas fa-file-medical w-6"></i>
                <span>报告管理</span>
            </a>
            <a href="messages.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-envelope w-6"></i>
                <span>消息管理</span>
            </a>
            <a href="system.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-cog w-6"></i>
                <span>系统管理</span>
            </a>
        </nav>
    </div>
    <!-- 右侧内容区 -->
    <div class="content p-6 space-y-4" style="margin-top:64px; margin-left:250px;">

        <!-- 患者基本信息 -->
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-bold">患者基本信息</h2>
            </div>
            <div class="grid grid-cols-4 gap-4">
                <div>
                    <span class="text-gray-500">患者编号：</span>
                    <span class="text-gray-800">P20240001</span>
                </div>
                <div>
                    <span class="text-gray-500">姓名：</span>
                    <span class="text-gray-800">张三</span>
                </div>
                <div>
                    <span class="text-gray-500">性别：</span>
                    <span class="text-gray-800">男</span>
                </div>
                <div>
                    <span class="text-gray-500">年龄：</span>
                    <span class="text-gray-800">45岁</span>
                </div>
                <div>
                    <span class="text-gray-500">手机号：</span>
                    <span class="text-gray-800">138****8888</span>
                </div>
                <div>
                    <span class="text-gray-500">民族：</span>
                    <span class="text-gray-800">汉族</span>
                </div>
                <div>
                    <span class="text-gray-500">身高：</span>
                    <span class="text-gray-800">170cm</span>
                </div>
                <div>
                    <span class="text-gray-500">体重：</span>
                    <span class="text-gray-800">70kg</span>
                </div>
                <div>
                    <span class="text-gray-500">BMI：</span>
                    <span class="text-gray-800">24.2</span>
                </div>
                <div>
                    <span class="text-gray-500">来源：</span>
                    <span class="text-gray-800">门诊</span>
                </div>
                <div>
                    <span class="text-gray-500">备注：</span>
                    <span class="text-gray-800">需要持续关注</span>
                </div>
            </div>
        </div>

        <!-- 报告内容 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-800">报告名称：不宁腿制动试验报告</h2>
                <button onclick="openModal()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm font-medium">
                    修改报告信息
                </button>
            </div>
            <div class="p-6">
                <!-- 监测数据 -->
                <div class="mb-8">
                    <h3 class="text-base font-medium text-gray-900 mb-4">报告内容</h3>
                    <div class="grid grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">试验日期：2025/05/01</label>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">疼痛评分-0分钟：1</label>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">疼痛评分-10分钟：2</label>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">疼痛评分-20分钟：3</label>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">疼痛评分-30分钟：3</label>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">疼痛评分-40分钟：3</label>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">疼痛评分-50分钟：3</label>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">疼痛评分-60分钟：3</label>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-base font-medium text-gray-900 mb-4">诊断结果</h3>
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700">患者第一次制动试验后睡着，终止试验</h4>
                        </div>
                    </div>
                </div>

                <!-- 睡眠质量分析
                <div class="mb-8">
                    <h3 class="text-base font-medium text-gray-900 mb-4">睡眠质量分析</h3>
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700">睡眠结构</h4>
                            <p class="mt-1 text-sm text-gray-600">患者的睡眠结构基本正常，深睡眠、浅睡眠和快速眼动睡眠的比例符合健康标准。</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700">睡眠效率</h4>
                            <p class="mt-1 text-sm text-gray-600">睡眠效率达到92%，属于良好水平。入睡时间适中，觉醒次数较少。</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700">睡眠质量</h4>
                            <p class="mt-1 text-sm text-gray-600">总体睡眠质量良好，无明显睡眠障碍表现。</p>
                        </div>
                    </div>
                </div>
            -->

                <!-- 医生建议
                <div>
                    <h3 class="text-base font-medium text-gray-900 mb-4">医生建议</h3>
                    <div class="space-y-4">
                        <p class="text-sm text-gray-600">1. 继续保持当前的作息时间，建议在22:30前入睡。</p>
                        <p class="text-sm text-gray-600">2. 适当增加运动量，建议每天进行30分钟中等强度运动。</p>
                        <p class="text-sm text-gray-600">3. 保持良好的睡眠环境，保持室内温度在18-22℃之间。</p>
                        <p class="text-sm text-gray-600">4. 睡前避免使用电子设备，可以尝试阅读或听轻音乐。</p>
                    </div>
                </div>
                 -->
            </div>
        </div>
    </div>

    <!-- 修改报告信息弹窗 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">修改报告信息</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">报告类型</label>
                    <select class="mt-1 block w-full pl-3 pr-10 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white">
                        <option value="sleep">睡眠监测报告</option>
                        <option value="blood">血氧监测报告</option>
                        <option value="heart">心率监测报告</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">报告结论</label>
                    <textarea class="mt-1 block w-full pl-3 pr-3 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white" rows="3"></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">医生建议</label>
                    <textarea class="mt-1 block w-full pl-3 pr-3 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white" rows="4"></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 focus:outline-none text-sm font-medium">
                        取消
                    </button>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm font-medium">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openModal() {
            document.getElementById('modal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 点击弹窗外部关闭弹窗
        window.onclick = function(event) {
            if (event.target == document.getElementById('modal')) {
                closeModal();
            }
        }
    </script>
</body>
</html> 