<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问卷管理 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            width: 250px;
            min-height: 100vh;
        }
        .content {
            margin-left: 250px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 头部栏 -->
    <div class="fixed top-0 left-0 right-0 h-16 bg-white shadow flex items-center justify-between z-40 px-6 w-full border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <img src="../../images/logo.png" alt="logo" class="h-10 w-auto">
            <div class="flex items-center">
                <span class="text-xl font-bold text-gray-800">居家睡眠远程管理平台</span>
                <span class="ml-2 px-2 py-0.5 bg-green-100 text-green-600 rounded-full text-sm font-medium">医生端</span>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <i class="fas fa-user-circle text-2xl text-green-700"></i>
            <span class="text-base text-gray-700 font-medium">doctor</span>
        </div>
    </div>
    <!-- 左侧菜单栏 -->
    <div class="sidebar fixed top-16 left-0 bg-gray-50 shadow-lg z-30 border-t border-gray-200" style="height:calc(100vh-4rem);">
        <nav class="mt-4">
            <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-home w-6"></i>
                <span>首页</span>
            </a>
            <a href="patients.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-users w-6"></i>
                <span>患者管理</span>
            </a>
            <a href="questionnaires.html" class="flex items-center px-4 py-3 text-blue-600 bg-blue-50 border-l-4 border-blue-600">
                <i class="fas fa-clipboard-list w-6"></i>
                <span>问卷管理</span>
            </a>
            <a href="reports.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-file-medical w-6"></i>
                <span>报告管理</span>
            </a>
            <a href="messages.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-envelope w-6"></i>
                <span>消息管理</span>
            </a>
            <a href="system.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-cog w-6"></i>
                <span>系统管理</span>
            </a>
        </nav>
    </div>
    <!-- 右侧内容区 -->
    <div class="content p-8" style="margin-top:64px;">
        <!-- 搜索区域 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">问卷搜索</h2>
            <form>
                <div class="flex flex-wrap items-center mb-3">
                    <div class="flex items-center mr-6 mb-2">
                        <span class="text-sm text-gray-700 w-20">患者编号：</span>
                        <input type="text" placeholder="请输入患者编号" class="w-60 pl-3 pr-3 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white transition-all duration-200" />
                    </div>
                    <div class="flex items-center mr-6 mb-2">
                        <span class="text-sm text-gray-700 w-20">患者姓名：</span>
                        <input type="text" placeholder="请输入患者姓名" class="w-60 pl-3 pr-3 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white transition-all duration-200" />
                    </div>
                    <div class="flex-1 flex justify-end mb-2">
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm font-medium mr-2">查询</button>
                        <button type="reset" class="bg-gray-100 text-gray-700 px-6 py-2 rounded hover:bg-gray-200 focus:outline-none text-sm font-medium">重置</button>
                        <div class="flex gap-2 ml-2">
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="file" class="hidden" id="questionnaireUpload" accept=".xls,.xlsx">
                                <span class="bg-green-100 text-green-700 px-4 py-2 rounded hover:bg-green-200 text-sm">
                                    <i class="fas fa-upload mr-1"></i>批量上传
                                </span>
                            </label>
                            <a href="#" class="bg-blue-100 text-blue-700 px-4 py-2 rounded hover:bg-blue-200 text-sm">
                                <i class="fas fa-download mr-1"></i>下载模板
                            </a>
                        </div>
                    </div>
                </div>
                <div class="flex flex-wrap items-center">
                    <div class="flex items-center mr-6 mb-2">
                        <span class="text-sm text-gray-700 w-20">问卷类型：</span>
                        <select class="w-60 pl-3 pr-10 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white transition-all duration-200">
                            <option value="">请选择问卷类型</option>
                            <option value="sleep">睡眠质量评估</option>
                            <option value="anxiety">焦虑评估</option>
                            <option value="depression">抑郁评估</option>
                        </select>
                    </div>
                    <div class="flex items-center mb-2">
                        <span class="text-sm text-gray-700 w-20">填写时间：</span>
                        <input type="date" class="w-60 pl-3 pr-3 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white transition-all duration-200" placeholder="开始日期" />
                        <span class="mx-2 text-gray-500">至</span>
                        <input type="date" class="w-60 pl-3 pr-3 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white transition-all duration-200" placeholder="结束日期" />
                    </div>
                </div>
            </form>
        </div>

        <!-- 问卷列表 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">患者编号</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">患者姓名</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">问卷类型</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">问卷结论</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">填写时间</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- 示例数据 -->
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240001</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张三</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">(GSAQ)睡眠状况问卷</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">轻度睡眠障碍</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2025-03-15</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="questionnaire-detail.html?id=Q20240001" class="hover:text-blue-900">查看</a></td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240002</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李四</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">腰失眠严重指数</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">严重失眠</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2025-03-14</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="questionnaire-detail.html?id=Q20240002" class="hover:text-blue-900">查看</a></td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </a>
                    <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </a>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">上一页</span>
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                1
                            </a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">下一页</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 