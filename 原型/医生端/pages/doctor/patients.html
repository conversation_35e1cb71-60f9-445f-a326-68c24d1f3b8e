<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>患者管理 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            width: 250px;
            min-height: 100vh;
        }
        .content {
            margin-left: 250px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 头部栏 -->
    <div class="fixed top-0 left-0 right-0 h-16 bg-white shadow flex items-center justify-between z-40 px-6 w-full border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <img src="../../images/logo.png" alt="logo" class="h-10 w-auto">
            <div class="flex items-center">
                <span class="text-xl font-bold text-gray-800">居家睡眠远程管理平台</span>
                <span class="ml-2 px-2 py-0.5 bg-green-100 text-green-600 rounded-full text-sm font-medium">医生端</span>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <i class="fas fa-user-circle text-2xl text-green-700"></i>
            <span class="text-base text-gray-700 font-medium">doctor</span>
        </div>
    </div>
    <!-- 左侧菜单栏 -->
    <div class="sidebar fixed top-16 left-0 bg-gray-50 shadow-lg z-30 border-t border-gray-200" style="height:calc(100vh-4rem);">
        <nav class="mt-4">
            <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-home w-6"></i>
                <span>首页</span>
            </a>
            <a href="patients.html" class="flex items-center px-4 py-3 text-blue-600 bg-blue-50 border-l-4 border-blue-600">
                <i class="fas fa-users w-6"></i>
                <span>患者管理</span>
            </a>
            <a href="questionnaires.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-clipboard-list w-6"></i>
                <span>问卷管理</span>
            </a>
            <a href="reports.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-file-medical w-6"></i>
                <span>报告管理</span>
            </a>
            <a href="messages.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-envelope w-6"></i>
                <span>消息管理</span>
            </a>
            <a href="system.html" class="flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100">
                <i class="fas fa-cog w-6"></i>
                <span>系统管理</span>
            </a>
        </nav>
    </div>
    <!-- 右侧内容区 -->
    <div class="content p-8" style="margin-top:64px;">
        <!-- 搜索区域 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">患者搜索</h2>
            <form class="flex flex-wrap items-center gap-4">
                <div class="w-48">
                    <input type="text" placeholder="患者编号" class="block w-full pl-3 pr-3 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white transition-all duration-200" />
                </div>
                <div class="w-48">
                    <input type="text" placeholder="患者姓名" class="block w-full pl-3 pr-3 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white transition-all duration-200" />
                </div>
                <div class="w-56">
                    <input type="text" placeholder="患者手机号" class="block w-full pl-3 pr-3 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white transition-all duration-200" />
                </div>
                <div class="w-56">
                    <select class="block w-full pl-3 pr-10 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white transition-all duration-200">
                        <option value="">患者来源</option>
                        <option value="info">全部</option>
                        <option value="questionnaire">个人</option>
                        <option value="screening">社区</option>
                        <option value="diagnosis">医院</option>
                    </select>
                </div>
                <!--
                <div class="w-56">
                    <select class="block w-full pl-3 pr-10 py-2 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500 bg-white transition-all duration-200">
                        <option value="">患者状态</option>
                        <option value="info">信息完善中</option>
                        <option value="questionnaire">填写问卷中</option>
                        <option value="screening">筛查中</option>
                        <option value="diagnosis">诊断中</option>
                        <option value="treatment">治疗中</option>
                        <option value="followup">随访中</option>
                        <option value="completed">治疗结束</option>
                    </select>
                </div>
                -->
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm font-medium">查询</button>
                <button type="reset" class="bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 focus:outline-none text-sm font-medium">重置</button>
                <div class="flex gap-2">
                    <label class="flex items-center gap-2 cursor-pointer">
                        <input type="file" class="hidden" id="patientUpload" accept=".xls,.xlsx">
                        <span class="bg-green-100 text-green-700 px-4 py-2 rounded hover:bg-green-200 text-sm">
                            <i class="fas fa-upload mr-1"></i>批量上传
                        </span>
                    </label>
                    <a href="#" class="bg-blue-100 text-blue-700 px-4 py-2 rounded hover:bg-blue-200 text-sm">
                        <i class="fas fa-download mr-1"></i>下载模板
                    </a>
                </div>
            </form>
        </div>

        <!-- 患者列表 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">患者编号</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">患者姓名</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">患者来源</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">信息完善</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">填写问卷</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">筛查</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">诊断</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">治疗</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">随访</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">治疗结束</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- 10条患者数据示例 -->
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240001</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张三</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">个人</td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background: linear-gradient(90deg, #12e970 50%, #e5e7eb 50%);"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="patient-detail.html?id=P20240001" class="hover:text-blue-900">查看</a></td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240002</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李四</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">个人</td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background: linear-gradient(90deg, #12e970 50%, #e5e7eb 50%);"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="patient-detail.html?id=P20240002" class="hover:text-blue-900">查看</a></td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240003</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王五</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">社区</td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background: linear-gradient(90deg, #12e970 50%, #e5e7eb 50%);"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="patient-detail.html?id=P20240003" class="hover:text-blue-900">查看</a></td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240004</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">赵六</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">社区</td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background: linear-gradient(90deg, #12e970 50%, #e5e7eb 50%);"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="patient-detail.html?id=P20240004" class="hover:text-blue-900">查看</a></td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240005</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">孙七</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医院</td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="patient-detail.html?id=P20240005" class="hover:text-blue-900">查看</a></td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240006</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">周八</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医院</td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="patient-detail.html?id=P20240006" class="hover:text-blue-900">查看</a></td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">7</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240007</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">吴九</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医院</td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="patient-detail.html?id=P20240007" class="hover:text-blue-900">查看</a></td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240008</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">郑十</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医院</td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background: linear-gradient(90deg, #12e970 50%, #e5e7eb 50%);"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="patient-detail.html?id=P20240008" class="hover:text-blue-900">查看</a></td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">9</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240009</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钱十一</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医院</td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background: linear-gradient(90deg, #12e970 50%, #e5e7eb 50%);"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full bg-gray-300"></span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="patient-detail.html?id=P20240009" class="hover:text-blue-900">查看</a></td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">P20240010</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">孙十二</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医院</td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 text-center"><span class="inline-block w-4 h-4 rounded-full" style="background-color: #12e970;"></span></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600"><a href="patient-detail.html?id=P20240010" class="hover:text-blue-900">查看</a></td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </a>
                    <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </a>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">97</span> 条记录
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">上一页</span>
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                1
                            </a>
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                2
                            </a>
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                3
                            </a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">下一页</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入弹窗 -->
    <div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-1/2">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">批量导入数据</h3>
                <button onclick="hideImportModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium mb-2">导入说明：</h4>
                    <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
                        <li>请下载Excel模板，按照模板格式填写数据</li>
                        <li>支持.xlsx和.xls格式的文件</li>
                        <li>文件大小不超过10MB</li>
                        <li>必填字段：姓名、性别、年龄、手机号</li>
                    </ul>
                </div>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input type="file" class="hidden" id="fileInput" accept=".xlsx,.xls">
                    <label for="fileInput" class="cursor-pointer">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                        <p class="text-gray-600">点击或拖拽文件到此处上传</p>
                        <p class="text-sm text-gray-500 mt-1">支持.xlsx和.xls格式</p>
                    </label>
                </div>
                <div class="flex justify-end gap-2">
                    <button onclick="hideImportModal()" class="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100">取消</button>
                    <button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">开始导入</button>
                </div>
            </div>
        </div>
    </div>

    <script>
    // 处理文件上传
    function handleFileUpload(input, type) {
        const file = input.files[0];
        if (file) {
            // 显示导入弹窗
            document.getElementById('importModal').classList.remove('hidden');
            // 这里可以添加文件预览或验证逻辑
        }
    }
    
    // 隐藏导入弹窗
    function hideImportModal() {
        document.getElementById('importModal').classList.add('hidden');
    }
    </script>
</body>
</html> 