-- 创建数据库
CREATE DATABASE IF NOT EXISTS sleep_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE sleep_management;

-- 用户表
CREATE TABLE sys_user (
    id BIGINT NOT NULL PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(200) COMMENT '头像',
    user_type TINYINT DEFAULT 1 COMMENT '用户类型（1医生 2社区管理员 3系统管理员）',
    status TINYINT DEFAULT 1 COMMENT '状态（0禁用 1正常）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    INDEX idx_username (username),
    INDEX idx_status (status),
    INDEX idx_user_type (user_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
CREATE TABLE sys_role (
    id BIGINT NOT NULL PRIMARY KEY COMMENT '角色ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    role_key VARCHAR(50) NOT NULL UNIQUE COMMENT '角色权限字符串',
    description VARCHAR(200) COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态（0禁用 1正常）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    INDEX idx_role_key (role_key),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 权限表
CREATE TABLE sys_permission (
    id BIGINT NOT NULL PRIMARY KEY COMMENT '权限ID',
    permission_name VARCHAR(50) NOT NULL COMMENT '权限名称',
    permission_key VARCHAR(100) NOT NULL COMMENT '权限标识',
    resource_type VARCHAR(20) DEFAULT 'menu' COMMENT '资源类型（menu菜单 button按钮）',
    url VARCHAR(200) COMMENT '请求地址',
    component VARCHAR(200) COMMENT '组件路径',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    sort_order INT DEFAULT 0 COMMENT '显示顺序',
    icon VARCHAR(50) COMMENT '图标',
    status TINYINT DEFAULT 1 COMMENT '状态（0禁用 1正常）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 用户角色关联表
CREATE TABLE sys_user_role (
    id BIGINT NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    UNIQUE KEY uk_user_role (user_id, role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE sys_role_permission (
    id BIGINT NOT NULL PRIMARY KEY COMMENT '主键ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id),
    UNIQUE KEY uk_role_permission (role_id, permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 操作日志表
CREATE TABLE sys_operation_log (
    id BIGINT NOT NULL PRIMARY KEY COMMENT '日志主键',
    operation_name VARCHAR(50) COMMENT '操作模块',
    operation_type VARCHAR(20) COMMENT '操作类型',
    method VARCHAR(100) COMMENT '方法名称',
    request_method VARCHAR(10) COMMENT '请求方式',
    operator_type VARCHAR(20) COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
    operator_name VARCHAR(50) COMMENT '操作人员',
    dept_name VARCHAR(50) COMMENT '部门名称',
    request_url VARCHAR(255) COMMENT '请求URL',
    request_ip VARCHAR(128) COMMENT '主机地址',
    request_location VARCHAR(255) COMMENT '操作地点',
    request_param TEXT COMMENT '请求参数',
    response_result TEXT COMMENT '返回参数',
    status TINYINT DEFAULT 1 COMMENT '操作状态（0异常 1正常）',
    error_msg TEXT COMMENT '错误消息',
    operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_operation_name (operation_name),
    INDEX idx_operator_name (operator_name),
    INDEX idx_operation_time (operation_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志记录';

-- 患者信息表
CREATE TABLE patient_info (
    id BIGINT NOT NULL PRIMARY KEY COMMENT '患者ID',
    patient_code VARCHAR(50) NOT NULL UNIQUE COMMENT '患者编号',
    name VARCHAR(50) NOT NULL COMMENT '患者姓名',
    gender TINYINT COMMENT '性别（0女 1男）',
    birth_date DATE COMMENT '出生日期',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    address VARCHAR(200) COMMENT '地址',
    emergency_contact VARCHAR(50) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
    status TINYINT DEFAULT 1 COMMENT '状态（0禁用 1正常）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
    INDEX idx_patient_code (patient_code),
    INDEX idx_name (name),
    INDEX idx_phone (phone)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者信息表';

-- 插入初始数据
-- 插入默认管理员用户（密码：admin123）
INSERT INTO sys_user (id, username, password, real_name, email, user_type, status) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWbfXCNOaWpDDvNVjpBBUuUdwXaWqOdd4sgO1S6', '系统管理员', '<EMAIL>', 3, 1),
(2, 'doctor', '$2a$10$7JB720yubVSOfvVWbfXCNOaWpDDvNVjpBBUuUdwXaWqOdd4sgO1S6', '张医生', '<EMAIL>', 1, 1),
(3, 'community', '$2a$10$7JB720yubVSOfvVWbfXCNOaWpDDvNVjpBBUuUdwXaWqOdd4sgO1S6', '社区管理员', '<EMAIL>', 2, 1);

-- 插入角色数据
INSERT INTO sys_role (id, role_name, role_key, description, status) VALUES
(1, '系统管理员', 'ROLE_ADMIN', '系统管理员角色', 1),
(2, '医生', 'ROLE_DOCTOR', '医生角色', 1),
(3, '社区管理员', 'ROLE_COMMUNITY', '社区管理员角色', 1);

-- 插入权限数据
INSERT INTO sys_permission (id, permission_name, permission_key, resource_type, url, parent_id, sort_order, icon, status) VALUES
-- 系统管理
(1, '系统管理', 'system', 'menu', '/system', 0, 1, 'fas fa-cog', 1),
(2, '用户管理', 'system:user', 'menu', '/system/user', 1, 1, 'fas fa-users', 1),
(3, '角色管理', 'system:role', 'menu', '/system/role', 1, 2, 'fas fa-user-tag', 1),
(4, '权限管理', 'system:permission', 'menu', '/system/permission', 1, 3, 'fas fa-key', 1),
(5, '操作日志', 'system:log', 'menu', '/system/log', 1, 4, 'fas fa-file-alt', 1),

-- 用户管理按钮权限
(6, '用户查询', 'system:user:query', 'button', '', 2, 1, '', 1),
(7, '用户新增', 'system:user:add', 'button', '', 2, 2, '', 1),
(8, '用户修改', 'system:user:edit', 'button', '', 2, 3, '', 1),
(9, '用户删除', 'system:user:remove', 'button', '', 2, 4, '', 1),

-- 角色管理按钮权限
(10, '角色查询', 'system:role:query', 'button', '', 3, 1, '', 1),
(11, '角色新增', 'system:role:add', 'button', '', 3, 2, '', 1),
(12, '角色修改', 'system:role:edit', 'button', '', 3, 3, '', 1),
(13, '角色删除', 'system:role:remove', 'button', '', 3, 4, '', 1),

-- 权限管理按钮权限
(14, '权限查询', 'system:permission:query', 'button', '', 4, 1, '', 1),
(15, '权限新增', 'system:permission:add', 'button', '', 4, 2, '', 1),
(16, '权限修改', 'system:permission:edit', 'button', '', 4, 3, '', 1),
(17, '权限删除', 'system:permission:remove', 'button', '', 4, 4, '', 1),

-- 日志查询权限
(18, '日志查询', 'system:log:query', 'button', '', 5, 1, '', 1);

-- 插入用户角色关联数据
INSERT INTO sys_user_role (id, user_id, role_id) VALUES
(1, 1, 1),
(2, 2, 2),
(3, 3, 3);

-- 插入角色权限关联数据（系统管理员拥有所有权限）
INSERT INTO sys_role_permission (id, role_id, permission_id) VALUES
(1, 1, 1), (2, 1, 2), (3, 1, 3), (4, 1, 4), (5, 1, 5),
(6, 1, 6), (7, 1, 7), (8, 1, 8), (9, 1, 9),
(10, 1, 10), (11, 1, 11), (12, 1, 12), (13, 1, 13),
(14, 1, 14), (15, 1, 15), (16, 1, 16), (17, 1, 17),
(18, 1, 18);

-- 医生角色权限（基础查询权限）
INSERT INTO sys_role_permission (id, role_id, permission_id) VALUES
(19, 2, 1), (20, 2, 2), (21, 2, 5), (22, 2, 6), (23, 2, 18);

-- 社区管理员角色权限（用户管理权限）
INSERT INTO sys_role_permission (id, role_id, permission_id) VALUES
(24, 3, 1), (25, 3, 2), (26, 3, 5), (27, 3, 6), (28, 3, 7), (29, 3, 8), (30, 3, 18);
