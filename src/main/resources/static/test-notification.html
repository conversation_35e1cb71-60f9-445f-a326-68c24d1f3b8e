<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Test</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="/js/notification.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow p-6">
        <h1 class="text-2xl font-bold mb-6">Notification System Test</h1>
        
        <div class="space-y-4">
            <button onclick="testSuccess()" class="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                Test Success
            </button>
            
            <button onclick="testError()" class="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                Test Error
            </button>
            
            <button onclick="testWarning()" class="w-full bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                Test Warning
            </button>
            
            <button onclick="testInfo()" class="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Test Info
            </button>
        </div>
    </div>

    <script>
        function testSuccess() {
            if (window.notification) {
                window.notification.success('This is a success message!');
            } else {
                window.showNotification('This is a success message!', 'success');
            }
        }

        function testError() {
            if (window.notification) {
                window.notification.error('This is an error message!');
            } else {
                window.showNotification('This is an error message!', 'error');
            }
        }

        function testWarning() {
            if (window.notification) {
                window.notification.warning('This is a warning message!');
            } else {
                window.showNotification('This is a warning message!', 'warning');
            }
        }

        function testInfo() {
            if (window.notification) {
                window.notification.info('This is an info message!');
            } else {
                window.showNotification('This is an info message!', 'info');
            }
        }
    </script>
</body>
</html>
