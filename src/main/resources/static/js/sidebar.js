// 统一左侧菜单栏组件
window.SidebarComponent = {
    currentPage: 'dashboard', // 当前页面标识
    userRole: 'guest', // 用户角色

    // 初始化侧边栏
    init(currentPage = 'dashboard') {
        this.currentPage = currentPage;
        this.createSidebar();
        this.updateUserRole();
        this.generateMenu();
        this.bindEvents();
    },

    // 创建侧边栏HTML结构
    createSidebar() {
        const sidebarHTML = `
            <div id="sidebar" class="sidebar fixed top-16 left-0 bg-gray-50 shadow-lg z-30 border-t border-gray-200" style="width: 250px; height: calc(100vh - 64px);">
                <nav class="mt-4" id="sidebarNav">
                    <!-- 菜单将通过JavaScript动态生成 -->
                </nav>
            </div>
        `;

        // 如果页面已有侧边栏，先移除
        const existingSidebar = document.getElementById('sidebar');
        if (existingSidebar) {
            existingSidebar.remove();
        }

        // 添加新的侧边栏（在头部栏之后）
        const headerElement = document.querySelector('.fixed.top-0');
        if (headerElement) {
            headerElement.insertAdjacentHTML('afterend', sidebarHTML);
        } else {
            document.body.insertAdjacentHTML('afterbegin', sidebarHTML);
        }

        // 确保主内容区有正确的左边距
        this.adjustMainContent();
    },

    // 调整主内容区域的样式
    adjustMainContent() {
        // 查找主内容区域并设置左边距
        const contentElements = document.querySelectorAll('.content, [class*="content"], main');
        contentElements.forEach(element => {
            // 检查元素是否已经有margin-left样式
            const currentMarginLeft = element.style.marginLeft;
            if (!currentMarginLeft || currentMarginLeft === '0px' || currentMarginLeft === '') {
                element.style.marginLeft = '250px';
            }
        });

        // 创建全局样式规则，确保所有内容区域都有正确的左边距
        const existingStyle = document.getElementById('sidebar-layout-style');
        if (!existingStyle) {
            const style = document.createElement('style');
            style.id = 'sidebar-layout-style';
            style.textContent = `
                /* 侧边栏布局样式 - 最高优先级，简化选择器 */
                .content {
                    margin-left: 250px !important;
                    margin-top: 64px !important;
                    padding-left: 0 !important;
                    transition: margin-left 0.3s ease !important;
                    min-height: calc(100vh - 64px) !important;
                }

                div.content {
                    margin-left: 250px !important;
                    margin-top: 64px !important;
                }

                /* 确保侧边栏正确定位 */
                #sidebar {
                    position: fixed !important;
                    top: 64px !important;
                    left: 0 !important;
                    width: 250px !important;
                    height: calc(100vh - 64px) !important;
                    z-index: 1000 !important;
                    background-color: #f9fafb !important;
                    border-right: 1px solid #e5e7eb !important;
                    overflow-y: auto !important;
                    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1) !important;
                }

                /* 菜单项样式 */
                #sidebar .menu-item {
                    display: flex !important;
                    align-items: center !important;
                    padding: 12px 16px !important;
                    color: #6b7280 !important;
                    text-decoration: none !important;
                    transition: all 0.2s ease !important;
                    border-right: 4px solid transparent !important;
                }

                #sidebar .menu-item:hover {
                    background-color: #f3f4f6 !important;
                    color: #374151 !important;
                }

                #sidebar .menu-item.active {
                    background-color: #eff6ff !important;
                    color: #2563eb !important;
                    border-right-color: #2563eb !important;
                }

                #sidebar .menu-item i {
                    width: 24px !important;
                    margin-right: 12px !important;
                    text-align: center !important;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .content, div.content {
                        margin-left: 200px !important;
                    }
                    #sidebar {
                        width: 200px !important;
                    }
                }

                @media (max-width: 640px) {
                    .content, div.content {
                        margin-left: 0 !important;
                    }
                    #sidebar {
                        transform: translateX(-100%) !important;
                        transition: transform 0.3s ease !important;
                    }
                    #sidebar.open {
                        transform: translateX(0) !important;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // 直接设置内容区域的样式，确保立即生效
        setTimeout(() => {
            const contentElements = document.querySelectorAll('.content, div.content');
            contentElements.forEach(element => {
                element.style.setProperty('margin-left', '250px', 'important');
                element.style.setProperty('margin-top', '64px', 'important');
                element.style.setProperty('padding-left', '0', 'important');
                element.style.setProperty('min-height', 'calc(100vh - 64px)', 'important');
                element.style.setProperty('transition', 'margin-left 0.3s ease', 'important');
            });
        }, 100);
    },

    // 更新用户角色
    async updateUserRole() {
        try {
            if (window.authManager) {
                const userInfo = window.authManager.getCachedUserInfo();
                if (userInfo) {
                    this.setUserRole(userInfo);
                }
                
                // 获取最新用户信息
                const currentUser = await window.authManager.getCurrentUser();
                if (currentUser) {
                    this.setUserRole(currentUser);
                }
            } else {
                // 直接调用API获取用户信息
                const response = await axios.get('/api/auth/current');
                if (response.data.code === 200) {
                    this.setUserRole(response.data.data);
                }
            }
        } catch (error) {
            console.error('获取用户角色失败:', error);
            this.userRole = 'guest';
        }
    },

    // 设置用户角色
    setUserRole(userInfo) {
        if (!userInfo) {
            this.userRole = 'guest';
            return;
        }

        // 根据用户类型设置角色
        switch (userInfo.userType) {
            case 1:
                this.userRole = 'doctor';
                break;
            case 2:
                this.userRole = 'community';
                break;
            case 3:
                this.userRole = 'admin';
                break;
            default:
                this.userRole = 'guest';
        }

        console.log('侧边栏用户角色设置为:', this.userRole, '用户类型:', userInfo.userType);
    },

    // 根据用户角色生成菜单
    generateMenu() {
        const sidebarNav = document.getElementById('sidebarNav');
        if (!sidebarNav) {
            console.error('侧边栏导航元素不存在');
            return;
        }

        let menuItems = [];

        // 首页菜单（所有角色都有）
        menuItems.push({
            href: '/dashboard',
            id: 'menu-dashboard',
            icon: 'fas fa-home',
            text: '首页',
            active: this.currentPage === 'dashboard'
        });

        // 根据角色添加不同菜单
        if (this.userRole === 'doctor' || this.userRole === 'community' || this.userRole === 'admin') {
            menuItems.push(
                {
                    href: '/patients',
                    id: 'menu-patients',
                    icon: 'fas fa-users',
                    text: '患者管理',
                    active: this.currentPage === 'patients'
                },
                {
                    href: '/questionnaires',
                    id: 'menu-questionnaires',
                    icon: 'fas fa-clipboard-list',
                    text: '问卷管理',
                    active: this.currentPage === 'questionnaires'
                },
                {
                    href: '/reports',
                    id: 'menu-reports',
                    icon: 'fas fa-file-medical',
                    text: '报告管理',
                    active: this.currentPage === 'reports'
                },
                {
                    href: '/messages',
                    id: 'menu-messages',
                    icon: 'fas fa-envelope',
                    text: '消息管理',
                    active: this.currentPage === 'messages'
                }
            );
        }

        // 系统管理菜单（所有角色都有，但权限在后端控制）
        menuItems.push({
            href: '/system',
            id: 'menu-system',
            icon: 'fas fa-cog',
            text: '系统管理',
            active: this.currentPage === 'system'
        });

        // 生成菜单HTML
        let menuHTML = '';
        menuItems.forEach(item => {
            const activeClass = item.active ? 
                'menu-item active flex items-center px-4 py-3 text-blue-600 bg-blue-50 border-r-4 border-blue-600' : 
                'menu-item flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100 hover:text-gray-900';

            menuHTML += `
                <a href="${item.href}" id="${item.id}" class="${activeClass} transition-colors duration-200">
                    <i class="${item.icon} w-6 mr-3"></i>
                    <span>${item.text}</span>
                </a>
            `;
        });

        sidebarNav.innerHTML = menuHTML;
    },

    // 绑定事件
    bindEvents() {
        // 菜单点击事件
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                // 移除所有活动状态
                menuItems.forEach(mi => {
                    mi.classList.remove('active', 'text-blue-600', 'bg-blue-50', 'border-r-4', 'border-blue-600');
                    mi.classList.add('text-gray-600');
                });

                // 添加活动状态到当前项
                item.classList.add('active', 'text-blue-600', 'bg-blue-50', 'border-r-4', 'border-blue-600');
                item.classList.remove('text-gray-600');
            });
        });
    },

    // 设置当前活动菜单
    setActiveMenu(pageId) {
        this.currentPage = pageId;
        this.generateMenu();
    },

    // 获取菜单配置（供其他组件使用）
    getMenuConfig() {
        return {
            doctor: [
                { href: '/dashboard', icon: 'fas fa-home', text: '首页' },
                { href: '/patients', icon: 'fas fa-users', text: '患者管理' },
                { href: '/questionnaires', icon: 'fas fa-clipboard-list', text: '问卷管理' },
                { href: '/reports', icon: 'fas fa-file-medical', text: '报告管理' },
                { href: '/messages', icon: 'fas fa-envelope', text: '消息管理' },
                { href: '/system', icon: 'fas fa-cog', text: '系统管理' }
            ],
            community: [
                { href: '/dashboard', icon: 'fas fa-home', text: '首页' },
                { href: '/patients', icon: 'fas fa-users', text: '居民管理' },
                { href: '/questionnaires', icon: 'fas fa-clipboard-list', text: '问卷管理' },
                { href: '/reports', icon: 'fas fa-file-medical', text: '报告管理' },
                { href: '/messages', icon: 'fas fa-envelope', text: '消息管理' },
                { href: '/system', icon: 'fas fa-cog', text: '系统管理' }
            ],
            admin: [
                { href: '/dashboard', icon: 'fas fa-home', text: '首页' },
                { href: '/patients', icon: 'fas fa-users', text: '用户管理' },
                { href: '/questionnaires', icon: 'fas fa-clipboard-list', text: '问卷管理' },
                { href: '/reports', icon: 'fas fa-file-medical', text: '报告管理' },
                { href: '/messages', icon: 'fas fa-envelope', text: '消息管理' },
                { href: '/system', icon: 'fas fa-cog', text: '系统管理' }
            ],
            guest: [
                { href: '/dashboard', icon: 'fas fa-home', text: '首页' },
                { href: '/system', icon: 'fas fa-cog', text: '系统管理' }
            ]
        };
    }
};

// 全局函数，保持向后兼容
function initSidebar(currentPage = 'dashboard') {
    SidebarComponent.init(currentPage);
}

function setActiveMenu(pageId) {
    SidebarComponent.setActiveMenu(pageId);
}
