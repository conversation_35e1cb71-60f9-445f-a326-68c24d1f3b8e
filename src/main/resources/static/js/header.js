// 统一头部栏组件
window.HeaderComponent = {
    // 初始化头部栏
    init() {
        this.createHeader();
        this.bindEvents();
        this.updateUserInfo();
    },

    // 创建头部栏HTML
    createHeader() {
        const headerHTML = `
            <div class="fixed top-0 left-0 right-0 h-16 bg-white shadow flex items-center justify-between z-40 px-6 w-full border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <img src="/images/logo.png" alt="logo" class="h-10 w-auto">
                    <div class="flex items-center">
                        <span class="text-xl font-bold text-gray-800">居家睡眠远程管理平台</span>
                        <span id="roleTag" class="ml-2 px-2 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-600">系统管理</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户头像和下拉菜单 -->
                    <div class="relative" id="userDropdown">
                        <button id="userMenuButton" class="flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none">
                            <img id="userAvatar" src="/images/default-avatar.svg" alt="用户头像" class="h-8 w-8 rounded-full object-cover">
                            <span id="currentUser" class="text-sm font-medium">用户</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <!-- 下拉菜单 -->
                        <div id="userDropdownMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                            <a href="#" id="editProfileBtn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-edit mr-2"></i>修改个人信息
                            </a>
                            <a href="#" id="logoutBtn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 如果页面已有头部栏，先移除
        const existingHeader = document.querySelector('.fixed.top-0');
        if (existingHeader) {
            existingHeader.remove();
        }

        // 添加新的头部栏
        document.body.insertAdjacentHTML('afterbegin', headerHTML);
    },

    // 绑定事件
    bindEvents() {
        const userMenuButton = document.getElementById('userMenuButton');
        const userDropdownMenu = document.getElementById('userDropdownMenu');
        const editProfileBtn = document.getElementById('editProfileBtn');
        const logoutBtn = document.getElementById('logoutBtn');

        // 用户菜单点击事件
        userMenuButton.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdownMenu.classList.toggle('hidden');
        });

        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', () => {
            userDropdownMenu.classList.add('hidden');
        });

        // 修改个人信息
        editProfileBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.showEditProfileModal();
            userDropdownMenu.classList.add('hidden');
        });

        // 退出登录
        logoutBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.logout();
            userDropdownMenu.classList.add('hidden');
        });
    },

    // 更新用户信息
    async updateUserInfo() {
        try {
            // 如果有认证管理器，使用它
            if (window.authManager) {
                await window.authManager.updateUserDisplay();
                const userInfo = window.authManager.getCachedUserInfo();
                if (userInfo) {
                    this.setUserInfo(userInfo);
                }
            } else {
                // 否则直接调用API
                const response = await axios.get('/api/auth/current');
                if (response.data.code === 200) {
                    this.setUserInfo(response.data.data);
                }
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
        }
    },

    // 设置用户信息
    setUserInfo(userInfo) {
        const currentUserElement = document.getElementById('currentUser');
        const userAvatarElement = document.getElementById('userAvatar');
        const roleTagElement = document.getElementById('roleTag');

        if (currentUserElement) {
            currentUserElement.textContent = userInfo.realName || userInfo.username;
        }

        if (userAvatarElement) {
            if (userInfo.avatar) {
                userAvatarElement.src = userInfo.avatar;
            } else {
                userAvatarElement.src = '/images/default-avatar.svg';
            }
        }

        if (roleTagElement) {
            // 根据用户类型设置角色标签
            const roleConfig = this.getRoleConfig(userInfo.userType);
            roleTagElement.textContent = roleConfig.text;
            roleTagElement.className = `ml-2 px-2 py-0.5 rounded-full text-sm font-medium ${roleConfig.class}`;
        }
    },

    // 获取角色配置
    getRoleConfig(userType) {
        const configs = {
            1: { text: '医生端', class: 'bg-green-100 text-green-600' },
            2: { text: '社区端', class: 'bg-purple-100 text-purple-600' },
            3: { text: '系统管理', class: 'bg-blue-100 text-blue-600' }
        };
        return configs[userType] || configs[3];
    },

    // 显示编辑个人信息模态框
    showEditProfileModal() {
        // 先获取当前用户信息
        this.getCurrentUserInfo().then(userInfo => {
            if (userInfo) {
                this.createEditProfileModal(userInfo);
            }
        });
    },

    // 获取当前用户详细信息
    async getCurrentUserInfo() {
        try {
            const response = await axios.get('/api/auth/current');
            if (response.data.code === 200) {
                return response.data.data;
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
            if (window.UIComponents) {
                window.UIComponents.showErrorMessage('获取用户信息失败');
            }
        }
        return null;
    },

    // 创建编辑个人信息模态框
    createEditProfileModal(userInfo) {
        // 移除已存在的模态框
        const existingModal = document.getElementById('editProfileModal');
        if (existingModal) {
            existingModal.remove();
        }

        const modalHTML = `
            <div id="editProfileModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">修改个人信息</h3>
                        <form id="editProfileForm" onsubmit="return false;">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                                <input type="text" value="${userInfo.username || ''}" disabled class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">真实姓名</label>
                                <input type="text" value="${userInfo.realName || ''}" disabled class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                                <input type="email" name="email" value="${userInfo.email || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                                <input type="tel" name="phone" value="${userInfo.phone || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">新密码（留空则不修改）</label>
                                <input type="password" name="password" placeholder="请输入新密码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">确认新密码</label>
                                <input type="password" name="confirmPassword" placeholder="请再次输入新密码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">头像</label>
                                <div class="flex items-center space-x-4">
                                    <img id="previewAvatar" src="${userInfo.avatar || '/images/default-avatar.svg'}" alt="头像预览" class="h-16 w-16 rounded-full object-cover border">
                                    <input type="file" id="avatarFile" accept="image/*" class="hidden">
                                    <button type="button" onclick="document.getElementById('avatarFile').click()" class="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
                                        选择头像
                                    </button>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3">
                                <button type="button" onclick="HeaderComponent.closeEditProfileModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">取消</button>
                                <button type="button" onclick="HeaderComponent.saveProfile()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">保存</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // 绑定头像选择事件
        document.getElementById('avatarFile').addEventListener('change', this.handleAvatarChange);
    },

    // 处理头像选择
    handleAvatarChange(event) {
        const file = event.target.files[0];
        if (file) {
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                if (window.UIComponents) {
                    window.UIComponents.showErrorMessage('请选择图片文件');
                }
                return;
            }

            // 验证文件大小（限制为2MB）
            if (file.size > 2 * 1024 * 1024) {
                if (window.UIComponents) {
                    window.UIComponents.showErrorMessage('图片大小不能超过2MB');
                }
                return;
            }

            // 预览图片
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('previewAvatar').src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    },

    // 保存个人信息
    async saveProfile() {
        const form = document.getElementById('editProfileForm');
        const formData = new FormData(form);
        
        // 获取表单数据
        const profileData = {
            email: formData.get('email'),
            phone: formData.get('phone')
        };

        // 密码验证
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        
        if (password) {
            if (password !== confirmPassword) {
                if (window.UIComponents) {
                    window.UIComponents.showErrorMessage('两次输入的密码不一致');
                }
                return;
            }
            if (password.length < 6) {
                if (window.UIComponents) {
                    window.UIComponents.showErrorMessage('密码长度不能少于6位');
                }
                return;
            }
            profileData.password = password;
        }

        try {
            // 先上传头像（如果有选择新头像）
            const avatarFile = document.getElementById('avatarFile').files[0];
            if (avatarFile) {
                const avatarFormData = new FormData();
                avatarFormData.append('file', avatarFile);
                
                const uploadResponse = await axios.post('/api/upload/avatar', avatarFormData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });
                
                if (uploadResponse.data.code === 200) {
                    profileData.avatar = uploadResponse.data.data;
                } else {
                    if (window.UIComponents) {
                        window.UIComponents.showErrorMessage('头像上传失败：' + uploadResponse.data.message);
                    }
                    return;
                }
            }

            // 更新个人信息
            const response = await axios.put('/api/auth/profile', profileData);
            
            if (response.data.code === 200) {
                if (window.UIComponents) {
                    window.UIComponents.showSuccessMessage('个人信息更新成功');
                }
                this.closeEditProfileModal();
                // 刷新头部栏用户信息
                this.updateUserInfo();
            } else {
                if (window.UIComponents) {
                    window.UIComponents.showErrorMessage(response.data.message || '更新失败');
                }
            }
        } catch (error) {
            console.error('更新个人信息失败:', error);
            if (window.UIComponents) {
                window.UIComponents.showErrorMessage('更新失败，请稍后重试');
            }
        }
    },

    // 关闭编辑个人信息模态框
    closeEditProfileModal() {
        const modal = document.getElementById('editProfileModal');
        if (modal) {
            modal.remove();
        }
    },

    // 退出登录
    logout() {
        if (window.UIComponents) {
            window.UIComponents.showConfirmDialog(
                '退出登录',
                '确定要退出登录吗？',
                () => {
                    if (window.authManager) {
                        window.authManager.logout();
                    } else {
                        // 清除本地存储
                        localStorage.removeItem('token');
                        localStorage.removeItem('userInfo');
                        // 跳转到登录页
                        window.location.href = '/';
                    }
                }
            );
        } else {
            if (confirm('确定要退出登录吗？')) {
                if (window.authManager) {
                    window.authManager.logout();
                } else {
                    localStorage.removeItem('token');
                    localStorage.removeItem('userInfo');
                    window.location.href = '/';
                }
            }
        }
    }
};

// 全局函数，保持向后兼容
function logout() {
    HeaderComponent.logout();
}
