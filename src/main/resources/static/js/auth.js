/**
 * 统一认证管理模块
 * 负责处理用户认证状态检查、用户信息获取、登录状态维护等
 */
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.authCheckPromise = null;
    }

    /**
     * 初始化认证管理器
     * 页面加载时调用，检查用户认证状态
     */
    async init() {
        try {
            await this.checkAuthStatus();
            this.setupAxiosInterceptors();
            return true;
        } catch (error) {
            console.error('认证初始化失败:', error);
            return false;
        }
    }

    /**
     * 检查认证状态
     * 从服务器获取当前用户信息
     */
    async checkAuthStatus() {
        // 如果已经有正在进行的认证检查，返回该Promise
        if (this.authCheckPromise) {
            return this.authCheckPromise;
        }

        this.authCheckPromise = this._performAuthCheck();
        
        try {
            const result = await this.authCheckPromise;
            return result;
        } finally {
            this.authCheckPromise = null;
        }
    }

    /**
     * 执行认证检查的内部方法
     */
    async _performAuthCheck() {
        try {
            const response = await fetch('/api/auth/current', {
                method: 'GET',
                credentials: 'same-origin', // 确保发送session cookie
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.code === 200) {
                    this.currentUser = data.data;
                    this.isAuthenticated = true;
                    
                    // 保存用户信息到localStorage（用于页面刷新时的快速显示）
                    localStorage.setItem('userInfo', JSON.stringify(this.currentUser));
                    
                    // 触发认证成功事件
                    this._dispatchAuthEvent('auth-success', this.currentUser);
                    
                    return this.currentUser;
                } else {
                    throw new Error(data.message || '获取用户信息失败');
                }
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            this.currentUser = null;
            this.isAuthenticated = false;
            localStorage.removeItem('userInfo');
            
            // 触发认证失败事件
            this._dispatchAuthEvent('auth-failed', error);
            
            throw error;
        }
    }

    /**
     * 获取当前用户信息
     * 如果没有缓存，会先进行认证检查
     */
    async getCurrentUser() {
        if (this.currentUser) {
            return this.currentUser;
        }
        
        return await this.checkAuthStatus();
    }

    /**
     * 检查是否已认证
     */
    isUserAuthenticated() {
        return this.isAuthenticated;
    }

    /**
     * 登出
     */
    async logout() {
        try {
            await fetch('/logout', {
                method: 'POST',
                credentials: 'same-origin'
            });
        } catch (error) {
            console.error('登出请求失败:', error);
        } finally {
            // 清理本地状态
            this.currentUser = null;
            this.isAuthenticated = false;
            localStorage.removeItem('userInfo');
            
            // 触发登出事件
            this._dispatchAuthEvent('auth-logout');
            
            // 跳转到登录页
            window.location.href = '/doctor/login';
        }
    }

    /**
     * 设置用户信息显示
     * 自动查找页面中的用户信息显示元素并更新
     */
    async updateUserDisplay() {
        try {
            const user = await this.getCurrentUser();
            
            // 更新所有可能的用户显示元素
            const userElements = [
                'currentUser',
                'welcomeName',
                'userName',
                'userDisplayName'
            ];
            
            userElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = user.realName || user.username;
                }
            });
            
            return user;
        } catch (error) {
            console.error('更新用户显示失败:', error);
            throw error;
        }
    }

    /**
     * 设置Axios拦截器
     * 统一处理认证相关的HTTP请求
     */
    setupAxiosInterceptors() {
        if (typeof axios !== 'undefined') {
            // 请求拦截器
            axios.interceptors.request.use(
                config => {
                    // 确保发送session cookie
                    config.withCredentials = true;
                    return config;
                },
                error => {
                    return Promise.reject(error);
                }
            );

            // 响应拦截器
            axios.interceptors.response.use(
                response => {
                    return response;
                },
                error => {
                    // 如果是401未授权，清理认证状态并跳转登录
                    if (error.response && error.response.status === 401) {
                        this.currentUser = null;
                        this.isAuthenticated = false;
                        localStorage.removeItem('userInfo');
                        window.location.href = '/doctor/login';
                    }
                    return Promise.reject(error);
                }
            );
        }
    }

    /**
     * 触发认证相关事件
     */
    _dispatchAuthEvent(eventType, data = null) {
        const event = new CustomEvent(eventType, {
            detail: data
        });
        document.dispatchEvent(event);
    }

    /**
     * 从localStorage快速获取用户信息（用于页面初始显示）
     * 注意：这只是为了快速显示，实际认证状态仍需通过服务器验证
     */
    getCachedUserInfo() {
        try {
            const userInfo = localStorage.getItem('userInfo');
            return userInfo ? JSON.parse(userInfo) : null;
        } catch (error) {
            console.error('解析缓存用户信息失败:', error);
            localStorage.removeItem('userInfo');
            return null;
        }
    }
}

// 创建全局认证管理器实例
window.authManager = new AuthManager();

// 页面加载完成后自动初始化（如果需要）
document.addEventListener('DOMContentLoaded', function() {
    // 只在需要认证的页面自动初始化
    if (document.body.dataset.requireAuth === 'true') {
        window.authManager.init().catch(error => {
            console.error('自动认证初始化失败:', error);
        });
    }
});

// 导出认证管理器（用于模块化环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
