// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkLogin();
    
    // 设置axios默认配置
    setupAxios();
    
    // 加载用户列表
    loadUserList();
    
    // 绑定搜索事件
    document.getElementById('userSearch').addEventListener('input', debounce(loadUserList, 500));
    document.getElementById('userTypeFilter').addEventListener('change', loadUserList);
    
    // 绑定添加用户表单提交事件
    document.getElementById('addUserForm').addEventListener('submit', handleAddUser);
});

// 检查登录状态
function checkLogin() {
    const token = localStorage.getItem('token');
    const userInfo = localStorage.getItem('userInfo');
    
    if (!token || !userInfo) {
        window.location.href = '/doctor/login';
        return;
    }
    
    try {
        const user = JSON.parse(userInfo);
        document.getElementById('currentUser').textContent = user.realName || user.username;
    } catch (e) {
        console.error('解析用户信息失败:', e);
    }
}

// 设置axios默认配置
function setupAxios() {
    const token = localStorage.getItem('token');
    if (token) {
        axios.defaults.headers.common['Authorization'] = 'Bearer ' + token;
    }
    
    // 响应拦截器
    axios.interceptors.response.use(
        response => response,
        error => {
            if (error.response && error.response.status === 401) {
                localStorage.removeItem('token');
                localStorage.removeItem('userInfo');
                window.location.href = '/doctor/login';
            }
            return Promise.reject(error);
        }
    );
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 显示用户管理
function showUserManagement() {
    hideAllContent();
    document.getElementById('userManagement').classList.remove('hidden');
    setActiveMenu('userMenu');
    loadUserList();
}

// 显示角色管理
function showRoleManagement() {
    hideAllContent();
    document.getElementById('roleManagement').classList.remove('hidden');
    setActiveMenu('roleMenu');
}

// 显示权限管理
function showPermissionManagement() {
    hideAllContent();
    document.getElementById('permissionManagement').classList.remove('hidden');
    setActiveMenu('permissionMenu');
}

// 显示操作日志
function showOperationLog() {
    hideAllContent();
    document.getElementById('operationLog').classList.remove('hidden');
    setActiveMenu('logMenu');
}

// 隐藏所有内容
function hideAllContent() {
    const contents = document.querySelectorAll('.management-content');
    contents.forEach(content => content.classList.add('hidden'));
}

// 设置活动菜单
function setActiveMenu(activeId) {
    const menus = ['userMenu', 'roleMenu', 'permissionMenu', 'logMenu'];
    menus.forEach(menuId => {
        const menu = document.getElementById(menuId);
        if (menuId === activeId) {
            menu.className = 'flex items-center px-4 py-3 text-blue-600 bg-blue-50 border-l-4 border-blue-600';
        } else {
            menu.className = 'flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100';
        }
    });
}

// 加载用户列表
async function loadUserList() {
    try {
        const searchValue = document.getElementById('userSearch').value;
        const userType = document.getElementById('userTypeFilter').value;
        
        const params = {
            pageNum: currentPage,
            pageSize: pageSize
        };
        
        if (searchValue) {
            params.username = searchValue;
        }
        
        if (userType) {
            params.userType = userType;
        }
        
        const response = await axios.get('/api/system/user/list', { params });
        
        if (response.data.code === 200) {
            const data = response.data.data;
            renderUserTable(data.records);
            renderPagination(data);
        } else {
            console.error('加载用户列表失败:', response.data.message);
        }
    } catch (error) {
        console.error('加载用户列表错误:', error);
    }
}

// 渲染用户表格
function renderUserTable(users) {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <i class="fas fa-user text-gray-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">${user.realName || user.username}</div>
                        <div class="text-sm text-gray-500">${user.email || ''}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getUserTypeClass(user.userType)}">
                    ${getUserTypeText(user.userType)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${user.status === 1 ? '正常' : '禁用'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatDateTime(user.createTime)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button onclick="editUser(${user.id})" class="text-blue-600 hover:text-blue-900 mr-4">编辑</button>
                <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-900">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 获取用户类型样式类
function getUserTypeClass(userType) {
    switch (userType) {
        case 1: return 'bg-blue-100 text-blue-800';
        case 2: return 'bg-purple-100 text-purple-800';
        case 3: return 'bg-green-100 text-green-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

// 获取用户类型文本
function getUserTypeText(userType) {
    switch (userType) {
        case 1: return '医生';
        case 2: return '社区管理员';
        case 3: return '系统管理员';
        default: return '未知';
    }
}

// 格式化日期时间
function formatDateTime(dateTime) {
    if (!dateTime) return '';
    return new Date(dateTime).toLocaleString('zh-CN');
}

// 渲染分页
function renderPagination(data) {
    totalPages = data.pages;
    currentPage = data.current;
    
    // 更新页面信息
    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, data.total);
    document.getElementById('pageInfo').textContent = `显示第 ${start} 到 ${end} 条，共 ${data.total} 条记录`;
    
    // 生成分页按钮
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    // 上一页按钮
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
    prevButton.className = `relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''}`;
    prevButton.onclick = () => currentPage > 1 && changePage(currentPage - 1);
    pagination.appendChild(prevButton);
    
    // 页码按钮
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const pageButton = document.createElement('button');
        pageButton.textContent = i;
        pageButton.className = `relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium ${i === currentPage ? 'bg-blue-50 border-blue-500 text-blue-600' : 'bg-white text-gray-700 hover:bg-gray-50'}`;
        pageButton.onclick = () => changePage(i);
        pagination.appendChild(pageButton);
    }
    
    // 下一页按钮
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
    nextButton.className = `relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''}`;
    nextButton.onclick = () => currentPage < totalPages && changePage(currentPage + 1);
    pagination.appendChild(nextButton);
}

// 切换页面
function changePage(page) {
    currentPage = page;
    loadUserList();
}

// 显示添加用户模态框
function showAddUserModal() {
    document.getElementById('addUserModal').classList.remove('hidden');
}

// 关闭添加用户模态框
function closeAddUserModal() {
    document.getElementById('addUserModal').classList.add('hidden');
    document.getElementById('addUserForm').reset();
}

// 处理添加用户
async function handleAddUser(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const userData = {
        username: formData.get('username'),
        password: formData.get('password'),
        realName: formData.get('realName'),
        email: formData.get('email'),
        userType: parseInt(formData.get('userType')),
        status: 1
    };
    
    try {
        const response = await axios.post('/api/system/user', userData);
        
        if (response.data.code === 200) {
            alert('添加用户成功');
            closeAddUserModal();
            loadUserList();
        } else {
            alert(response.data.message || '添加用户失败');
        }
    } catch (error) {
        console.error('添加用户错误:', error);
        alert('添加用户失败');
    }
}

// 编辑用户
function editUser(userId) {
    alert('编辑用户功能开发中...');
}

// 删除用户
async function deleteUser(userId) {
    if (!confirm('确定要删除这个用户吗？')) {
        return;
    }
    
    try {
        const response = await axios.delete(`/api/system/user/${userId}`);
        
        if (response.data.code === 200) {
            alert('删除用户成功');
            loadUserList();
        } else {
            alert(response.data.message || '删除用户失败');
        }
    } catch (error) {
        console.error('删除用户错误:', error);
        alert('删除用户失败');
    }
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        window.location.href = '/doctor/login';
    }
}
