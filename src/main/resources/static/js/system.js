// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;
let currentTab = 'user';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // 先显示缓存的用户信息（快速显示）
        const cachedUser = window.authManager.getCachedUserInfo();
        if (cachedUser) {
            document.getElementById('currentUser').textContent = cachedUser.realName || cachedUser.username;
        }

        // 初始化认证管理器
        await window.authManager.init();
        await window.authManager.updateUserDisplay();

        // 生成左侧菜单
        generateSidebarMenu();

        // 默认显示用户管理TAB
        showTab('user');

    } catch (error) {
        console.error('系统管理页面初始化失败:', error);
        // 认证失败时，Spring Security会自动重定向到登录页
    }
});

// 生成左侧菜单
function generateSidebarMenu() {
    const menuContainer = document.getElementById('sidebarMenu');
    if (!menuContainer) return;

    const menuItems = [
        {
            href: '/dashboard',
            id: 'menu-dashboard',
            icon: 'fas fa-tachometer-alt',
            text: '首页'
        },
        {
            href: '/patients',
            id: 'menu-patients',
            icon: 'fas fa-users',
            text: '患者管理'
        },
        {
            href: '/questionnaires',
            id: 'menu-questionnaires',
            icon: 'fas fa-clipboard-list',
            text: '问卷管理'
        },
        {
            href: '/reports',
            id: 'menu-reports',
            icon: 'fas fa-file-medical',
            text: '报告管理'
        },
        {
            href: '/messages',
            id: 'menu-messages',
            icon: 'fas fa-envelope',
            text: '消息管理'
        },
        {
            href: '/system',
            id: 'menu-system',
            icon: 'fas fa-cogs',
            text: '系统管理',
            active: true
        }
    ];

    menuContainer.innerHTML = menuItems.map(item => `
        <a href="${item.href}" class="flex items-center px-4 py-3 ${item.active ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600' : 'text-gray-600 hover:bg-gray-100'}" id="${item.id}">
            <i class="${item.icon} w-6"></i>
            <span>${item.text}</span>
        </a>
    `).join('');
}

// 退出登录
function logout() {
    if (window.notification) {
        window.notification.success('已安全退出');
        setTimeout(() => {
            window.authManager.logout();
        }, 1000);
    } else {
        window.authManager.logout();
    }
}

// TAB切换功能
function showTab(tabName) {
    // 隐藏所有TAB内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // 移除所有TAB按钮的active状态
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active', 'text-blue-600', 'border-blue-600');
        button.classList.add('text-gray-500', 'border-transparent');
    });

    // 显示当前TAB内容
    document.getElementById(`tab-content-${tabName}`).classList.remove('hidden');

    // 设置当前TAB按钮为active状态
    const activeButton = document.getElementById(`tab-${tabName}`);
    activeButton.classList.add('active', 'text-blue-600', 'border-blue-600');
    activeButton.classList.remove('text-gray-500', 'border-transparent');

    currentTab = tabName;

    // 根据TAB加载对应内容
    switch(tabName) {
        case 'user':
            loadUserManagement();
            break;
        case 'role':
            loadRoleManagement();
            break;
        case 'permission':
            loadPermissionManagement();
            break;
        case 'log':
            loadLogManagement();
            break;
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// ==================== 用户管理模块 ====================
function loadUserManagement() {
    const container = document.getElementById('tab-content-user');
    container.innerHTML = `
        <div class="p-6">
            <!-- 搜索和操作栏 -->
            <div class="mb-6 flex justify-between items-center">
                <div class="flex space-x-4">
                    <div class="relative">
                        <input type="text" id="userSearch" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="搜索用户...">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                    <select id="userTypeFilter" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">所有类型</option>
                        <option value="1">医生</option>
                        <option value="2">社区管理员</option>
                        <option value="3">系统管理员</option>
                    </select>
                </div>
                <button onclick="showAddUserModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <i class="fas fa-plus mr-2"></i>添加用户
                </button>
            </div>

            <!-- 用户列表 -->
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户信息</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 用户数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <button onclick="previousPage()" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            上一页
                        </button>
                        <button onclick="nextPage()" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            下一页
                        </button>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700" id="pageInfo">
                                显示第 1 到 10 条，共 0 条记录
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 绑定事件
    document.getElementById('userSearch').addEventListener('input', debounce(loadUserList, 500));
    document.getElementById('userTypeFilter').addEventListener('change', loadUserList);

    // 加载用户列表
    loadUserList();
}

// 加载用户列表
async function loadUserList() {
    try {
        const searchValue = document.getElementById('userSearch')?.value || '';
        const userTypeFilter = document.getElementById('userTypeFilter')?.value || '';

        const params = new URLSearchParams({
            pageNum: currentPage,
            pageSize: pageSize
        });

        if (searchValue) {
            params.append('username', searchValue);
        }

        if (userTypeFilter) {
            params.append('userType', userTypeFilter);
        }

        const response = await axios.get(`/api/system/user/list?${params}`);

        if (response.data.code === 200) {
            const data = response.data.data;
            renderUserTable(data.records);
            renderPagination(data);
        } else {
            if (window.notification) {
                window.notification.error(response.data.message || '加载用户列表失败');
            }
        }
    } catch (error) {
        console.error('加载用户列表错误:', error);
        if (window.notification) {
            window.notification.error('加载用户列表失败');
        }
    }
}

// 渲染用户表格
function renderUserTable(users) {
    const tbody = document.getElementById('userTableBody');
    if (!tbody) return;

    if (!users || users.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="px-6 py-12 text-center">
                    <div class="flex flex-col items-center justify-center">
                        <i class="fas fa-users text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500 text-lg font-medium">暂无用户记录</p>
                        <p class="text-gray-400 text-sm mt-2">点击上方"添加用户"按钮创建新用户</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = users.map(user => `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <i class="fas fa-user text-gray-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">${user.realName || user.username}</div>
                        <div class="text-sm text-gray-500">${user.email || ''}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUserTypeClass(user.userType)}">
                    ${getUserTypeText(user.userType)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${user.status === 1 ? '启用' : '禁用'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatDate(user.createTime)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button onclick="editUser(${user.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-900">删除</button>
            </td>
        </tr>
    `).join('');
}

// 辅助函数
function getUserTypeClass(userType) {
    switch(userType) {
        case 1: return 'bg-blue-100 text-blue-800';
        case 2: return 'bg-green-100 text-green-800';
        case 3: return 'bg-purple-100 text-purple-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getUserTypeText(userType) {
    switch(userType) {
        case 1: return '医生';
        case 2: return '社区管理员';
        case 3: return '系统管理员';
        default: return '未知';
    }
}

function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN');
}

// 分页相关函数
function renderPagination(data) {
    totalPages = data.pages;
    const pageInfo = document.getElementById('pageInfo');
    const pagination = document.getElementById('pagination');

    if (pageInfo) {
        const start = (currentPage - 1) * pageSize + 1;
        const end = Math.min(currentPage * pageSize, data.total);
        pageInfo.textContent = `显示第 ${start} 到 ${end} 条，共 ${data.total} 条记录`;
    }

    if (pagination) {
        let paginationHTML = '';

        // 上一页按钮
        paginationHTML += `
            <button onclick="previousPage()" ${currentPage <= 1 ? 'disabled' : ''}
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage <= 1 ? 'cursor-not-allowed opacity-50' : ''}">
                <i class="fas fa-chevron-left"></i>
            </button>
        `;

        // 页码按钮
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage) {
                paginationHTML += `
                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                        ${i}
                    </button>
                `;
            } else {
                paginationHTML += `
                    <button onclick="goToPage(${i})" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                        ${i}
                    </button>
                `;
            }
        }

        // 下一页按钮
        paginationHTML += `
            <button onclick="nextPage()" ${currentPage >= totalPages ? 'disabled' : ''}
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage >= totalPages ? 'cursor-not-allowed opacity-50' : ''}">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;

        pagination.innerHTML = paginationHTML;
    }
}

function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        loadUserList();
    }
}

function nextPage() {
    if (currentPage < totalPages) {
        currentPage++;
        loadUserList();
    }
}

function goToPage(page) {
    currentPage = page;
    loadUserList();
}

// ==================== 角色管理模块 ====================
function loadRoleManagement() {
    const container = document.getElementById('tab-content-role');
    container.innerHTML = `
        <div class="p-6">
            <div class="mb-6 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">角色管理</h3>
                <button onclick="showAddRoleModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <i class="fas fa-plus mr-2"></i>添加角色
                </button>
            </div>

            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色描述</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="roleTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 角色数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    loadRoleList();
}

async function loadRoleList() {
    try {
        const response = await axios.get('/api/system/role/list', {
            params: {
                pageNum: currentPage,
                pageSize: pageSize
            }
        });

        if (response.data.code === 200) {
            const data = response.data.data;
            renderRoleTable(data.records || []);
            if (data.records) {
                renderPagination(data);
            }
        } else {
            console.error('加载角色列表失败:', response.data.message);
            // 如果API失败，显示模拟数据
            const mockRoles = [
                { id: 1, roleName: '系统管理员', remark: '拥有系统所有权限', createTime: '2024-01-01 10:00:00', status: 1 },
                { id: 2, roleName: '医生', remark: '医生角色，可管理患者和问卷', createTime: '2024-01-01 10:00:00', status: 1 },
                { id: 3, roleName: '社区管理员', remark: '社区管理员角色', createTime: '2024-01-01 10:00:00', status: 1 }
            ];
            renderRoleTable(mockRoles);
        }
    } catch (error) {
        console.error('加载角色列表错误:', error);
        // 显示模拟数据作为后备
        const mockRoles = [
            { id: 1, roleName: '系统管理员', remark: '拥有系统所有权限', createTime: '2024-01-01 10:00:00', status: 1 },
            { id: 2, roleName: '医生', remark: '医生角色，可管理患者和问卷', createTime: '2024-01-01 10:00:00', status: 1 },
            { id: 3, roleName: '社区管理员', remark: '社区管理员角色', createTime: '2024-01-01 10:00:00', status: 1 }
        ];
        renderRoleTable(mockRoles);
    }
}

function renderRoleTable(roles) {
    const tbody = document.getElementById('roleTableBody');
    if (!tbody) return;

    if (!roles || roles.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="px-6 py-12 text-center">
                    <div class="flex flex-col items-center justify-center">
                        <i class="fas fa-inbox text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500 text-lg font-medium">暂无角色记录</p>
                        <p class="text-gray-400 text-sm mt-2">点击上方"添加角色"按钮创建新角色</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = roles.map(role => `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${role.roleName || role.name}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${role.description || role.remark || ''}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(role.createTime)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button onclick="editRole(${role.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                <button onclick="deleteRole(${role.id})" class="text-red-600 hover:text-red-900">删除</button>
            </td>
        </tr>
    `).join('');
}

// ==================== 权限管理模块 ====================
function loadPermissionManagement() {
    const container = document.getElementById('tab-content-permission');
    container.innerHTML = `
        <div class="p-6">
            <div class="mb-6 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">权限管理</h3>
                <button onclick="showAddPermissionModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <i class="fas fa-plus mr-2"></i>添加权限
                </button>
            </div>

            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权限名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权限代码</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权限描述</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="permissionTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 权限数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    loadPermissionList();
}

function loadPermissionList() {
    // 模拟权限数据
    const permissions = [
        { id: 1, name: '用户管理', code: 'user:manage', description: '管理系统用户' },
        { id: 2, name: '角色管理', code: 'role:manage', description: '管理系统角色' },
        { id: 3, name: '权限管理', code: 'permission:manage', description: '管理系统权限' },
        { id: 4, name: '患者管理', code: 'patient:manage', description: '管理患者信息' },
        { id: 5, name: '问卷管理', code: 'questionnaire:manage', description: '管理问卷' }
    ];

    renderPermissionTable(permissions);
}

function renderPermissionTable(permissions) {
    const tbody = document.getElementById('permissionTableBody');
    if (!tbody) return;

    if (!permissions || permissions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="px-6 py-12 text-center">
                    <div class="flex flex-col items-center justify-center">
                        <i class="fas fa-key text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500 text-lg font-medium">暂无权限记录</p>
                        <p class="text-gray-400 text-sm mt-2">点击上方"添加权限"按钮创建新权限</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = permissions.map(permission => `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${permission.name}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${permission.code}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${permission.description}</td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button onclick="editPermission(${permission.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                <button onclick="deletePermission(${permission.id})" class="text-red-600 hover:text-red-900">删除</button>
            </td>
        </tr>
    `).join('');
}

// ==================== 操作日志模块 ====================
function loadLogManagement() {
    const container = document.getElementById('tab-content-log');
    container.innerHTML = `
        <div class="p-6">
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">操作日志</h3>
                <div class="flex items-center space-x-4">
                    <div class="date-input-container">
                        <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                        <input type="date" id="logStartDate" class="date-input">
                    </div>
                    <div class="date-input-container">
                        <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                        <input type="date" id="logEndDate" class="date-input">
                    </div>
                    <div class="pt-6">
                        <button onclick="searchLogs()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            <i class="fas fa-search mr-2"></i>查询
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作用户</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作描述</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作时间</th>
                            </tr>
                        </thead>
                        <tbody id="logTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 日志数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页控件 -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <button onclick="previousPage()" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            上一页
                        </button>
                        <button onclick="nextPage()" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            下一页
                        </button>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                显示第 <span class="font-medium" id="logStartRecord">1</span> 到 <span class="font-medium" id="logEndRecord">10</span> 条，
                                共 <span class="font-medium" id="logTotalRecords">0</span> 条记录
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="logPagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    loadLogList();
}

async function loadLogList() {
    try {
        const startDate = document.getElementById('logStartDate').value;
        const endDate = document.getElementById('logEndDate').value;

        const params = {
            pageNum: currentPage,
            pageSize: pageSize
        };

        if (startDate) {
            params.startTime = startDate + ' 00:00:00';
        }
        if (endDate) {
            params.endTime = endDate + ' 23:59:59';
        }

        const response = await axios.get('/api/system/operlog/list', { params });

        if (response.data.code === 200) {
            const data = response.data.data;
            renderLogTable(data.records || []);
            if (data.records) {
                renderPagination(data);
            }
        } else {
            console.error('加载操作日志失败:', response.data.message);
            // 如果API失败，显示模拟数据
            const mockLogs = [
                { id: 1, operName: 'admin', businessType: '登录', title: '用户登录系统', operIp: '127.0.0.1', operTime: '2024-01-15 14:20:00' },
                { id: 2, operName: 'doctor', businessType: '新增', title: '添加新患者信息', operIp: '*************', operTime: '2024-01-15 11:00:00' },
                { id: 3, operName: 'admin', businessType: '修改', title: '修改用户权限', operIp: '127.0.0.1', operTime: '2024-01-15 10:30:00' }
            ];
            renderLogTable(mockLogs);
        }
    } catch (error) {
        console.error('加载操作日志错误:', error);
        // 显示模拟数据作为后备
        const mockLogs = [
            { id: 1, operName: 'admin', businessType: '登录', title: '用户登录系统', operIp: '127.0.0.1', operTime: '2024-01-15 14:20:00' },
            { id: 2, operName: 'doctor', businessType: '新增', title: '添加新患者信息', operIp: '*************', operTime: '2024-01-15 11:00:00' },
            { id: 3, operName: 'admin', businessType: '修改', title: '修改用户权限', operIp: '127.0.0.1', operTime: '2024-01-15 10:30:00' }
        ];
        renderLogTable(mockLogs);
    }
}

function searchLogs() {
    currentPage = 1; // 重置到第一页
    loadLogList(); // 重新加载日志列表
}

function renderLogTable(logs) {
    const tbody = document.getElementById('logTableBody');
    if (!tbody) return;

    if (!logs || logs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="px-6 py-12 text-center">
                    <div class="flex flex-col items-center justify-center">
                        <i class="fas fa-file-alt text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500 text-lg font-medium">暂无操作日志</p>
                        <p class="text-gray-400 text-sm mt-2">系统操作日志将在此处显示</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = logs.map(log => `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${log.operName || log.username || ''}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.businessType || log.operation || ''}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.title || log.description || ''}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.operIp || log.ip || ''}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(log.operTime || log.createTime)}</td>
        </tr>
    `).join('');
}

// 隐藏所有内容
function hideAllContent() {
    const contents = document.querySelectorAll('.management-content');
    contents.forEach(content => content.classList.add('hidden'));
}

// 设置活动菜单
function setActiveMenu(activeId) {
    const menus = ['userMenu', 'roleMenu', 'permissionMenu', 'logMenu'];
    menus.forEach(menuId => {
        const menu = document.getElementById(menuId);
        if (menuId === activeId) {
            menu.className = 'flex items-center px-4 py-3 text-blue-600 bg-blue-50 border-l-4 border-blue-600';
        } else {
            menu.className = 'flex items-center px-4 py-3 text-gray-600 hover:bg-gray-100';
        }
    });
}

// 加载用户列表
async function loadUserList() {
    try {
        const searchValue = document.getElementById('userSearch').value;
        const userType = document.getElementById('userTypeFilter').value;
        
        const params = {
            page: currentPage,
            size: pageSize
        };

        if (searchValue) {
            params.username = searchValue;
        }

        if (userType) {
            params.userType = userType;
        }

        const response = await axios.get('/api/system/user/list', { params });

        if (response.data.code === 200) {
            const data = response.data.data;
            renderUserTable(data.records);
            renderPagination(data);
        } else {
            notification.error(response.data.message || '加载用户列表失败');
        }
    } catch (error) {
        console.error('加载用户列表错误:', error);
        notification.error('加载用户列表失败');
    }
}

// 渲染用户表格
function renderUserTable(users) {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <i class="fas fa-user text-gray-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">${user.realName || user.username}</div>
                        <div class="text-sm text-gray-500">${user.email || ''}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getUserTypeClass(user.userType)}">
                    ${getUserTypeText(user.userType)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${user.status === 1 ? '正常' : '禁用'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatDateTime(user.createTime)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button onclick="editUser(${user.id})" class="text-blue-600 hover:text-blue-900 mr-2">编辑</button>
                <button onclick="resetUserPassword(${user.id})" class="text-yellow-600 hover:text-yellow-900 mr-2">重置密码</button>
                <button onclick="toggleUserStatus(${user.id}, ${user.status})" class="text-${user.status === 1 ? 'orange' : 'green'}-600 hover:text-${user.status === 1 ? 'orange' : 'green'}-900 mr-2">
                    ${user.status === 1 ? '禁用' : '启用'}
                </button>
                <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-900">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 获取用户类型样式类
function getUserTypeClass(userType) {
    switch (userType) {
        case 1: return 'bg-blue-100 text-blue-800';
        case 2: return 'bg-purple-100 text-purple-800';
        case 3: return 'bg-green-100 text-green-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

// 获取用户类型文本
function getUserTypeText(userType) {
    switch (userType) {
        case 1: return '医生';
        case 2: return '社区管理员';
        case 3: return '系统管理员';
        default: return '未知';
    }
}

// 格式化日期时间
function formatDateTime(dateTime) {
    if (!dateTime) return '';
    return new Date(dateTime).toLocaleString('zh-CN');
}

// 渲染分页
function renderPagination(data) {
    totalPages = data.pages;
    currentPage = data.current;
    
    // 更新页面信息
    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, data.total);
    document.getElementById('pageInfo').textContent = `显示第 ${start} 到 ${end} 条，共 ${data.total} 条记录`;
    
    // 生成分页按钮
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    // 上一页按钮
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
    prevButton.className = `relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''}`;
    prevButton.onclick = () => currentPage > 1 && changePage(currentPage - 1);
    pagination.appendChild(prevButton);
    
    // 页码按钮
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const pageButton = document.createElement('button');
        pageButton.textContent = i;
        pageButton.className = `relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium ${i === currentPage ? 'bg-blue-50 border-blue-500 text-blue-600' : 'bg-white text-gray-700 hover:bg-gray-50'}`;
        pageButton.onclick = () => changePage(i);
        pagination.appendChild(pageButton);
    }
    
    // 下一页按钮
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
    nextButton.className = `relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''}`;
    nextButton.onclick = () => currentPage < totalPages && changePage(currentPage + 1);
    pagination.appendChild(nextButton);
}

// 切换页面
function changePage(page) {
    currentPage = page;
    loadUserList();
}

// 显示添加用户模态框
function showAddUserModal() {
    document.getElementById('addUserModal').classList.remove('hidden');
}

// 关闭添加用户模态框
function closeAddUserModal() {
    document.getElementById('addUserModal').classList.add('hidden');
    document.getElementById('addUserForm').reset();
}

// 处理添加用户
async function handleAddUser(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const userData = {
        username: formData.get('username'),
        password: formData.get('password'),
        realName: formData.get('realName'),
        email: formData.get('email'),
        userType: parseInt(formData.get('userType')),
        status: 1
    };
    
    try {
        const response = await axios.post('/api/system/user', userData);
        
        if (response.data.code === 200) {
            notification.success('添加用户成功');
            closeAddUserModal();
            loadUserList();
        } else {
            notification.error(response.data.message || '添加用户失败');
        }
    } catch (error) {
        console.error('添加用户错误:', error);
        notification.error('添加用户失败');
    }
}

// 编辑用户
async function editUser(userId) {
    try {
        const response = await axios.get(`/api/system/user/${userId}`);
        if (response.data.code === 200) {
            const user = response.data.data;
            showEditUserModal(user);
        } else {
            notification.error(response.data.message || '获取用户信息失败');
        }
    } catch (error) {
        console.error('获取用户信息错误:', error);
        notification.error('获取用户信息失败');
    }
}

// 删除用户
async function deleteUser(userId) {
    if (!confirm('确定要删除这个用户吗？')) {
        return;
    }
    
    try {
        const response = await axios.delete(`/api/system/user/${userId}`);
        
        if (response.data.code === 200) {
            notification.success('删除用户成功');
            loadUserList();
        } else {
            notification.error(response.data.message || '删除用户失败');
        }
    } catch (error) {
        console.error('删除用户错误:', error);
        notification.error('删除用户失败');
    }
}

// 显示编辑用户模态框
function showEditUserModal(user) {
    const form = document.getElementById('editUserForm');
    form.elements.id.value = user.id;
    form.elements.username.value = user.username;
    form.elements.password.value = ''; // 密码留空
    form.elements.realName.value = user.realName || '';
    form.elements.email.value = user.email || '';
    form.elements.userType.value = user.userType;
    form.elements.status.value = user.status;

    document.getElementById('editUserModal').classList.remove('hidden');
}

// 关闭编辑用户模态框
function closeEditUserModal() {
    document.getElementById('editUserModal').classList.add('hidden');
    document.getElementById('editUserForm').reset();
}

// 重置用户密码
async function resetUserPassword(userId) {
    if (!confirm('确定要重置该用户的密码吗？重置后密码将变为123456')) {
        return;
    }

    try {
        const response = await axios.put(`/api/system/user/${userId}/reset-password`, '123456', {
            headers: { 'Content-Type': 'text/plain' }
        });

        if (response.data.code === 200) {
            notification.success(response.data.message);
        } else {
            notification.error(response.data.message || '重置密码失败');
        }
    } catch (error) {
        console.error('重置密码错误:', error);
        notification.error('重置密码失败');
    }
}

// 切换用户状态
async function toggleUserStatus(userId, currentStatus) {
    const newStatus = currentStatus === 1 ? 0 : 1;
    const action = newStatus === 1 ? '启用' : '禁用';

    if (!confirm(`确定要${action}该用户吗？`)) {
        return;
    }

    try {
        const response = await axios.put(`/api/system/user/${userId}/status?status=${newStatus}`);

        if (response.data.code === 200) {
            notification.success(response.data.message);
            loadUserList();
        } else {
            notification.error(response.data.message || `${action}用户失败`);
        }
    } catch (error) {
        console.error(`${action}用户错误:`, error);
        notification.error(`${action}用户失败`);
    }
}

// ==================== 角色管理相关函数 ====================

// 加载角色列表
async function loadRoleList() {
    try {
        const searchValue = document.getElementById('roleSearch').value;
        const statusFilter = document.getElementById('roleStatusFilter').value;

        const params = {
            page: currentPage,
            size: pageSize
        };

        if (searchValue) {
            params.roleName = searchValue;
        }

        if (statusFilter) {
            params.status = statusFilter;
        }

        const response = await axios.get('/api/system/roles', { params });

        if (response.data.code === 200) {
            const data = response.data.data;
            renderRoleTable(data.records);
            renderPagination(data);
        } else {
            notification.error(response.data.message || '加载角色列表失败');
        }
    } catch (error) {
        console.error('加载角色列表错误:', error);
        notification.error('加载角色列表失败');
    }
}

// 渲染角色表格
function renderRoleTable(roles) {
    const tbody = document.getElementById('roleTableBody');
    tbody.innerHTML = '';

    roles.forEach(role => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${role.roleName}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${role.roleKey}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${role.sort || 0}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${role.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${role.status === 1 ? '启用' : '禁用'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                ${formatDateTime(role.createTime)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button onclick="editRole(${role.id})" class="text-blue-600 hover:text-blue-900 mr-2">编辑</button>
                <button onclick="toggleRoleStatus(${role.id}, ${role.status})" class="text-${role.status === 1 ? 'orange' : 'green'}-600 hover:text-${role.status === 1 ? 'orange' : 'green'}-900 mr-2">
                    ${role.status === 1 ? '禁用' : '启用'}
                </button>
                <button onclick="deleteRole(${role.id})" class="text-red-600 hover:text-red-900">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 搜索角色
function searchRoles() {
    currentPage = 1;
    loadRoleList();
}

// 显示添加角色模态框
function showAddRoleModal() {
    document.getElementById('addRoleModal').classList.remove('hidden');
}

// 关闭添加角色模态框
function closeAddRoleModal() {
    document.getElementById('addRoleModal').classList.add('hidden');
    document.getElementById('addRoleForm').reset();
}

// 编辑角色
async function editRole(roleId) {
    try {
        const response = await axios.get(`/api/system/roles/${roleId}`);
        if (response.data.code === 200) {
            const role = response.data.data;
            showEditRoleModal(role);
        } else {
            notification.error(response.data.message || '获取角色信息失败');
        }
    } catch (error) {
        console.error('获取角色信息错误:', error);
        notification.error('获取角色信息失败');
    }
}

// 显示编辑角色模态框
function showEditRoleModal(role) {
    const form = document.getElementById('editRoleForm');
    form.elements.id.value = role.id;
    form.elements.roleName.value = role.roleName;
    form.elements.roleKey.value = role.roleKey;
    form.elements.sort.value = role.sort || 0;
    form.elements.status.value = role.status;
    form.elements.remark.value = role.remark || '';

    document.getElementById('editRoleModal').classList.remove('hidden');
}

// 关闭编辑角色模态框
function closeEditRoleModal() {
    document.getElementById('editRoleModal').classList.add('hidden');
    document.getElementById('editRoleForm').reset();
}

// 切换角色状态
async function toggleRoleStatus(roleId, currentStatus) {
    const newStatus = currentStatus === 1 ? 0 : 1;
    const action = newStatus === 1 ? '启用' : '禁用';

    if (!confirm(`确定要${action}该角色吗？`)) {
        return;
    }

    try {
        const response = await axios.put(`/api/system/roles/${roleId}/status?status=${newStatus}`);

        if (response.data.code === 200) {
            notification.success(response.data.message);
            loadRoleList();
        } else {
            notification.error(response.data.message || `${action}角色失败`);
        }
    } catch (error) {
        console.error(`${action}角色错误:`, error);
        notification.error(`${action}角色失败`);
    }
}

// 删除角色
async function deleteRole(roleId) {
    if (!confirm('确定要删除这个角色吗？')) {
        return;
    }

    try {
        const response = await axios.delete(`/api/system/roles/${roleId}`);

        if (response.data.code === 200) {
            notification.success('删除角色成功');
            loadRoleList();
        } else {
            notification.error(response.data.message || '删除角色失败');
        }
    } catch (error) {
        console.error('删除角色错误:', error);
        notification.error('删除角色失败');
    }
}

// 处理添加角色表单提交
async function handleAddRole(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const roleData = {
        roleName: formData.get('roleName'),
        roleKey: formData.get('roleKey'),
        sort: parseInt(formData.get('sort')) || 0,
        remark: formData.get('remark')
    };

    try {
        const response = await axios.post('/api/system/roles', roleData);

        if (response.data.code === 200) {
            notification.success('添加角色成功');
            closeAddRoleModal();
            loadRoleList();
        } else {
            notification.error(response.data.message || '添加角色失败');
        }
    } catch (error) {
        console.error('添加角色错误:', error);
        notification.error('添加角色失败');
    }
}

// 处理编辑角色表单提交
async function handleEditRole(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const roleId = formData.get('id');
    const roleData = {
        roleName: formData.get('roleName'),
        roleKey: formData.get('roleKey'),
        sort: parseInt(formData.get('sort')) || 0,
        status: parseInt(formData.get('status')),
        remark: formData.get('remark')
    };

    try {
        const response = await axios.put(`/api/system/roles/${roleId}`, roleData);

        if (response.data.code === 200) {
            notification.success('更新角色成功');
            closeEditRoleModal();
            loadRoleList();
        } else {
            notification.error(response.data.message || '更新角色失败');
        }
    } catch (error) {
        console.error('更新角色错误:', error);
        notification.error('更新角色失败');
    }
}

// 角色管理相关函数
function showAddRoleModal() {
    console.log('显示添加角色模态框');
    // 这里可以添加实际的模态框显示逻辑
}

function editRole(roleId) {
    console.log('编辑角色:', roleId);
    // 这里可以添加实际的编辑角色逻辑
}

function deleteRole(roleId) {
    if (confirm('确定要删除这个角色吗？')) {
        console.log('删除角色:', roleId);
        // 这里可以添加实际的删除角色逻辑
    }
}

// 权限管理相关函数
function showAddPermissionModal() {
    // 创建模态框HTML
    const modalHTML = `
        <div id="addPermissionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">添加权限</h3>
                    <form id="addPermissionForm">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">权限名称</label>
                            <input type="text" name="permissionName" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">权限代码</label>
                            <input type="text" name="permissionCode" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">权限描述</label>
                            <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeAddPermissionModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">取消</button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">添加</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 绑定表单提交事件
    document.getElementById('addPermissionForm').addEventListener('submit', handleAddPermission);
}

function closeAddPermissionModal() {
    const modal = document.getElementById('addPermissionModal');
    if (modal) {
        modal.remove();
    }
}

function handleAddPermission(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const permissionData = {
        name: formData.get('permissionName'),
        code: formData.get('permissionCode'),
        description: formData.get('description')
    };

    console.log('添加权限:', permissionData);
    // 这里可以添加实际的API调用

    if (window.notification) {
        window.notification.success('权限添加成功');
    }
    closeAddPermissionModal();
    loadPermissionList();
}

function editPermission(permissionId) {
    console.log('编辑权限:', permissionId);
    // 这里可以添加实际的编辑权限逻辑
}

function deletePermission(permissionId) {
    if (confirm('确定要删除这个权限吗？')) {
        console.log('删除权限:', permissionId);
        // 这里可以添加实际的删除权限逻辑
    }
}

// 退出登录
function logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
    if (window.notification) {
        window.notification.success('已安全退出');
    }
    setTimeout(() => {
        window.location.href = '/';
    }, 1000);
}
