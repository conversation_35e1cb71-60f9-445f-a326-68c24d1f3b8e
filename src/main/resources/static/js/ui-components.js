/**
 * 统一的UI组件库
 * 提供项目中所有通用的UI组件和样式
 */

// 全局UI组件对象
window.UIComponents = {
    
    /**
     * 显示成功消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长（毫秒），默认3000
     */
    showSuccessMessage(message, duration = 3000) {
        this.showMessage(message, 'success', duration);
    },
    
    /**
     * 显示错误消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长（毫秒），默认5000
     */
    showErrorMessage(message, duration = 5000) {
        this.showMessage(message, 'error', duration);
    },
    
    /**
     * 显示警告消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长（毫秒），默认4000
     */
    showWarningMessage(message, duration = 4000) {
        this.showMessage(message, 'warning', duration);
    },
    
    /**
     * 显示信息消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长（毫秒），默认3000
     */
    showInfoMessage(message, duration = 3000) {
        this.showMessage(message, 'info', duration);
    },
    
    /**
     * 通用消息显示方法
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型：success, error, warning, info
     * @param {number} duration - 显示时长（毫秒）
     */
    showMessage(message, type = 'info', duration = 3000) {
        // 移除已存在的消息
        const existingMessage = document.getElementById('ui-message');
        if (existingMessage) {
            existingMessage.remove();
        }
        
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.id = 'ui-message';
        messageEl.className = `ui-message ui-message-${type}`;
        
        // 根据类型设置图标
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        messageEl.innerHTML = `
            <div class="ui-message-content">
                <i class="${icons[type]}"></i>
                <span class="ui-message-text">${message}</span>
                <button class="ui-message-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(messageEl);
        
        // 添加显示动画
        setTimeout(() => {
            messageEl.classList.add('ui-message-show');
        }, 10);
        
        // 自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.classList.remove('ui-message-show');
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.remove();
                    }
                }, 300);
            }
        }, duration);
    },
    
    /**
     * 显示确认对话框
     * @param {string} title - 标题
     * @param {string} message - 消息内容
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     * @param {Object} options - 选项
     */
    showConfirmDialog(title, message, onConfirm, onCancel = null, options = {}) {
        const {
            confirmText = '确认',
            cancelText = '取消',
            type = 'warning',
            danger = false
        } = options;
        
        // 移除已存在的对话框
        const existingDialog = document.getElementById('ui-confirm-dialog');
        if (existingDialog) {
            existingDialog.remove();
        }
        
        // 创建对话框
        const dialogEl = document.createElement('div');
        dialogEl.id = 'ui-confirm-dialog';
        dialogEl.className = 'ui-dialog-overlay';
        
        const icons = {
            success: 'fas fa-check-circle text-green-500',
            error: 'fas fa-exclamation-circle text-red-500',
            warning: 'fas fa-exclamation-triangle text-yellow-500',
            info: 'fas fa-info-circle text-blue-500'
        };
        
        const confirmButtonClass = danger ? 'ui-btn-danger' : 'ui-btn-primary';
        
        dialogEl.innerHTML = `
            <div class="ui-dialog">
                <div class="ui-dialog-header">
                    <h3 class="ui-dialog-title">
                        <i class="${icons[type]}"></i>
                        ${title}
                    </h3>
                </div>
                <div class="ui-dialog-body">
                    <p class="ui-dialog-message">${message}</p>
                </div>
                <div class="ui-dialog-footer">
                    <button class="ui-btn ui-btn-secondary" onclick="UIComponents.closeConfirmDialog(false)">
                        ${cancelText}
                    </button>
                    <button class="ui-btn ${confirmButtonClass}" onclick="UIComponents.closeConfirmDialog(true)">
                        ${confirmText}
                    </button>
                </div>
            </div>
        `;
        
        // 保存回调函数
        this._confirmCallback = onConfirm;
        this._cancelCallback = onCancel;
        
        // 添加到页面
        document.body.appendChild(dialogEl);
        
        // 添加显示动画
        setTimeout(() => {
            dialogEl.classList.add('ui-dialog-show');
        }, 10);
        
        // 点击遮罩关闭
        dialogEl.addEventListener('click', (e) => {
            if (e.target === dialogEl) {
                this.closeConfirmDialog(false);
            }
        });
    },
    
    /**
     * 关闭确认对话框
     * @param {boolean} confirmed - 是否确认
     */
    closeConfirmDialog(confirmed) {
        const dialogEl = document.getElementById('ui-confirm-dialog');
        if (dialogEl) {
            dialogEl.classList.remove('ui-dialog-show');
            setTimeout(() => {
                if (dialogEl.parentNode) {
                    dialogEl.remove();
                }
            }, 300);
        }
        
        // 执行回调
        if (confirmed && this._confirmCallback) {
            this._confirmCallback();
        } else if (!confirmed && this._cancelCallback) {
            this._cancelCallback();
        }
        
        // 清除回调
        this._confirmCallback = null;
        this._cancelCallback = null;
    },
    
    /**
     * 生成暂无数据的HTML
     * @param {string} message - 提示消息，默认"暂无数据"
     * @param {string} icon - 图标类名，默认"fas fa-inbox"
     * @param {string} description - 描述文字
     * @param {number} colspan - 表格列数
     * @returns {string} HTML字符串
     */
    generateEmptyState(message = '暂无数据', icon = 'fas fa-inbox', description = '', colspan = 5) {
        return `
            <tr>
                <td colspan="${colspan}" class="ui-empty-state">
                    <div class="ui-empty-content">
                        <div class="ui-empty-icon">
                            <i class="${icon}"></i>
                        </div>
                        <div class="ui-empty-text">
                            <h3 class="ui-empty-title">${message}</h3>
                            ${description ? `<p class="ui-empty-description">${description}</p>` : ''}
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }
};

// 全局快捷方法
window.showSuccessMessage = (message, duration) => UIComponents.showSuccessMessage(message, duration);
window.showErrorMessage = (message, duration) => UIComponents.showErrorMessage(message, duration);
window.showWarningMessage = (message, duration) => UIComponents.showWarningMessage(message, duration);
window.showInfoMessage = (message, duration) => UIComponents.showInfoMessage(message, duration);
window.showConfirmDialog = (title, message, onConfirm, onCancel, options) => 
    UIComponents.showConfirmDialog(title, message, onConfirm, onCancel, options);
