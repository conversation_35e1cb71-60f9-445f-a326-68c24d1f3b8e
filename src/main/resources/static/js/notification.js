/**
 * 统一提示窗口组件
 * 支持成功、错误、警告、信息四种类型的提示
 */
class Notification {
    constructor() {
        this.container = null;
        this.init();
    }

    init() {
        // 创建提示容器
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'fixed top-4 right-4 z-50 space-y-2';
        document.body.appendChild(this.container);
    }

    /**
     * 显示提示
     * @param {string} message - 提示消息
     * @param {string} type - 提示类型：success, error, warning, info
     * @param {number} duration - 显示时长（毫秒），默认3000ms
     */
    show(message, type = 'info', duration = 3000) {
        const notification = this.createNotification(message, type);
        this.container.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                this.hide(notification);
            }, duration);
        }

        return notification;
    }

    /**
     * 创建提示元素
     */
    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type} transform translate-x-full transition-all duration-300 ease-in-out`;
        
        const config = this.getTypeConfig(type);
        
        notification.innerHTML = `
            <div class="flex items-center p-4 bg-white rounded-lg shadow-lg border-l-4 ${config.borderColor} max-w-sm">
                <div class="flex-shrink-0">
                    <div class="w-6 h-6 ${config.bgColor} rounded-full flex items-center justify-center">
                        ${config.icon}
                    </div>
                </div>
                <div class="ml-3 flex-1">
                    <p class="text-sm font-medium text-gray-900">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button class="close-btn inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition ease-in-out duration-150">
                        <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;

        // 添加关闭事件
        const closeBtn = notification.querySelector('.close-btn');
        closeBtn.addEventListener('click', () => {
            this.hide(notification);
        });

        return notification;
    }

    /**
     * 获取类型配置
     */
    getTypeConfig(type) {
        const configs = {
            success: {
                borderColor: 'border-green-400',
                bgColor: 'bg-green-100',
                icon: '<svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>'
            },
            error: {
                borderColor: 'border-red-400',
                bgColor: 'bg-red-100',
                icon: '<svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>'
            },
            warning: {
                borderColor: 'border-yellow-400',
                bgColor: 'bg-yellow-100',
                icon: '<svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>'
            },
            info: {
                borderColor: 'border-blue-400',
                bgColor: 'bg-blue-100',
                icon: '<svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
            }
        };
        return configs[type] || configs.info;
    }

    /**
     * 隐藏提示
     */
    hide(notification) {
        notification.classList.remove('show');
        notification.classList.add('translate-x-full');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /**
     * 成功提示
     */
    success(message, duration = 3000) {
        return this.show(message, 'success', duration);
    }

    /**
     * 错误提示
     */
    error(message, duration = 5000) {
        return this.show(message, 'error', duration);
    }

    /**
     * 警告提示
     */
    warning(message, duration = 4000) {
        return this.show(message, 'warning', duration);
    }

    /**
     * 信息提示
     */
    info(message, duration = 3000) {
        return this.show(message, 'info', duration);
    }
}

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
    .notification.show {
        transform: translateX(0) !important;
    }
    
    .notification {
        transition: all 0.3s ease-in-out;
    }
`;
document.head.appendChild(style);

// 创建全局实例
window.notification = new Notification();

// 兼容性方法
window.showNotification = (message, type, duration) => {
    return window.notification.show(message, type, duration);
};
