/* 系统管理页面布局修复 - 最高优先级样式 */

/* 强制主内容区域布局 */
.content,
div.content,
div[class="content p-8"],
div[class*="content"] {
    margin-left: 250px !important;
    margin-top: 64px !important;
    padding-left: 0 !important;
    min-height: calc(100vh - 64px) !important;
    transition: margin-left 0.3s ease !important;
    box-sizing: border-box !important;
}

/* 确保侧边栏正确定位 */
#sidebar {
    position: fixed !important;
    top: 64px !important;
    left: 0 !important;
    width: 250px !important;
    height: calc(100vh - 64px) !important;
    z-index: 1000 !important;
    background-color: #f9fafb !important;
    border-right: 1px solid #e5e7eb !important;
    overflow-y: auto !important;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1) !important;
}

/* 菜单项样式 */
#sidebar .menu-item {
    display: flex !important;
    align-items: center !important;
    padding: 12px 16px !important;
    color: #6b7280 !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
    border-right: 4px solid transparent !important;
}

#sidebar .menu-item:hover {
    background-color: #f3f4f6 !important;
    color: #374151 !important;
}

#sidebar .menu-item.active {
    background-color: #eff6ff !important;
    color: #2563eb !important;
    border-right-color: #2563eb !important;
}

#sidebar .menu-item i {
    width: 24px !important;
    margin-right: 12px !important;
    text-align: center !important;
}

/* 覆盖任何可能的内联样式 */
body div[style*="margin-left"],
body div[style*="margin-top"] {
    margin-left: 250px !important;
    margin-top: 64px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .content,
    div.content,
    div[class="content p-8"],
    div[class*="content"] {
        margin-left: 200px !important;
    }
    
    #sidebar {
        width: 200px !important;
    }
    
    body div[style*="margin-left"] {
        margin-left: 200px !important;
    }
}

@media (max-width: 640px) {
    .content,
    div.content,
    div[class="content p-8"],
    div[class*="content"] {
        margin-left: 0 !important;
    }
    
    #sidebar {
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease !important;
    }
    
    #sidebar.open {
        transform: translateX(0) !important;
    }
    
    body div[style*="margin-left"] {
        margin-left: 0 !important;
    }
}

/* 确保页面内容不被遮挡 */
body {
    overflow-x: hidden !important;
}

/* 修复可能的z-index冲突 */
.content * {
    position: relative !important;
    z-index: 1 !important;
}

#sidebar {
    z-index: 1000 !important;
}

/* 头部栏确保在最上层 */
header,
.header,
#header {
    z-index: 1001 !important;
}
