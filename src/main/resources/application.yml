server:
  port: 8888
  servlet:
    context-path: /

spring:
  application:
    name: sleep-management-system
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************
    username: root
    password:
    
  # Thymeleaf配置
  thymeleaf:
    mode: HTML
    encoding: UTF-8
    cache: false
    prefix: classpath:/templates/
    suffix: .html
    
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: none
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.sleephome.entity
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: sleepHomeManagementSystemSecretKey2024
  expiration: 86400000 # 24小时

# 日志配置
logging:
  level:
    com.sleephome: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
