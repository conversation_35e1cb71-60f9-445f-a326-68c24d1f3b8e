<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${userRole == 'doctor' ? '医生工作台' : (userRole == 'community' ? '社区管理台' : '系统管理台')}">工作台</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="/js/notification.js"></script>
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-light: #dbeafe;
            --primary-dark: #2563eb;
        }
        
        .theme-doctor {
            --primary-color: #10b981;
            --primary-light: #d1fae5;
            --primary-dark: #059669;
        }
        
        .theme-community {
            --primary-color: #3b82f6;
            --primary-light: #dbeafe;
            --primary-dark: #2563eb;
        }
        
        .theme-system {
            --primary-color: #8b5cf6;
            --primary-light: #ede9fe;
            --primary-dark: #7c3aed;
        }
        
        .sidebar {
            width: 250px;
            min-height: calc(100vh - 64px);
        }
        
        .content {
            margin-left: 250px;
            min-height: calc(100vh - 64px);
        }
        
        .menu-item {
            transition: all 0.2s ease;
        }
        
        .menu-item:hover {
            background-color: #f3f4f6;
        }
        
        .menu-item.active {
            background-color: var(--primary-light);
            border-left: 4px solid var(--primary-color);
            color: var(--primary-dark);
        }
        
        .stat-card {
            transition: transform 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-gray-100" th:classappend="${userRole == 'doctor' ? 'theme-doctor' : (userRole == 'community' ? 'theme-community' : 'theme-system')}">
    <!-- 头部栏 -->
    <div class="fixed top-0 left-0 right-0 h-16 bg-white shadow flex items-center justify-between z-40 px-6 w-full border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <img src="/images/logo.png" alt="logo" class="h-10 w-auto">
            <div class="flex items-center">
                <span class="text-xl font-bold text-gray-800">居家睡眠远程管理平台</span>
                <span th:class="${'ml-2 px-2 py-0.5 rounded-full text-sm font-medium ' +
                    (userRole == 'doctor' ? 'bg-green-100 text-green-600' :
                     userRole == 'community' ? 'bg-purple-100 text-purple-600' :
                     'bg-purple-100 text-purple-600')}"
                     th:text="${userRole == 'doctor' ? '医生端' : (userRole == 'community' ? '社区端' : '系统管理')}">
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <!-- 通知图标 -->
            <div class="relative">
                <button class="text-gray-500 hover:text-gray-700 relative">
                    <i class="fas fa-bell text-xl"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
                </button>
            </div>
            <!-- 用户信息 -->
            <div class="flex items-center space-x-2">
                <i th:class="${userRole == 'doctor' ? 'fas fa-user-circle text-2xl text-green-700' :
                         userRole == 'community' ? 'fas fa-user-circle text-2xl text-purple-700' :
                         'fas fa-user-circle text-2xl text-purple-700'}"></i>
                <span class="text-sm font-medium text-gray-700" id="currentUser">用户</span>
            </div>
            <!-- 退出按钮 -->
            <button onclick="logout()" class="text-gray-500 hover:text-red-600 transition-colors">
                <i class="fas fa-sign-out-alt"></i>
                <span class="ml-1">退出</span>
            </button>
        </div>
    </div>
    
    <!-- 左侧菜单栏 -->
    <div class="sidebar fixed top-16 left-0 bg-gray-50 shadow-lg z-30 border-t border-gray-200">
        <nav class="mt-4" id="sidebarNav">
            <!-- 菜单将通过JavaScript动态生成 -->
        </nav>
    </div>

    <!-- 右侧内容区 -->
    <div class="content p-8" style="margin-top:64px;">
        <!-- 欢迎信息 -->
        <div class="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div th:class="${'w-12 h-12 rounded-full flex items-center justify-center ' +
                            (userRole == 'doctor' ? 'bg-green-100' :
                             userRole == 'community' ? 'bg-blue-100' :
                             'bg-purple-100')}">
                            <i th:class="${'text-xl ' +
                                (userRole == 'doctor' ? 'fas fa-user-md text-green-600' :
                                 userRole == 'community' ? 'fas fa-users text-blue-600' :
                                 'fas fa-cog text-purple-600')}"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-gray-900">欢迎回来，<span id="welcomeName">用户</span>！</h1>
                        <p class="text-gray-600">今天是 <span id="currentDate"></span>，祝您工作愉快！</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6" id="statsCards">
            <!-- 统计卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 快捷操作 -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">快捷操作</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4" id="quickActions">
                    <!-- 快捷操作将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">最近活动</h3>
                <div class="space-y-4" id="recentActivities">
                    <!-- 最近活动将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        let userRole = '[[${userRole}]]'; // Thymeleaf变量传递给JavaScript
        
        // 检查登录状态并设置用户信息
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/';
                return false;
            }
            
            try {
                const user = JSON.parse(userInfo);
                document.getElementById('currentUser').textContent = user.realName || user.username;
                document.getElementById('welcomeName').textContent = user.realName || user.username;
                
                // 根据用户类型设置userRole
                if (user.userType === 1) {
                    userRole = 'doctor';
                } else if (user.userType === 2) {
                    userRole = 'community';
                } else {
                    userRole = 'system';
                }
                
                // 设置axios默认header
                axios.defaults.headers.common['Authorization'] = 'Bearer ' + token;
                
                return true;
            } catch (error) {
                console.error('解析用户信息失败:', error);
                window.location.href = '/';
                return false;
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            if (window.notification) {
                window.notification.success('已安全退出');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } else {
                window.location.href = '/';
            }
        }

        // 设置当前日期
        function setCurrentDate() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long'
            };
            document.getElementById('currentDate').textContent = now.toLocaleDateString('zh-CN', options);
        }

        // 根据用户角色生成菜单
        function generateMenu() {
            const sidebarNav = document.getElementById('sidebarNav');
            let menuItems = [];

            // 首页菜单（所有角色都有）
            menuItems.push({
                href: '/dashboard',
                id: 'menu-dashboard',
                icon: 'fas fa-home',
                text: '首页',
                active: true
            });

            // 根据角色添加不同菜单
            if (userRole === 'doctor' || userRole === 'community') {
                menuItems.push(
                    {
                        href: '/patients',
                        id: 'menu-patients',
                        icon: 'fas fa-users',
                        text: userRole === 'doctor' ? '患者管理' : '患者管理'
                    },
                    {
                        href: '/questionnaires',
                        id: 'menu-questionnaires',
                        icon: 'fas fa-clipboard-list',
                        text: '问卷管理'
                    },
                    {
                        href: '/reports',
                        id: 'menu-reports',
                        icon: 'fas fa-file-medical',
                        text: '报告管理'
                    },
                    {
                        href: '/messages',
                        id: 'menu-messages',
                        icon: 'fas fa-envelope',
                        text: '消息管理'
                    }
                );
            }

            // 系统管理菜单（所有角色都有，但权限在后端控制）
            menuItems.push({
                href: '/system',
                id: 'menu-system',
                icon: 'fas fa-cog',
                text: '系统管理'
            });

            // 生成菜单HTML
            let menuHTML = '';
            menuItems.forEach(item => {
                if (item.separator) {
                    menuHTML += '<div class="border-t border-gray-200 mt-4 pt-4">';
                }

                const activeClass = item.active ? 'menu-item active flex items-center px-4 py-3' : 'menu-item flex items-center px-4 py-3 text-gray-600';

                menuHTML += `
                    <a href="${item.href}" id="${item.id}" class="${activeClass}">
                        <i class="${item.icon} w-6"></i>
                        <span>${item.text}</span>
                    </a>
                `;

                if (item.separator) {
                    menuHTML += '</div>';
                }
            });

            sidebarNav.innerHTML = menuHTML;
        }

        // 根据用户角色生成统计卡片
        function generateStatsCards() {
            const statsCards = document.getElementById('statsCards');
            let cards = [];
            
            if (userRole === 'doctor') {
                cards = [
                    { icon: 'fas fa-users', color: 'blue', title: '管理患者', value: '156' },
                    { icon: 'fas fa-file-medical', color: 'green', title: '待处理报告', value: '23' },
                    { icon: 'fas fa-clipboard-list', color: 'yellow', title: '本月问卷', value: '89' },
                    { icon: 'fas fa-chart-line', color: 'purple', title: '改善率', value: '78%' }
                ];
            } else if (userRole === 'community') {
                cards = [
                    { icon: 'fas fa-home', color: 'blue', title: '社区居民', value: '1,234' },
                    { icon: 'fas fa-user-check', color: 'green', title: '参与用户', value: '456' },
                    { icon: 'fas fa-clipboard-check', color: 'yellow', title: '本月问卷', value: '234' },
                    { icon: 'fas fa-percentage', color: 'purple', title: '参与率', value: '67%' }
                ];
            } else {
                cards = [
                    { icon: 'fas fa-users', color: 'blue', title: '系统用户', value: '89' },
                    { icon: 'fas fa-shield-alt', color: 'green', title: '在线用户', value: '23' },
                    { icon: 'fas fa-database', color: 'yellow', title: '数据量', value: '2.3GB' },
                    { icon: 'fas fa-server', color: 'purple', title: '系统负载', value: '45%' }
                ];
            }
            
            statsCards.innerHTML = cards.map(card => `
                <div class="bg-white overflow-hidden shadow rounded-lg stat-card">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-${card.color}-100 rounded-full flex items-center justify-center">
                                    <i class="${card.icon} text-${card.color}-600"></i>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">${card.title}</dt>
                                    <dd class="text-lg font-medium text-gray-900">${card.value}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 根据用户角色生成快捷操作
        function generateQuickActions() {
            const quickActions = document.getElementById('quickActions');
            let actions = [];
            
            if (userRole === 'doctor') {
                actions = [
                    { icon: 'fas fa-user-plus', color: 'blue', title: '添加患者' },
                    { icon: 'fas fa-file-medical-alt', color: 'green', title: '查看报告' },
                    { icon: 'fas fa-edit', color: 'yellow', title: '创建问卷' },
                    { icon: 'fas fa-chart-bar', color: 'purple', title: '数据分析' }
                ];
            } else if (userRole === 'community') {
                actions = [
                    { icon: 'fas fa-user-plus', color: 'blue', title: '添加居民' },
                    { icon: 'fas fa-bullhorn', color: 'green', title: '发布通知' },
                    { icon: 'fas fa-calendar-alt', color: 'yellow', title: '活动安排' },
                    { icon: 'fas fa-chart-pie', color: 'purple', title: '统计报表' }
                ];
            } else {
                actions = [
                    { icon: 'fas fa-user-plus', color: 'blue', title: '添加用户' },
                    { icon: 'fas fa-shield-alt', color: 'green', title: '权限管理' },
                    { icon: 'fas fa-cog', color: 'yellow', title: '系统配置' },
                    { icon: 'fas fa-chart-line', color: 'purple', title: '系统监控' }
                ];
            }
            
            quickActions.innerHTML = actions.map(action => `
                <button class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="w-12 h-12 bg-${action.color}-100 rounded-full flex items-center justify-center mb-2">
                        <i class="${action.icon} text-${action.color}-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-900">${action.title}</span>
                </button>
            `).join('');
        }

        // 生成最近活动
        function generateRecentActivities() {
            const recentActivities = document.getElementById('recentActivities');
            let activities = [];
            
            if (userRole === 'doctor') {
                activities = [
                    { color: 'green', text: '患者张三提交了睡眠质量问卷', time: '2小时前' },
                    { color: 'blue', text: '新增患者李四的睡眠监测报告', time: '4小时前' },
                    { color: 'yellow', text: '患者王五的治疗方案需要调整', time: '6小时前' },
                    { color: 'purple', text: '完成了本周的数据统计分析', time: '1天前' }
                ];
            } else if (userRole === 'community') {
                activities = [
                    { color: 'green', text: '新增居民王阿姨完成了健康档案建立', time: '1小时前' },
                    { color: 'blue', text: '发布了"睡眠健康知识讲座"活动通知', time: '3小时前' },
                    { color: 'yellow', text: '李大爷提交了本周睡眠质量反馈', time: '5小时前' },
                    { color: 'purple', text: '完成了本月社区健康数据统计', time: '1天前' }
                ];
            } else {
                activities = [
                    { color: 'green', text: '新增系统用户"张医生"', time: '30分钟前' },
                    { color: 'blue', text: '系统备份任务执行成功', time: '2小时前' },
                    { color: 'yellow', text: '用户权限配置已更新', time: '4小时前' },
                    { color: 'purple', text: '系统性能监控报告生成完成', time: '1天前' }
                ];
            }
            
            recentActivities.innerHTML = activities.map(activity => `
                <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 bg-${activity.color}-400 rounded-full"></div>
                    <span class="text-sm text-gray-600">${activity.text}</span>
                    <span class="text-xs text-gray-400">${activity.time}</span>
                </div>
            `).join('');
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                generateMenu();
                setCurrentDate();
                generateStatsCards();
                generateQuickActions();
                generateRecentActivities();
            }
        });
    </script>
</body>
</html>
