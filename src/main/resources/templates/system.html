<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${userRole == 'doctor' ? '系统管理 - 医生工作台' : (userRole == 'community' ? '系统管理 - 社区管理台' : '系统管理 - 系统管理台')}">系统管理</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .sidebar {
            width: 250px;
            min-height: 100vh;
        }
        .content {
            margin-left: 250px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .theme-doctor {
            --primary-color: #10b981;
            --primary-light: #d1fae5;
        }
        .theme-community {
            --primary-color: #8b5cf6;
            --primary-light: #ede9fe;
        }
        .theme-system {
            --primary-color: #8b5cf6;
            --primary-light: #ede9fe;
        }
    </style>
</head>
<body class="bg-gray-100" th:classappend="${userRole == 'doctor' ? 'theme-doctor' : (userRole == 'community' ? 'theme-community' : 'theme-system')}">
    <!-- 头部栏 -->
    <div class="fixed top-0 left-0 right-0 h-16 bg-white shadow flex items-center justify-between z-40 px-6 w-full border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <img src="/images/logo.png" alt="logo" class="h-10 w-auto">
            <div class="flex items-center">
                <span class="text-xl font-bold text-gray-800">居家睡眠远程管理平台</span>
                <span th:class="${'ml-2 px-2 py-0.5 rounded-full text-sm font-medium ' +
                    (userRole == 'doctor' ? 'bg-green-100 text-green-600' :
                     userRole == 'community' ? 'bg-purple-100 text-purple-600' :
                     'bg-purple-100 text-purple-600')}"
                     th:text="${userRole == 'doctor' ? '医生端' : (userRole == 'community' ? '社区端' : '系统管理')}">
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <!-- 通知图标 -->
            <div class="relative">
                <button class="text-gray-500 hover:text-gray-700 relative">
                    <i class="fas fa-bell text-xl"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
                </button>
            </div>
            <!-- 用户信息 -->
            <div class="flex items-center space-x-2">
                <i th:class="${userRole == 'doctor' ? 'fas fa-user-circle text-2xl text-green-700' :
                         userRole == 'community' ? 'fas fa-user-circle text-2xl text-purple-700' :
                         'fas fa-user-circle text-2xl text-purple-700'}"></i>
                <span class="text-sm font-medium text-gray-700" id="currentUser">用户</span>
            </div>
            <!-- 退出按钮 -->
            <button onclick="logout()" class="text-gray-500 hover:text-red-600 transition-colors">
                <i class="fas fa-sign-out-alt"></i>
                <span class="ml-1">退出</span>
            </button>
        </div>
    </div>
    
    <!-- 左侧菜单栏 -->
    <div class="sidebar fixed top-16 left-0 bg-gray-50 shadow-lg z-30 border-t border-gray-200">
        <nav class="mt-4" id="sidebarNav">
            <!-- 菜单将通过JavaScript动态生成 -->
        </nav>
    </div>

    <!-- 右侧内容区 -->
    <div class="content p-8" style="margin-top:64px;">
        <!-- 系统管理导航 -->
        <div class="mb-6">
            <nav class="flex space-x-4" aria-label="Tabs">
                <a href="#" onclick="switchTab('users')" id="tab-users" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">用户管理</a>
                <a href="#" onclick="switchTab('roles')" id="tab-roles" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">角色管理</a>
                <a href="#" onclick="switchTab('permissions')" id="tab-permissions" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">权限管理</a>
                <a href="#" onclick="switchTab('logs')" id="tab-logs" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">操作日志</a>
            </nav>
        </div>

        <!-- 用户管理内容 -->
        <div id="content-users" class="tab-content active">
            <div class="bg-white rounded-lg shadow">
                <!-- 搜索和操作栏 -->
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-4">
                            <div class="relative">
                                <input type="text" id="userSearch" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="搜索用户...">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                            <select id="roleFilter" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                                <option value="">所有角色</option>
                                <option value="admin">管理员</option>
                                <option value="doctor">医生</option>
                                <option value="community">社区管理员</option>
                            </select>
                        </div>
                        <button onclick="showAddUserModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <i class="fas fa-plus mr-2"></i>添加用户
                        </button>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户信息</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后登录</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 用户数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
                        <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700" id="userPaginationInfo">
                                显示第 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">97</span> 条记录
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination" id="userPagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 角色管理内容 -->
        <div id="content-roles" class="tab-content">
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">角色管理</h3>
                        <button onclick="showAddRoleModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <i class="fas fa-plus mr-2"></i>添加角色
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div id="rolesList" class="space-y-4">
                        <!-- 角色列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限管理内容 -->
        <div id="content-permissions" class="tab-content">
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">权限管理</h3>
                        <button onclick="showAddPermissionModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <i class="fas fa-plus mr-2"></i>添加权限
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div id="permissionsList" class="space-y-4">
                        <!-- 权限列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作日志内容 -->
        <div id="content-logs" class="tab-content">
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">操作日志</h3>
                        <div class="flex space-x-4">
                            <input type="date" id="logDateFrom" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <input type="date" id="logDateTo" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <button onclick="searchLogs()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">
                                <i class="fas fa-search mr-2"></i>查询
                            </button>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作用户</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作描述</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                            </tr>
                        </thead>
                        <tbody id="logsTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 日志数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        let userRole = 'admin'; // 默认角色，将从后端获取
        let username = '管理员'; // 默认用户名，将从后端获取
        let currentTab = 'users';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });

        function initializePage() {
            // 获取用户信息
            getUserInfo();
            // 生成菜单
            generateMenu();
            // 加载用户数据
            loadUsers();
        }

        // 获取用户信息
        function getUserInfo() {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/doctor/login';
                return;
            }

            // 设置axios默认header
            axios.defaults.headers.common['Authorization'] = 'Bearer ' + token;

            // 获取当前用户信息
            axios.get('/api/auth/me')
                .then(response => {
                    const user = response.data;
                    userRole = user.role || 'admin';
                    username = user.username || '管理员';
                    document.getElementById('currentUser').textContent = username;
                })
                .catch(error => {
                    console.error('获取用户信息失败:', error);
                    if (error.response && error.response.status === 401) {
                        localStorage.removeItem('token');
                        window.location.href = '/doctor/login';
                    }
                });
        }

        // 根据用户角色生成菜单
        function generateMenu() {
            const sidebarNav = document.getElementById('sidebarNav');
            let menuItems = [];

            // 首页菜单（所有角色都有）
            menuItems.push({
                href: '/dashboard',
                id: 'menu-dashboard',
                icon: 'fas fa-home',
                text: '首页'
            });

            // 根据角色添加不同菜单
            if (userRole === 'doctor' || userRole === 'community') {
                menuItems.push(
                    {
                        href: '/patients',
                        id: 'menu-patients',
                        icon: 'fas fa-users',
                        text: '患者管理'
                    },
                    {
                        href: '/questionnaires',
                        id: 'menu-questionnaires',
                        icon: 'fas fa-clipboard-list',
                        text: '问卷管理'
                    },
                    {
                        href: '/reports',
                        id: 'menu-reports',
                        icon: 'fas fa-file-medical',
                        text: '报告管理'
                    },
                    {
                        href: '/messages',
                        id: 'menu-messages',
                        icon: 'fas fa-envelope',
                        text: '消息管理'
                    }
                );
            }

            // 系统管理菜单（所有角色都有，但权限在后端控制）
            menuItems.push({
                href: '/system',
                id: 'menu-system',
                icon: 'fas fa-cog',
                text: '系统管理'
            });

            // 生成菜单HTML
            let menuHTML = '';
            menuItems.forEach(item => {
                const isActive = item.href === '/system';
                menuHTML += `
                    <a href="${item.href}" id="${item.id}" class="flex items-center px-4 py-3 ${isActive ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600' : 'text-gray-600 hover:bg-gray-100'}">
                        <i class="${item.icon} w-6"></i>
                        <span>${item.text}</span>
                    </a>
                `;
            });

            sidebarNav.innerHTML = menuHTML;
        }

        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的活动状态
            document.querySelectorAll('[id^="tab-"]').forEach(tab => {
                tab.className = 'text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium';
            });

            // 显示选中的标签内容
            document.getElementById(`content-${tabName}`).classList.add('active');
            
            // 设置选中标签的活动状态
            document.getElementById(`tab-${tabName}`).className = 'bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium';

            currentTab = tabName;

            // 根据标签加载相应数据
            switch(tabName) {
                case 'users':
                    loadUsers();
                    break;
                case 'roles':
                    loadRoles();
                    break;
                case 'permissions':
                    loadPermissions();
                    break;
                case 'logs':
                    loadLogs();
                    break;
            }
        }

        // 加载用户数据
        function loadUsers() {
            // TODO: 实现用户数据加载
            console.log('加载用户数据');
        }

        // 加载角色数据
        function loadRoles() {
            // TODO: 实现角色数据加载
            console.log('加载角色数据');
        }

        // 加载权限数据
        function loadPermissions() {
            // TODO: 实现权限数据加载
            console.log('加载权限数据');
        }

        // 加载日志数据
        function loadLogs() {
            // TODO: 实现日志数据加载
            console.log('加载日志数据');
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            window.location.href = '/doctor/login';
        }

        // 模态框相关函数（待实现）
        function showAddUserModal() {
            console.log('显示添加用户模态框');
        }

        function showAddRoleModal() {
            console.log('显示添加角色模态框');
        }

        function showAddPermissionModal() {
            console.log('显示添加权限模态框');
        }

        function searchLogs() {
            console.log('搜索日志');
        }
    </script>
</body>
</html>
