package com.sleephome.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sleephome.common.result.Result;
import com.sleephome.entity.SysRole;
import com.sleephome.service.SysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 角色管理控制器
 */
@RestController
@RequestMapping("/api/system/role")
public class SysRoleController {
    
    @Autowired
    private SysRoleService roleService;
    
    /**
     * 分页查询角色列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<IPage<SysRole>> list(@RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "10") Integer pageSize,
                                       SysRole role) {
        Page<SysRole> page = new Page<>(pageNum, pageSize);
        IPage<SysRole> rolePage = roleService.selectRolePage(page, role);
        return Result.success(rolePage);
    }
    
    /**
     * 查询所有角色列表
     */
    @GetMapping("/listAll")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<List<SysRole>> listAll() {
        List<SysRole> roles = roleService.selectRoleAll();
        return Result.success(roles);
    }
    
    /**
     * 根据角色ID查询角色信息
     */
    @GetMapping("/{roleId}")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<SysRole> getInfo(@PathVariable Long roleId) {
        SysRole role = roleService.getById(roleId);
        return Result.success(role);
    }
    
    /**
     * 新增角色
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:role:add')")
    public Result<Void> add(@Valid @RequestBody SysRole role) {
        roleService.insertRole(role);
        return Result.success("新增角色成功");
    }
    
    /**
     * 修改角色
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:role:edit')")
    public Result<Void> edit(@Valid @RequestBody SysRole role) {
        roleService.updateRole(role);
        return Result.success("修改角色成功");
    }
    
    /**
     * 删除角色
     */
    @DeleteMapping("/{roleIds}")
    @PreAuthorize("hasAuthority('system:role:remove')")
    public Result<Void> remove(@PathVariable Long[] roleIds) {
        roleService.deleteRoleByIds(roleIds);
        return Result.success("删除角色成功");
    }
    
    /**
     * 检查角色名称是否唯一
     */
    @GetMapping("/checkRoleNameUnique")
    public Result<Boolean> checkRoleNameUnique(@RequestParam String roleName, 
                                               @RequestParam(required = false) Long roleId) {
        boolean unique = roleService.checkRoleNameUnique(roleName, roleId);
        return Result.success(unique);
    }
    
    /**
     * 检查角色权限字符串是否唯一
     */
    @GetMapping("/checkRoleKeyUnique")
    public Result<Boolean> checkRoleKeyUnique(@RequestParam String roleKey, 
                                              @RequestParam(required = false) Long roleId) {
        boolean unique = roleService.checkRoleKeyUnique(roleKey, roleId);
        return Result.success(unique);
    }
}
