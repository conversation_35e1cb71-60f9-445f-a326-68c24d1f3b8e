package com.sleephome.controller;

import com.sleephome.common.result.Result;
import com.sleephome.dto.LoginRequest;
import com.sleephome.dto.LoginResponse;
import com.sleephome.mapper.SysUserMapper;
import com.sleephome.security.jwt.JwtUtils;
import com.sleephome.security.service.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private SysUserMapper userMapper;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword()));
        
        SecurityContextHolder.getContext().setAuthentication(authentication);
        String jwt = jwtUtils.generateJwtToken(loginRequest.getUsername());
        
        UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
        List<String> roles = userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        
        // 获取用户权限
        List<String> permissions = userMapper.selectUserPermissions(userPrincipal.getId());
        
        LoginResponse loginResponse = new LoginResponse(jwt,
                userPrincipal.getId(),
                userPrincipal.getUsername(),
                userPrincipal.getRealName(),
                userPrincipal.getEmail(),
                userPrincipal.getUserType(),
                roles,
                permissions);
        
        return Result.success(loginResponse);
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<Void> logoutUser() {
        SecurityContextHolder.clearContext();
        return Result.success();
    }
}
