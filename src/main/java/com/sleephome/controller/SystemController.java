package com.sleephome.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 系统管理页面控制器
 */
@Controller
@RequestMapping("/system")
public class SystemController {

    /**
     * 系统管理主页面
     */
    @GetMapping
    public String systemPage(Model model) {
        // 这里可以添加一些默认的模型数据
        model.addAttribute("userRole", "admin"); // 默认角色，实际应该从认证信息中获取
        model.addAttribute("username", "管理员"); // 默认用户名，实际应该从认证信息中获取
        return "system";
    }
}
