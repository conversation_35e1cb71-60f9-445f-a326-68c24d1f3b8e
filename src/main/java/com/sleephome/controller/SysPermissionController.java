package com.sleephome.controller;

import com.sleephome.common.result.Result;
import com.sleephome.entity.SysPermission;
import com.sleephome.service.SysPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 权限管理控制器
 */
@RestController
@RequestMapping("/api/system/permission")
public class SysPermissionController {
    
    @Autowired
    private SysPermissionService permissionService;
    
    /**
     * 查询权限列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<List<SysPermission>> list(SysPermission permission) {
        List<SysPermission> permissions = permissionService.selectPermissionList(permission);
        return Result.success(permissions);
    }
    
    /**
     * 查询权限树结构
     */
    @GetMapping("/treeselect")
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<List<SysPermission>> treeselect(SysPermission permission) {
        List<SysPermission> permissions = permissionService.selectPermissionList(permission);
        List<SysPermission> tree = permissionService.buildPermissionTree(permissions);
        return Result.success(tree);
    }
    
    /**
     * 根据权限ID查询权限信息
     */
    @GetMapping("/{permissionId}")
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<SysPermission> getInfo(@PathVariable Long permissionId) {
        SysPermission permission = permissionService.getById(permissionId);
        return Result.success(permission);
    }
    
    /**
     * 新增权限
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:permission:add')")
    public Result<Void> add(@Valid @RequestBody SysPermission permission) {
        permissionService.insertPermission(permission);
        return Result.success();
    }
    
    /**
     * 修改权限
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:permission:edit')")
    public Result<Void> edit(@Valid @RequestBody SysPermission permission) {
        permissionService.updatePermission(permission);
        return Result.success();
    }
    
    /**
     * 删除权限
     */
    @DeleteMapping("/{permissionId}")
    @PreAuthorize("hasAuthority('system:permission:remove')")
    public Result<Void> remove(@PathVariable Long permissionId) {
        permissionService.deletePermissionById(permissionId);
        return Result.success();
    }
    
    /**
     * 检查权限名称是否唯一
     */
    @GetMapping("/checkPermissionNameUnique")
    public Result<Boolean> checkPermissionNameUnique(@RequestParam String permissionName, 
                                                     @RequestParam(required = false) Long permissionId) {
        boolean unique = permissionService.checkPermissionNameUnique(permissionName, permissionId);
        return Result.success(unique);
    }
    
    /**
     * 检查权限标识是否唯一
     */
    @GetMapping("/checkPermissionKeyUnique")
    public Result<Boolean> checkPermissionKeyUnique(@RequestParam String permissionKey, 
                                                    @RequestParam(required = false) Long permissionId) {
        boolean unique = permissionService.checkPermissionKeyUnique(permissionKey, permissionId);
        return Result.success(unique);
    }
}
