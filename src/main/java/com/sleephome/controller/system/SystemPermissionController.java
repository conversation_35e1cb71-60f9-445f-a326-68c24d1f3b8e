package com.sleephome.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sleephome.common.result.Result;
import com.sleephome.entity.SysPermission;
import com.sleephome.service.SysPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 权限管理控制器
 */
@RestController
@RequestMapping("/api/system/permissions")
public class SystemPermissionController {

    @Autowired
    private SysPermissionService permissionService;

    /**
     * 分页查询权限列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('system:permission:list')")
    public Result<IPage<SysPermission>> list(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String permissionName,
            @RequestParam(required = false) String permissionKey,
            @RequestParam(required = false) String resourceType,
            @RequestParam(required = false) Integer status) {
        
        Page<SysPermission> page = new Page<>(pageNum, pageSize);
        QueryWrapper<SysPermission> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(permissionName)) {
            queryWrapper.like("permission_name", permissionName);
        }
        if (StringUtils.hasText(permissionKey)) {
            queryWrapper.like("permission_key", permissionKey);
        }
        if (StringUtils.hasText(resourceType)) {
            queryWrapper.eq("resource_type", resourceType);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByAsc("sort_order");
        queryWrapper.orderByDesc("create_time");
        
        IPage<SysPermission> result = permissionService.page(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 获取权限树结构
     */
    @GetMapping("/tree")
    @PreAuthorize("hasAuthority('system:permission:list')")
    public Result<List<SysPermission>> tree() {
        List<SysPermission> permissions = permissionService.selectPermissionTree();
        return Result.success(permissions);
    }

    /**
     * 根据权限ID获取详细信息
     */
    @GetMapping("/{permissionId}")
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<SysPermission> getInfo(@PathVariable Long permissionId) {
        SysPermission permission = permissionService.getById(permissionId);
        if (permission == null) {
            return Result.error("权限不存在");
        }
        return Result.success(permission);
    }

    /**
     * 新增权限
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:permission:add')")
    public Result<String> add(@RequestBody SysPermission permission) {
        // 验证必填字段
        if (!StringUtils.hasText(permission.getPermissionName())) {
            return Result.error("权限名称不能为空");
        }
        if (!StringUtils.hasText(permission.getPermissionKey())) {
            return Result.error("权限标识不能为空");
        }

        // 检查权限名称是否已存在
        QueryWrapper<SysPermission> nameWrapper = new QueryWrapper<>();
        nameWrapper.eq("permission_name", permission.getPermissionName());
        if (permissionService.count(nameWrapper) > 0) {
            return Result.error("权限名称已存在");
        }

        // 检查权限标识是否已存在
        QueryWrapper<SysPermission> keyWrapper = new QueryWrapper<>();
        keyWrapper.eq("permission_key", permission.getPermissionKey());
        if (permissionService.count(keyWrapper) > 0) {
            return Result.error("权限标识已存在");
        }

        // 设置默认值
        if (permission.getStatus() == null) {
            permission.setStatus(1);
        }
        if (permission.getParentId() == null) {
            permission.setParentId(0L);
        }
        if (permission.getSortOrder() == null) {
            permission.setSortOrder(0);
        }

        boolean result = permissionService.save(permission);
        return result ? Result.success("新增成功") : Result.error("新增失败");
    }

    /**
     * 修改权限
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:permission:edit')")
    public Result<String> edit(@RequestBody SysPermission permission) {
        if (permission.getId() == null) {
            return Result.error("权限ID不能为空");
        }

        // 验证必填字段
        if (!StringUtils.hasText(permission.getPermissionName())) {
            return Result.error("权限名称不能为空");
        }
        if (!StringUtils.hasText(permission.getPermissionKey())) {
            return Result.error("权限标识不能为空");
        }

        // 检查权限是否存在
        SysPermission existPermission = permissionService.getById(permission.getId());
        if (existPermission == null) {
            return Result.error("权限不存在");
        }

        // 检查权限名称是否已存在（排除自己）
        QueryWrapper<SysPermission> nameWrapper = new QueryWrapper<>();
        nameWrapper.eq("permission_name", permission.getPermissionName());
        nameWrapper.ne("id", permission.getId());
        if (permissionService.count(nameWrapper) > 0) {
            return Result.error("权限名称已存在");
        }

        // 检查权限标识是否已存在（排除自己）
        QueryWrapper<SysPermission> keyWrapper = new QueryWrapper<>();
        keyWrapper.eq("permission_key", permission.getPermissionKey());
        keyWrapper.ne("id", permission.getId());
        if (permissionService.count(keyWrapper) > 0) {
            return Result.error("权限标识已存在");
        }

        boolean result = permissionService.updateById(permission);
        return result ? Result.success("修改成功") : Result.error("修改失败");
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{permissionIds}")
    @PreAuthorize("hasAuthority('system:permission:remove')")
    public Result<String> remove(@PathVariable Long[] permissionIds) {
        if (permissionIds == null || permissionIds.length == 0) {
            return Result.error("请选择要删除的权限");
        }

        // 检查是否有子权限
        for (Long permissionId : permissionIds) {
            QueryWrapper<SysPermission> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("parent_id", permissionId);
            if (permissionService.count(queryWrapper) > 0) {
                return Result.error("存在子权限，不允许删除");
            }
        }

        boolean result = permissionService.removeByIds(Arrays.asList(permissionIds));
        return result ? Result.success("删除成功") : Result.error("删除失败");
    }

    /**
     * 状态修改
     */
    @PutMapping("/changeStatus")
    @PreAuthorize("hasAuthority('system:permission:edit')")
    public Result<String> changeStatus(@RequestBody SysPermission permission) {
        if (permission.getId() == null) {
            return Result.error("权限ID不能为空");
        }
        if (permission.getStatus() == null) {
            return Result.error("状态不能为空");
        }

        SysPermission updatePermission = new SysPermission();
        updatePermission.setId(permission.getId());
        updatePermission.setStatus(permission.getStatus());

        boolean result = permissionService.updateById(updatePermission);
        String statusText = permission.getStatus() == 1 ? "启用" : "停用";
        return result ? Result.success(statusText + "成功") : Result.error(statusText + "失败");
    }

    /**
     * 检查权限名称是否唯一
     */
    @GetMapping("/checkPermissionNameUnique")
    public Result<Boolean> checkPermissionNameUnique(
            @RequestParam String permissionName,
            @RequestParam(required = false) Long permissionId) {
        
        QueryWrapper<SysPermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_name", permissionName);
        
        if (permissionId != null) {
            queryWrapper.ne("id", permissionId);
        }
        
        long count = permissionService.count(queryWrapper);
        return Result.success(count == 0);
    }

    /**
     * 检查权限标识是否唯一
     */
    @GetMapping("/checkPermissionKeyUnique")
    public Result<Boolean> checkPermissionKeyUnique(
            @RequestParam String permissionKey,
            @RequestParam(required = false) Long permissionId) {
        
        QueryWrapper<SysPermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_key", permissionKey);
        
        if (permissionId != null) {
            queryWrapper.ne("id", permissionId);
        }
        
        long count = permissionService.count(queryWrapper);
        return Result.success(count == 0);
    }
}
