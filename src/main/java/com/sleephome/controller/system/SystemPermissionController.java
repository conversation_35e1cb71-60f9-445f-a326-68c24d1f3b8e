package com.sleephome.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sleephome.common.result.Result;
import com.sleephome.entity.SysPermission;
import com.sleephome.service.SysPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统权限管理控制器
 */
@RestController
@RequestMapping("/api/system/permissions")
public class SystemPermissionController {

    @Autowired
    private SysPermissionService permissionService;

    /**
     * 获取权限树形列表
     */
    @GetMapping("/tree")
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<List<SysPermission>> getPermissionTree() {
        QueryWrapper<SysPermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByAsc("sort");
        queryWrapper.orderByDesc("create_time");
        
        List<SysPermission> allPermissions = permissionService.list(queryWrapper);
        List<SysPermission> tree = buildPermissionTree(allPermissions, 0L);
        
        return Result.success(tree);
    }

    /**
     * 获取所有权限列表（平铺）
     */
    @GetMapping
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<List<SysPermission>> getPermissionList(
            @RequestParam(required = false) String permissionName,
            @RequestParam(required = false) String permissionKey,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) Integer status) {
        
        QueryWrapper<SysPermission> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(permissionName)) {
            queryWrapper.like("permission_name", permissionName);
        }
        if (StringUtils.hasText(permissionKey)) {
            queryWrapper.like("permission_key", permissionKey);
        }
        if (type != null) {
            queryWrapper.eq("type", type);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByAsc("sort");
        queryWrapper.orderByDesc("create_time");
        
        List<SysPermission> permissions = permissionService.list(queryWrapper);
        
        return Result.success(permissions);
    }

    /**
     * 根据ID获取权限详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<SysPermission> getPermissionById(@PathVariable Long id) {
        SysPermission permission = permissionService.getById(id);
        if (permission == null) {
            return Result.error("权限不存在");
        }
        
        return Result.success(permission);
    }

    /**
     * 添加权限
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:permission:add')")
    public Result<String> addPermission(@RequestBody SysPermission permission) {
        // 验证必填字段
        if (!StringUtils.hasText(permission.getPermissionName())) {
            return Result.error("权限名称不能为空");
        }
        if (!StringUtils.hasText(permission.getPermissionKey())) {
            return Result.error("权限标识不能为空");
        }
        if (permission.getType() == null) {
            return Result.error("权限类型不能为空");
        }

        // 检查权限标识是否已存在
        QueryWrapper<SysPermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("permission_key", permission.getPermissionKey());
        queryWrapper.eq("deleted", 0);
        if (permissionService.count(queryWrapper) > 0) {
            return Result.error("权限标识已存在");
        }

        // 设置默认值
        if (permission.getParentId() == null) {
            permission.setParentId(0L);
        }
        if (permission.getStatus() == null) {
            permission.setStatus(1); // 默认启用
        }
        if (permission.getSort() == null) {
            permission.setSort(0);
        }
        permission.setCreateTime(LocalDateTime.now());
        permission.setUpdateTime(LocalDateTime.now());
        permission.setDeleted(0);

        boolean success = permissionService.save(permission);
        if (success) {
            return Result.success("添加权限成功");
        } else {
            return Result.error("添加权限失败");
        }
    }

    /**
     * 更新权限
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('system:permission:edit')")
    public Result<String> updatePermission(@PathVariable Long id, @RequestBody SysPermission permission) {
        SysPermission existingPermission = permissionService.getById(id);
        if (existingPermission == null) {
            return Result.error("权限不存在");
        }

        // 检查权限标识是否已被其他权限使用
        if (StringUtils.hasText(permission.getPermissionKey()) && 
            !permission.getPermissionKey().equals(existingPermission.getPermissionKey())) {
            QueryWrapper<SysPermission> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("permission_key", permission.getPermissionKey());
            queryWrapper.ne("id", id);
            queryWrapper.eq("deleted", 0);
            if (permissionService.count(queryWrapper) > 0) {
                return Result.error("权限标识已存在");
            }
        }

        // 不能将父级设置为自己或自己的子级
        if (permission.getParentId() != null && permission.getParentId().equals(id)) {
            return Result.error("不能将父级设置为自己");
        }

        // 更新字段
        permission.setId(id);
        permission.setUpdateTime(LocalDateTime.now());

        boolean success = permissionService.updateById(permission);
        if (success) {
            return Result.success("更新权限成功");
        } else {
            return Result.error("更新权限失败");
        }
    }

    /**
     * 删除权限（逻辑删除）
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:permission:remove')")
    public Result<String> deletePermission(@PathVariable Long id) {
        SysPermission permission = permissionService.getById(id);
        if (permission == null) {
            return Result.error("权限不存在");
        }

        // 检查是否有子权限
        QueryWrapper<SysPermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", id);
        queryWrapper.eq("deleted", 0);
        if (permissionService.count(queryWrapper) > 0) {
            return Result.error("该权限下还有子权限，不能删除");
        }

        permission.setDeleted(1);
        permission.setUpdateTime(LocalDateTime.now());
        
        boolean success = permissionService.updateById(permission);
        if (success) {
            return Result.success("删除权限成功");
        } else {
            return Result.error("删除权限失败");
        }
    }

    /**
     * 启用/禁用权限
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAuthority('system:permission:edit')")
    public Result<String> updatePermissionStatus(@PathVariable Long id, @RequestParam Integer status) {
        SysPermission permission = permissionService.getById(id);
        if (permission == null) {
            return Result.error("权限不存在");
        }

        permission.setStatus(status);
        permission.setUpdateTime(LocalDateTime.now());

        boolean success = permissionService.updateById(permission);
        if (success) {
            String message = status == 1 ? "启用权限成功" : "禁用权限成功";
            return Result.success(message);
        } else {
            return Result.error("更新权限状态失败");
        }
    }

    /**
     * 构建权限树
     */
    private List<SysPermission> buildPermissionTree(List<SysPermission> permissions, Long parentId) {
        List<SysPermission> tree = new ArrayList<>();
        
        Map<Long, List<SysPermission>> permissionMap = permissions.stream()
            .collect(Collectors.groupingBy(SysPermission::getParentId));
        
        List<SysPermission> children = permissionMap.get(parentId);
        if (children != null) {
            for (SysPermission permission : children) {
                List<SysPermission> subChildren = buildPermissionTree(permissions, permission.getId());
                permission.setChildren(subChildren);
                tree.add(permission);
            }
        }
        
        return tree;
    }
}
