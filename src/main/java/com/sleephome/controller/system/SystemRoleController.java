package com.sleephome.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sleephome.common.result.Result;
import com.sleephome.entity.SysRole;
import com.sleephome.service.SysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 角色管理控制器
 */
@RestController
@RequestMapping("/api/system/roles")
public class SystemRoleController {

    @Autowired
    private SysRoleService roleService;

    /**
     * 分页查询角色列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('system:role:list')")
    public Result<IPage<SysRole>> list(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String roleName,
            @RequestParam(required = false) String roleKey,
            @RequestParam(required = false) Integer status) {
        
        Page<SysRole> page = new Page<>(pageNum, pageSize);
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(roleName)) {
            queryWrapper.like("role_name", roleName);
        }
        if (StringUtils.hasText(roleKey)) {
            queryWrapper.like("role_key", roleKey);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByDesc("create_time");
        
        IPage<SysRole> result = roleService.page(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 获取所有角色列表（用于下拉选择）
     */
    @GetMapping("/all")
    @PreAuthorize("hasAuthority('system:role:list')")
    public Result<List<SysRole>> listAll() {
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByAsc("create_time");
        
        List<SysRole> roles = roleService.list(queryWrapper);
        return Result.success(roles);
    }

    /**
     * 根据角色ID获取详细信息
     */
    @GetMapping("/{roleId}")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<SysRole> getInfo(@PathVariable Long roleId) {
        SysRole role = roleService.getById(roleId);
        if (role == null) {
            return Result.error("角色不存在");
        }
        return Result.success(role);
    }

    /**
     * 新增角色
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:role:add')")
    public Result<String> add(@RequestBody SysRole role) {
        // 验证必填字段
        if (!StringUtils.hasText(role.getRoleName())) {
            return Result.error("角色名称不能为空");
        }
        if (!StringUtils.hasText(role.getRoleKey())) {
            return Result.error("角色权限字符串不能为空");
        }

        // 检查角色名称是否已存在
        QueryWrapper<SysRole> nameWrapper = new QueryWrapper<>();
        nameWrapper.eq("role_name", role.getRoleName());
        nameWrapper.eq("deleted", 0);
        if (roleService.count(nameWrapper) > 0) {
            return Result.error("角色名称已存在");
        }

        // 检查角色权限字符串是否已存在
        QueryWrapper<SysRole> keyWrapper = new QueryWrapper<>();
        keyWrapper.eq("role_key", role.getRoleKey());
        keyWrapper.eq("deleted", 0);
        if (roleService.count(keyWrapper) > 0) {
            return Result.error("角色权限字符串已存在");
        }

        // 设置默认状态
        if (role.getStatus() == null) {
            role.setStatus(1);
        }

        boolean result = roleService.save(role);
        return result ? Result.success("新增成功") : Result.error("新增失败");
    }

    /**
     * 修改角色
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:role:edit')")
    public Result<String> edit(@RequestBody SysRole role) {
        if (role.getId() == null) {
            return Result.error("角色ID不能为空");
        }

        // 验证必填字段
        if (!StringUtils.hasText(role.getRoleName())) {
            return Result.error("角色名称不能为空");
        }
        if (!StringUtils.hasText(role.getRoleKey())) {
            return Result.error("角色权限字符串不能为空");
        }

        // 检查角色是否存在
        SysRole existRole = roleService.getById(role.getId());
        if (existRole == null) {
            return Result.error("角色不存在");
        }

        // 检查角色名称是否已存在（排除自己）
        QueryWrapper<SysRole> nameWrapper = new QueryWrapper<>();
        nameWrapper.eq("role_name", role.getRoleName());
        nameWrapper.ne("id", role.getId());
        nameWrapper.eq("deleted", 0);
        if (roleService.count(nameWrapper) > 0) {
            return Result.error("角色名称已存在");
        }

        // 检查角色权限字符串是否已存在（排除自己）
        QueryWrapper<SysRole> keyWrapper = new QueryWrapper<>();
        keyWrapper.eq("role_key", role.getRoleKey());
        keyWrapper.ne("id", role.getId());
        keyWrapper.eq("deleted", 0);
        if (roleService.count(keyWrapper) > 0) {
            return Result.error("角色权限字符串已存在");
        }

        boolean result = roleService.updateById(role);
        return result ? Result.success("修改成功") : Result.error("修改失败");
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{roleIds}")
    @PreAuthorize("hasAuthority('system:role:remove')")
    public Result<String> remove(@PathVariable Long[] roleIds) {
        if (roleIds == null || roleIds.length == 0) {
            return Result.error("请选择要删除的角色");
        }

        // 检查角色是否被用户使用
        for (Long roleId : roleIds) {
            // TODO: 检查角色是否被用户使用
            // 这里可以添加检查逻辑
        }

        // 逻辑删除
        boolean result = roleService.removeByIds(Arrays.asList(roleIds));
        return result ? Result.success("删除成功") : Result.error("删除失败");
    }

    /**
     * 状态修改
     */
    @PutMapping("/changeStatus")
    @PreAuthorize("hasAuthority('system:role:edit')")
    public Result<String> changeStatus(@RequestBody SysRole role) {
        if (role.getId() == null) {
            return Result.error("角色ID不能为空");
        }
        if (role.getStatus() == null) {
            return Result.error("状态不能为空");
        }

        SysRole updateRole = new SysRole();
        updateRole.setId(role.getId());
        updateRole.setStatus(role.getStatus());

        boolean result = roleService.updateById(updateRole);
        String statusText = role.getStatus() == 1 ? "启用" : "停用";
        return result ? Result.success(statusText + "成功") : Result.error(statusText + "失败");
    }

    /**
     * 检查角色名称是否唯一
     */
    @GetMapping("/checkRoleNameUnique")
    public Result<Boolean> checkRoleNameUnique(
            @RequestParam String roleName,
            @RequestParam(required = false) Long roleId) {
        
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_name", roleName);
        queryWrapper.eq("deleted", 0);
        
        if (roleId != null) {
            queryWrapper.ne("id", roleId);
        }
        
        long count = roleService.count(queryWrapper);
        return Result.success(count == 0);
    }

    /**
     * 检查角色权限字符串是否唯一
     */
    @GetMapping("/checkRoleKeyUnique")
    public Result<Boolean> checkRoleKeyUnique(
            @RequestParam String roleKey,
            @RequestParam(required = false) Long roleId) {
        
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_key", roleKey);
        queryWrapper.eq("deleted", 0);
        
        if (roleId != null) {
            queryWrapper.ne("id", roleId);
        }
        
        long count = roleService.count(queryWrapper);
        return Result.success(count == 0);
    }
}
