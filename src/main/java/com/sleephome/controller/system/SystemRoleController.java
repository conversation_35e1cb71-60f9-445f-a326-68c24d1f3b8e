package com.sleephome.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sleephome.common.result.Result;
import com.sleephome.entity.SysRole;
import com.sleephome.service.SysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统角色管理控制器
 */
@RestController
@RequestMapping("/api/system/roles")
public class SystemRoleController {

    @Autowired
    private SysRoleService roleService;

    /**
     * 分页查询角色列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<Page<SysRole>> getRoleList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String roleName,
            @RequestParam(required = false) String roleKey,
            @RequestParam(required = false) Integer status) {
        
        Page<SysRole> pageParam = new Page<>(page, size);
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(roleName)) {
            queryWrapper.like("role_name", roleName);
        }
        if (StringUtils.hasText(roleKey)) {
            queryWrapper.like("role_key", roleKey);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByAsc("sort");
        queryWrapper.orderByDesc("create_time");
        
        Page<SysRole> result = roleService.page(pageParam, queryWrapper);
        
        return Result.success(result);
    }

    /**
     * 获取所有角色列表（不分页）
     */
    @GetMapping("/all")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<List<SysRole>> getAllRoles() {
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByAsc("sort");
        
        List<SysRole> roles = roleService.list(queryWrapper);
        return Result.success(roles);
    }

    /**
     * 根据ID获取角色详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<SysRole> getRoleById(@PathVariable Long id) {
        SysRole role = roleService.getById(id);
        if (role == null) {
            return Result.error("角色不存在");
        }
        
        return Result.success(role);
    }

    /**
     * 添加角色
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:role:add')")
    public Result<String> addRole(@RequestBody SysRole role) {
        // 验证必填字段
        if (!StringUtils.hasText(role.getRoleName())) {
            return Result.error("角色名称不能为空");
        }
        if (!StringUtils.hasText(role.getRoleKey())) {
            return Result.error("角色标识不能为空");
        }

        // 检查角色名称是否已存在
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_name", role.getRoleName());
        queryWrapper.eq("deleted", 0);
        if (roleService.count(queryWrapper) > 0) {
            return Result.error("角色名称已存在");
        }

        // 检查角色标识是否已存在
        queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_key", role.getRoleKey());
        queryWrapper.eq("deleted", 0);
        if (roleService.count(queryWrapper) > 0) {
            return Result.error("角色标识已存在");
        }

        // 设置默认值
        if (role.getStatus() == null) {
            role.setStatus(1); // 默认启用
        }
        if (role.getSort() == null) {
            role.setSort(0);
        }
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
        role.setDeleted(0);

        boolean success = roleService.save(role);
        if (success) {
            return Result.success("添加角色成功");
        } else {
            return Result.error("添加角色失败");
        }
    }

    /**
     * 更新角色
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('system:role:edit')")
    public Result<String> updateRole(@PathVariable Long id, @RequestBody SysRole role) {
        SysRole existingRole = roleService.getById(id);
        if (existingRole == null) {
            return Result.error("角色不存在");
        }

        // 检查角色名称是否已被其他角色使用
        if (StringUtils.hasText(role.getRoleName()) && !role.getRoleName().equals(existingRole.getRoleName())) {
            QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("role_name", role.getRoleName());
            queryWrapper.ne("id", id);
            queryWrapper.eq("deleted", 0);
            if (roleService.count(queryWrapper) > 0) {
                return Result.error("角色名称已存在");
            }
        }

        // 检查角色标识是否已被其他角色使用
        if (StringUtils.hasText(role.getRoleKey()) && !role.getRoleKey().equals(existingRole.getRoleKey())) {
            QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("role_key", role.getRoleKey());
            queryWrapper.ne("id", id);
            queryWrapper.eq("deleted", 0);
            if (roleService.count(queryWrapper) > 0) {
                return Result.error("角色标识已存在");
            }
        }

        // 更新字段
        role.setId(id);
        role.setUpdateTime(LocalDateTime.now());

        boolean success = roleService.updateById(role);
        if (success) {
            return Result.success("更新角色成功");
        } else {
            return Result.error("更新角色失败");
        }
    }

    /**
     * 删除角色（逻辑删除）
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:role:remove')")
    public Result<String> deleteRole(@PathVariable Long id) {
        SysRole role = roleService.getById(id);
        if (role == null) {
            return Result.error("角色不存在");
        }

        // 检查是否有用户使用该角色
        // TODO: 检查用户角色关联表

        role.setDeleted(1);
        role.setUpdateTime(LocalDateTime.now());
        
        boolean success = roleService.updateById(role);
        if (success) {
            return Result.success("删除角色成功");
        } else {
            return Result.error("删除角色失败");
        }
    }

    /**
     * 批量删除角色
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:role:remove')")
    public Result<String> batchDeleteRoles(@RequestBody List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Result.error("请选择要删除的角色");
        }

        // 批量逻辑删除
        List<SysRole> roles = roleService.listByIds(ids);
        roles.forEach(role -> {
            role.setDeleted(1);
            role.setUpdateTime(LocalDateTime.now());
        });

        boolean success = roleService.updateBatchById(roles);
        if (success) {
            return Result.success("批量删除角色成功");
        } else {
            return Result.error("批量删除角色失败");
        }
    }

    /**
     * 启用/禁用角色
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAuthority('system:role:edit')")
    public Result<String> updateRoleStatus(@PathVariable Long id, @RequestParam Integer status) {
        SysRole role = roleService.getById(id);
        if (role == null) {
            return Result.error("角色不存在");
        }

        role.setStatus(status);
        role.setUpdateTime(LocalDateTime.now());

        boolean success = roleService.updateById(role);
        if (success) {
            String message = status == 1 ? "启用角色成功" : "禁用角色成功";
            return Result.success(message);
        } else {
            return Result.error("更新角色状态失败");
        }
    }
}
