package com.sleephome.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sleephome.common.result.Result;
import com.sleephome.entity.OperationLog;
import com.sleephome.service.OperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 操作日志控制器
 */
@RestController
@RequestMapping("/api/system/logs")
public class SystemLogController {

    @Autowired
    private OperationLogService operationLogService;

    /**
     * 分页查询操作日志
     */
    @GetMapping
    @PreAuthorize("hasAuthority('system:log:list')")
    public Result<IPage<OperationLog>> list(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String operation,
            @RequestParam(required = false) String module,
            @RequestParam(required = false) String ipAddress,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        Page<OperationLog> page = new Page<>(pageNum, pageSize);
        QueryWrapper<OperationLog> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(username)) {
            queryWrapper.like("username", username);
        }
        if (StringUtils.hasText(operation)) {
            queryWrapper.eq("operation", operation);
        }
        if (StringUtils.hasText(module)) {
            queryWrapper.eq("module", module);
        }
        if (StringUtils.hasText(ipAddress)) {
            queryWrapper.like("ip_address", ipAddress);
        }
        if (startTime != null) {
            queryWrapper.ge("created_at", startTime);
        }
        if (endTime != null) {
            queryWrapper.le("created_at", endTime);
        }
        
        queryWrapper.orderByDesc("created_at");
        
        IPage<OperationLog> result = operationLogService.page(page, queryWrapper);
        return Result.success(result);
    }

    /**
     * 根据日志ID获取详细信息
     */
    @GetMapping("/{logId}")
    @PreAuthorize("hasAuthority('system:log:query')")
    public Result<OperationLog> getInfo(@PathVariable Long logId) {
        OperationLog log = operationLogService.getById(logId);
        if (log == null) {
            return Result.error("日志不存在");
        }
        return Result.success(log);
    }

    /**
     * 删除操作日志
     */
    @DeleteMapping("/{logIds}")
    @PreAuthorize("hasAuthority('system:log:remove')")
    public Result<String> remove(@PathVariable Long[] logIds) {
        if (logIds == null || logIds.length == 0) {
            return Result.error("请选择要删除的日志");
        }

        boolean result = operationLogService.removeByIds(Arrays.asList(logIds));
        return result ? Result.success("删除成功") : Result.error("删除失败");
    }

    /**
     * 清空操作日志
     */
    @DeleteMapping("/clean")
    @PreAuthorize("hasAuthority('system:log:remove')")
    public Result<String> clean() {
        QueryWrapper<OperationLog> queryWrapper = new QueryWrapper<>();
        boolean result = operationLogService.remove(queryWrapper);
        return result ? Result.success("清空成功") : Result.error("清空失败");
    }

    /**
     * 导出操作日志
     */
    @PostMapping("/export")
    @PreAuthorize("hasAuthority('system:log:export')")
    public Result<String> export(
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String operation,
            @RequestParam(required = false) String module,
            @RequestParam(required = false) String ipAddress,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        // TODO: 实现日志导出功能
        return Result.success("导出功能待实现");
    }

    /**
     * 获取操作类型统计
     */
    @GetMapping("/statistics/operations")
    @PreAuthorize("hasAuthority('system:log:list')")
    public Result<Object> getOperationStatistics() {
        // TODO: 实现操作类型统计
        return Result.success("统计功能待实现");
    }

    /**
     * 获取模块访问统计
     */
    @GetMapping("/statistics/modules")
    @PreAuthorize("hasAuthority('system:log:list')")
    public Result<Object> getModuleStatistics() {
        // TODO: 实现模块访问统计
        return Result.success("统计功能待实现");
    }

    /**
     * 获取用户操作统计
     */
    @GetMapping("/statistics/users")
    @PreAuthorize("hasAuthority('system:log:list')")
    public Result<Object> getUserStatistics() {
        // TODO: 实现用户操作统计
        return Result.success("统计功能待实现");
    }
}
