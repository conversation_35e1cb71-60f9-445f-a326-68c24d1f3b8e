package com.sleephome.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sleephome.common.result.Result;
import com.sleephome.entity.SysUser;
import com.sleephome.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 系统用户管理控制器
 */
@RestController
@RequestMapping("/api/system/users")
public class SystemUserController {

    @Autowired
    private SysUserService userService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 分页查询用户列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<Page<SysUser>> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String realName,
            @RequestParam(required = false) Integer userType,
            @RequestParam(required = false) Integer status) {
        
        Page<SysUser> pageParam = new Page<>(page, size);
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(username)) {
            queryWrapper.like("username", username);
        }
        if (StringUtils.hasText(realName)) {
            queryWrapper.like("real_name", realName);
        }
        if (userType != null) {
            queryWrapper.eq("user_type", userType);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByDesc("create_time");
        
        Page<SysUser> result = userService.page(pageParam, queryWrapper);
        
        // 清除密码字段
        result.getRecords().forEach(user -> user.setPassword(null));
        
        return Result.success(result);
    }

    /**
     * 根据ID获取用户详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<SysUser> getUserById(@PathVariable Long id) {
        SysUser user = userService.getById(id);
        if (user == null) {
            return Result.error("用户不存在");
        }
        
        // 清除密码字段
        user.setPassword(null);
        
        return Result.success(user);
    }

    /**
     * 添加用户
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:user:add')")
    public Result<String> addUser(@RequestBody SysUser user) {
        // 验证必填字段
        if (!StringUtils.hasText(user.getUsername())) {
            return Result.error("用户名不能为空");
        }
        if (!StringUtils.hasText(user.getPassword())) {
            return Result.error("密码不能为空");
        }
        if (user.getUserType() == null) {
            return Result.error("用户类型不能为空");
        }

        // 检查用户名是否已存在
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", user.getUsername());
        queryWrapper.eq("deleted", 0);
        if (userService.count(queryWrapper) > 0) {
            return Result.error("用户名已存在");
        }

        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 设置默认值
        if (user.getStatus() == null) {
            user.setStatus(1); // 默认启用
        }
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        user.setDeleted(0);

        boolean success = userService.save(user);
        if (success) {
            return Result.success("添加用户成功");
        } else {
            return Result.error("添加用户失败");
        }
    }

    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<String> updateUser(@PathVariable Long id, @RequestBody SysUser user) {
        SysUser existingUser = userService.getById(id);
        if (existingUser == null) {
            return Result.error("用户不存在");
        }

        // 检查用户名是否已被其他用户使用
        if (StringUtils.hasText(user.getUsername()) && !user.getUsername().equals(existingUser.getUsername())) {
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", user.getUsername());
            queryWrapper.ne("id", id);
            queryWrapper.eq("deleted", 0);
            if (userService.count(queryWrapper) > 0) {
                return Result.error("用户名已存在");
            }
        }

        // 更新字段
        user.setId(id);
        user.setUpdateTime(LocalDateTime.now());
        
        // 如果密码不为空，则加密新密码
        if (StringUtils.hasText(user.getPassword())) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        } else {
            user.setPassword(null); // 不更新密码
        }

        boolean success = userService.updateById(user);
        if (success) {
            return Result.success("更新用户成功");
        } else {
            return Result.error("更新用户失败");
        }
    }

    /**
     * 删除用户（逻辑删除）
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:user:remove')")
    public Result<String> deleteUser(@PathVariable Long id) {
        SysUser user = userService.getById(id);
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 不能删除自己
        // TODO: 获取当前登录用户ID进行比较

        user.setDeleted(1);
        user.setUpdateTime(LocalDateTime.now());
        
        boolean success = userService.updateById(user);
        if (success) {
            return Result.success("删除用户成功");
        } else {
            return Result.error("删除用户失败");
        }
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:user:remove')")
    public Result<String> batchDeleteUsers(@RequestBody List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Result.error("请选择要删除的用户");
        }

        // 批量逻辑删除
        List<SysUser> users = userService.listByIds(ids);
        users.forEach(user -> {
            user.setDeleted(1);
            user.setUpdateTime(LocalDateTime.now());
        });

        boolean success = userService.updateBatchById(users);
        if (success) {
            return Result.success("批量删除用户成功");
        } else {
            return Result.error("批量删除用户失败");
        }
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/{id}/reset-password")
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<String> resetPassword(@PathVariable Long id, @RequestBody String newPassword) {
        SysUser user = userService.getById(id);
        if (user == null) {
            return Result.error("用户不存在");
        }

        if (!StringUtils.hasText(newPassword)) {
            newPassword = "123456"; // 默认密码
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdateTime(LocalDateTime.now());

        boolean success = userService.updateById(user);
        if (success) {
            return Result.success("重置密码成功，新密码为：" + newPassword);
        } else {
            return Result.error("重置密码失败");
        }
    }

    /**
     * 启用/禁用用户
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<String> updateUserStatus(@PathVariable Long id, @RequestParam Integer status) {
        SysUser user = userService.getById(id);
        if (user == null) {
            return Result.error("用户不存在");
        }

        user.setStatus(status);
        user.setUpdateTime(LocalDateTime.now());

        boolean success = userService.updateById(user);
        if (success) {
            String message = status == 1 ? "启用用户成功" : "禁用用户成功";
            return Result.success(message);
        } else {
            return Result.error("更新用户状态失败");
        }
    }
}
