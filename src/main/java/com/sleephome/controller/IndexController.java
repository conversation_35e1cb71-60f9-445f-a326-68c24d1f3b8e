package com.sleephome.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 主页面控制器
 */
@Controller
public class IndexController {
    
    /**
     * 系统首页
     */
    @GetMapping("/")
    public String index() {
        return "index";
    }
    
    /**
     * 医生端登录页面
     */
    @GetMapping("/doctor/login")
    public String doctorLogin() {
        return "doctor/login";
    }
    
    /**
     * 社区端登录页面
     */
    @GetMapping("/community/login")
    public String communityLogin() {
        return "community/login";
    }
    
    /**
     * 医生端主页
     */
    @GetMapping("/doctor/dashboard")
    public String doctorDashboard() {
        return "doctor/dashboard";
    }
    
    /**
     * 社区端主页
     */
    @GetMapping("/community/dashboard")
    public String communityDashboard() {
        return "community/dashboard";
    }
    
    /**
     * 系统管理页面
     */
    @GetMapping("/system")
    public String system() {
        return "system/index";
    }
}
