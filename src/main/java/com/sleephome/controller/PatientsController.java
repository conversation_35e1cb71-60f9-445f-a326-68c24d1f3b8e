package com.sleephome.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 患者管理控制器
 */
@Controller
@RequestMapping("/patients")
public class PatientsController {

    /**
     * 患者管理页面
     */
    @GetMapping
    @PreAuthorize("hasAuthority('patients')")
    public String index() {
        return "patients";
    }
}
