package com.sleephome.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sleephome.annotation.Log;
import com.sleephome.common.result.Result;
import com.sleephome.entity.SysOperationLog;
import com.sleephome.service.SysOperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 操作日志控制器
 */
@RestController
@RequestMapping("/api/system/operlog")
public class SysOperationLogController {
    
    @Autowired
    private SysOperationLogService operationLogService;
    
    /**
     * 分页查询操作日志列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:log:query')")
    public Result<IPage<SysOperationLog>> list(@RequestParam(defaultValue = "1") Integer pageNum,
                                               @RequestParam(defaultValue = "10") Integer pageSize,
                                               SysOperationLog operationLog) {
        Page<SysOperationLog> page = new Page<>(pageNum, pageSize);
        IPage<SysOperationLog> logPage = operationLogService.selectOperationLogPage(page, operationLog);
        return Result.success(logPage);
    }
    
    /**
     * 根据操作日志ID查询详细信息
     */
    @GetMapping("/{operId}")
    @PreAuthorize("hasAuthority('system:log:query')")
    public Result<SysOperationLog> getInfo(@PathVariable Long operId) {
        SysOperationLog operationLog = operationLogService.getById(operId);
        return Result.success(operationLog);
    }
    
    /**
     * 删除操作日志
     */
    @Log(title = "操作日志", businessType = "DELETE")
    @DeleteMapping("/{operIds}")
    @PreAuthorize("hasAuthority('system:log:remove')")
    public Result<Void> remove(@PathVariable Long[] operIds) {
        operationLogService.deleteOperationLogByIds(operIds);
        return Result.success();
    }
    
    /**
     * 清空操作日志
     */
    @Log(title = "操作日志", businessType = "CLEAN")
    @DeleteMapping("/clean")
    @PreAuthorize("hasAuthority('system:log:remove')")
    public Result<Void> clean() {
        operationLogService.cleanOperationLog();
        return Result.success();
    }
}
