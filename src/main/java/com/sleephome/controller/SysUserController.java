package com.sleephome.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sleephome.annotation.Log;
import com.sleephome.common.result.Result;
import com.sleephome.entity.SysUser;
import com.sleephome.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 用户管理控制器
 */
@RestController
@RequestMapping("/api/system/user")
public class SysUserController {
    
    @Autowired
    private SysUserService userService;
    
    /**
     * 分页查询用户列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<IPage<SysUser>> list(@RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "10") Integer pageSize,
                                       SysUser user) {
        Page<SysUser> page = new Page<>(pageNum, pageSize);
        IPage<SysUser> userPage = userService.selectUserPage(page, user);
        return Result.success(userPage);
    }
    
    /**
     * 根据用户ID查询用户信息
     */
    @GetMapping("/{userId}")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<SysUser> getInfo(@PathVariable Long userId) {
        SysUser user = userService.getById(userId);
        return Result.success(user);
    }
    
    /**
     * 新增用户
     */
    @Log(title = "用户管理", businessType = "INSERT")
    @PostMapping
    @PreAuthorize("hasAuthority('system:user:add')")
    public Result<Void> add(@Valid @RequestBody SysUser user) {
        userService.insertUser(user);
        return Result.success();
    }
    
    /**
     * 修改用户
     */
    @Log(title = "用户管理", businessType = "UPDATE")
    @PutMapping
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<Void> edit(@Valid @RequestBody SysUser user) {
        userService.updateUser(user);
        return Result.success();
    }

    /**
     * 删除用户
     */
    @Log(title = "用户管理", businessType = "DELETE")
    @DeleteMapping("/{userIds}")
    @PreAuthorize("hasAuthority('system:user:remove')")
    public Result<Void> remove(@PathVariable Long[] userIds) {
        userService.deleteUserByIds(userIds);
        return Result.success();
    }
    
    /**
     * 重置用户密码
     */
    @PutMapping("/resetPwd")
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<Void> resetPwd(@RequestParam Long userId, @RequestParam String password) {
        userService.resetUserPassword(userId, password);
        return Result.success();
    }
    
    /**
     * 修改用户状态
     */
    @PutMapping("/changeStatus")
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<Void> changeStatus(@RequestParam Long userId, @RequestParam Integer status) {
        userService.changeUserStatus(userId, status);
        return Result.success();
    }
    
    /**
     * 检查用户名是否唯一
     */
    @GetMapping("/checkUsernameUnique")
    public Result<Boolean> checkUsernameUnique(@RequestParam String username, 
                                               @RequestParam(required = false) Long userId) {
        boolean unique = userService.checkUsernameUnique(username, userId);
        return Result.success(unique);
    }
    
    /**
     * 检查邮箱是否唯一
     */
    @GetMapping("/checkEmailUnique")
    public Result<Boolean> checkEmailUnique(@RequestParam String email, 
                                            @RequestParam(required = false) Long userId) {
        boolean unique = userService.checkEmailUnique(email, userId);
        return Result.success(unique);
    }
    
    /**
     * 检查手机号是否唯一
     */
    @GetMapping("/checkPhoneUnique")
    public Result<Boolean> checkPhoneUnique(@RequestParam String phone, 
                                            @RequestParam(required = false) Long userId) {
        boolean unique = userService.checkPhoneUnique(phone, userId);
        return Result.success(unique);
    }
}
