package com.sleephome.controller;

import com.sleephome.security.jwt.JwtUtils;
import com.sleephome.service.SysUserService;
import com.sleephome.entity.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 仪表板控制器
 */
@Controller
public class DashboardController {

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private SysUserService userService;

    /**
     * 统一仪表板 - 根据用户角色显示不同内容
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model, HttpServletRequest request) {
        // 不再在服务器端判断角色，由前端JavaScript处理
        model.addAttribute("userRole", ""); // 空值，由前端设置
        model.addAttribute("pageTitle", "工作台");
        return "dashboard";
    }

    /**
     * 医生端仪表板（重定向到统一仪表板）
     */
    @GetMapping("/doctor/dashboard")
    public String doctorDashboard() {
        return "redirect:/dashboard";
    }

    /**
     * 社区端仪表板（重定向到统一仪表板）
     */
    @GetMapping("/community/dashboard")
    public String communityDashboard() {
        return "redirect:/dashboard";
    }

    /**
     * 从token中获取用户角色
     */
    private String getUserRoleFromToken(HttpServletRequest request) {
        try {
            String token = parseJwt(request);
            if (StringUtils.hasText(token) && jwtUtils.validateJwtToken(token)) {
                String username = jwtUtils.getUserNameFromJwtToken(token);
                SysUser user = userService.selectByUsername(username);
                if (user != null) {
                    // 根据用户类型返回角色标识
                    switch (user.getUserType()) {
                        case 1:
                            return "doctor";
                        case 2:
                            return "community";
                        case 3:
                            return "system";
                        default:
                            return "doctor";
                    }
                }
            }
        } catch (Exception e) {
            // 如果获取失败，返回默认角色
        }
        return "doctor"; // 默认医生角色
    }

    /**
     * 从请求头中解析JWT Token
     */
    private String parseJwt(HttpServletRequest request) {
        String headerAuth = request.getHeader("Authorization");

        if (StringUtils.hasText(headerAuth) && headerAuth.startsWith("Bearer ")) {
            return headerAuth.substring(7);
        }

        return null;
    }

    /**
     * 根据用户角色获取页面标题
     */
    private String getPageTitle(String userRole) {
        switch (userRole) {
            case "doctor":
                return "医生工作台";
            case "community":
                return "社区管理台";
            case "system":
                return "系统管理台";
            default:
                return "工作台";
        }
    }
}
