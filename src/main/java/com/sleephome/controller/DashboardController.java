package com.sleephome.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 仪表板控制器
 */
@Controller
public class DashboardController {

    /**
     * 统一仪表板 - 根据用户角色显示不同内容
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model, HttpServletRequest request) {
        // 这里可以从JWT token或session中获取用户信息
        // 暂时使用默认值，实际应该从认证信息中获取
        String userRole = getUserRoleFromToken(request);
        model.addAttribute("userRole", userRole);
        model.addAttribute("pageTitle", getPageTitle(userRole));
        return "dashboard";
    }

    /**
     * 医生端仪表板（重定向到统一仪表板）
     */
    @GetMapping("/doctor/dashboard")
    public String doctorDashboard() {
        return "redirect:/dashboard";
    }

    /**
     * 社区端仪表板（重定向到统一仪表板）
     */
    @GetMapping("/community/dashboard")
    public String communityDashboard() {
        return "redirect:/dashboard";
    }

    /**
     * 从token中获取用户角色（简化实现）
     */
    private String getUserRoleFromToken(HttpServletRequest request) {
        // 这里应该解析JWT token获取用户角色
        // 暂时返回默认值
        return "doctor"; // 默认医生角色
    }

    /**
     * 根据用户角色获取页面标题
     */
    private String getPageTitle(String userRole) {
        switch (userRole) {
            case "doctor":
                return "医生工作台";
            case "community":
                return "社区管理台";
            case "system":
                return "系统管理台";
            default:
                return "工作台";
        }
    }
}
