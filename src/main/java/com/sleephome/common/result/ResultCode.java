package com.sleephome.common.result;

/**
 * 返回状态码枚举
 */
public enum ResultCode {
    
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    
    // 认证相关
    UNAUTHORIZED(401, "未认证"),
    FORBIDDEN(403, "无权限"),
    TOKEN_EXPIRED(4001, "Token已过期"),
    TOKEN_INVALID(4002, "Token无效"),
    
    // 参数相关
    PARAM_ERROR(400, "参数错误"),
    PARAM_MISSING(4001, "参数缺失"),
    PARAM_INVALID(4002, "参数无效"),
    
    // 用户相关
    USER_NOT_FOUND(5001, "用户不存在"),
    USER_DISABLED(5002, "用户已禁用"),
    USERNAME_EXISTS(5003, "用户名已存在"),
    PASSWORD_ERROR(5004, "密码错误"),
    
    // 角色相关
    ROLE_NOT_FOUND(6001, "角色不存在"),
    ROLE_EXISTS(6002, "角色已存在"),
    ROLE_IN_USE(6003, "角色正在使用中"),
    
    // 权限相关
    PERMISSION_NOT_FOUND(7001, "权限不存在"),
    PERMISSION_EXISTS(7002, "权限已存在"),
    PERMISSION_DENIED(7003, "权限不足");
    
    private final Integer code;
    private final String message;
    
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
}
