package com.sleephome.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sleephome.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户Mapper接口
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    
    /**
     * 根据用户名查询用户信息
     */
    @Select("SELECT * FROM sys_user WHERE username = #{username} AND deleted = 0")
    SysUser selectByUsername(@Param("username") String username);
    
    /**
     * 查询用户的角色权限
     */
    @Select("SELECT p.permission_key FROM sys_user u " +
            "LEFT JOIN sys_user_role ur ON u.id = ur.user_id " +
            "LEFT JOIN sys_role_permission rp ON ur.role_id = rp.role_id " +
            "LEFT JOIN sys_permission p ON rp.permission_id = p.id " +
            "WHERE u.id = #{userId} AND u.deleted = 0 AND p.status = 1")
    List<String> selectUserPermissions(@Param("userId") Long userId);
    
    /**
     * 查询用户的角色列表
     */
    @Select("SELECT r.role_key FROM sys_user u " +
            "LEFT JOIN sys_user_role ur ON u.id = ur.user_id " +
            "LEFT JOIN sys_role r ON ur.role_id = r.id " +
            "WHERE u.id = #{userId} AND u.deleted = 0 AND r.status = 1")
    List<String> selectUserRoles(@Param("userId") Long userId);
}
