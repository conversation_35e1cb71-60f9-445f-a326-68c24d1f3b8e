package com.sleephome.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sleephome.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色Mapper接口
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 根据用户ID查询角色列表
     */
    @Select("SELECT r.* FROM sys_role r " +
            "LEFT JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.deleted = 0 AND r.status = 1")
    List<SysRole> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据角色键查询角色
     */
    @Select("SELECT * FROM sys_role WHERE role_key = #{roleKey} AND deleted = 0")
    SysRole selectByRoleKey(@Param("roleKey") String roleKey);

    /**
     * 查询所有启用的角色
     */
    @Select("SELECT * FROM sys_role WHERE status = 1 AND deleted = 0 ORDER BY create_time DESC")
    List<SysRole> selectEnabledRoles();
}
