package com.sleephome.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sleephome.entity.SysRole;

import java.util.List;

/**
 * 角色服务接口
 */
public interface SysRoleService extends IService<SysRole> {
    
    /**
     * 分页查询角色列表
     */
    IPage<SysRole> selectRolePage(IPage<SysRole> page, SysRole role);
    
    /**
     * 查询所有角色列表
     */
    List<SysRole> selectRoleAll();
    
    /**
     * 新增角色
     */
    boolean insertRole(SysRole role);
    
    /**
     * 修改角色
     */
    boolean updateRole(SysRole role);
    
    /**
     * 删除角色
     */
    boolean deleteRoleByIds(Long[] roleIds);
    
    /**
     * 检查角色名称是否唯一
     */
    boolean checkRoleNameUnique(String roleName, Long roleId);
    
    /**
     * 检查角色权限字符串是否唯一
     */
    boolean checkRoleKeyUnique(String roleKey, Long roleId);
    
    /**
     * 检查角色是否允许操作
     */
    boolean checkRoleAllowed(SysRole role);
    
    /**
     * 根据用户ID查询角色
     */
    List<SysRole> selectRolesByUserId(Long userId);
}
