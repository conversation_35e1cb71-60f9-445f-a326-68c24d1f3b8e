package com.sleephome.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sleephome.entity.OperationLog;

/**
 * 操作日志服务接口
 */
public interface OperationLogService extends IService<OperationLog> {
    
    /**
     * 记录操作日志
     */
    void recordLog(String username, String operation, String module, String description);
    
    /**
     * 记录操作日志（详细信息）
     */
    void recordLog(String username, String operation, String module, String description, 
                   String ipAddress, String userAgent, String requestUrl, String requestMethod, 
                   String requestParams, Integer responseStatus, Long executionTime);
}
