package com.sleephome.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sleephome.entity.OperationLog;
import com.sleephome.mapper.OperationLogMapper;
import com.sleephome.service.OperationLogService;
import org.springframework.stereotype.Service;

/**
 * 操作日志服务实现类
 */
@Service
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements OperationLogService {
    
    @Override
    public void recordLog(String username, String operation, String module, String description) {
        OperationLog log = new OperationLog(username, operation, module, description);
        this.save(log);
    }
    
    @Override
    public void recordLog(String username, String operation, String module, String description, 
                         String ipAddress, String userAgent, String requestUrl, String requestMethod, 
                         String requestParams, Integer responseStatus, Long executionTime) {
        OperationLog log = new OperationLog(username, operation, module, description);
        log.setIpAddress(ipAddress);
        log.setUserAgent(userAgent);
        log.setRequestUrl(requestUrl);
        log.setRequestMethod(requestMethod);
        log.setRequestParams(requestParams);
        log.setResponseStatus(responseStatus);
        log.setExecutionTime(executionTime);
        this.save(log);
    }
}
