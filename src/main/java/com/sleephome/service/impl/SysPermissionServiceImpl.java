package com.sleephome.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sleephome.common.exception.BusinessException;
import com.sleephome.entity.SysPermission;
import com.sleephome.mapper.SysPermissionMapper;
import com.sleephome.service.SysPermissionService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 */
@Service
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermission> implements SysPermissionService {
    
    @Resource
    private SysPermissionMapper permissionMapper;
    
    @Override
    public List<SysPermission> selectPermissionList(SysPermission permission) {
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(permission.getPermissionName()), SysPermission::getPermissionName, permission.getPermissionName())
               .like(StringUtils.hasText(permission.getPermissionKey()), SysPermission::getPermissionKey, permission.getPermissionKey())
               .eq(StringUtils.hasText(permission.getResourceType()), SysPermission::getResourceType, permission.getResourceType())
               .eq(permission.getStatus() != null, SysPermission::getStatus, permission.getStatus())
               .orderByAsc(SysPermission::getParentId)
               .orderByAsc(SysPermission::getSortOrder);
        return this.list(wrapper);
    }
    
    @Override
    public List<SysPermission> buildPermissionTree(List<SysPermission> permissions) {
        List<SysPermission> returnList = new ArrayList<>();
        List<Long> tempList = permissions.stream().map(SysPermission::getId).collect(Collectors.toList());

        for (SysPermission permission : permissions) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(permission.getParentId())) {
                recursionFn(permissions, permission);
                returnList.add(permission);
            }
        }

        if (returnList.isEmpty()) {
            returnList = permissions;
        }
        return returnList;
    }

    @Override
    public List<SysPermission> selectPermissionTree() {
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysPermission::getStatus, 1)
               .orderByAsc(SysPermission::getParentId)
               .orderByAsc(SysPermission::getSortOrder);
        List<SysPermission> permissions = this.list(wrapper);
        return buildPermissionTree(permissions);
    }
    
    /**
     * 递归列表
     */
    private void recursionFn(List<SysPermission> list, SysPermission t) {
        // 得到子节点列表
        List<SysPermission> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysPermission tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }
    
    /**
     * 得到子节点列表
     */
    private List<SysPermission> getChildList(List<SysPermission> list, SysPermission t) {
        List<SysPermission> tlist = new ArrayList<>();
        Iterator<SysPermission> it = list.iterator();
        while (it.hasNext()) {
            SysPermission n = it.next();
            if (n.getParentId() != null && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }
    
    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysPermission> list, SysPermission t) {
        return getChildList(list, t).size() > 0;
    }
    
    @Override
    public List<SysPermission> selectPermissionsByRoleId(Long roleId) {
        // TODO: 实现根据角色ID查询权限
        return null;
    }
    
    @Override
    public List<SysPermission> selectPermissionsByUserId(Long userId) {
        // TODO: 实现根据用户ID查询权限
        return null;
    }
    
    @Override
    public boolean insertPermission(SysPermission permission) {
        // 检查权限名称唯一性
        if (!checkPermissionNameUnique(permission.getPermissionName(), null)) {
            throw new BusinessException("权限名称已存在");
        }
        
        // 检查权限标识唯一性
        if (!checkPermissionKeyUnique(permission.getPermissionKey(), null)) {
            throw new BusinessException("权限标识已存在");
        }
        
        return this.save(permission);
    }
    
    @Override
    public boolean updatePermission(SysPermission permission) {
        // 检查权限名称唯一性
        if (!checkPermissionNameUnique(permission.getPermissionName(), permission.getId())) {
            throw new BusinessException("权限名称已存在");
        }
        
        // 检查权限标识唯一性
        if (!checkPermissionKeyUnique(permission.getPermissionKey(), permission.getId())) {
            throw new BusinessException("权限标识已存在");
        }
        
        return this.updateById(permission);
    }
    
    @Override
    public boolean deletePermissionById(Long permissionId) {
        if (hasChildByPermissionId(permissionId)) {
            throw new BusinessException("存在子权限，不允许删除");
        }
        if (checkPermissionExistRole(permissionId)) {
            throw new BusinessException("权限已分配，不允许删除");
        }
        return this.removeById(permissionId);
    }
    
    @Override
    public boolean checkPermissionNameUnique(String permissionName, Long permissionId) {
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysPermission::getPermissionName, permissionName);
        if (permissionId != null) {
            wrapper.ne(SysPermission::getId, permissionId);
        }
        return this.count(wrapper) == 0;
    }
    
    @Override
    public boolean checkPermissionKeyUnique(String permissionKey, Long permissionId) {
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysPermission::getPermissionKey, permissionKey);
        if (permissionId != null) {
            wrapper.ne(SysPermission::getId, permissionId);
        }
        return this.count(wrapper) == 0;
    }
    
    @Override
    public boolean hasChildByPermissionId(Long permissionId) {
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysPermission::getParentId, permissionId);
        return this.count(wrapper) > 0;
    }
    
    @Override
    public boolean checkPermissionExistRole(Long permissionId) {
        // TODO: 检查权限是否存在角色
        return false;
    }
}
