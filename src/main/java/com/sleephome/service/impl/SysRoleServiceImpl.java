package com.sleephome.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sleephome.common.exception.BusinessException;
import com.sleephome.common.result.ResultCode;
import com.sleephome.entity.SysRole;
import com.sleephome.mapper.SysRoleMapper;
import com.sleephome.service.SysRoleService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 角色服务实现类
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {
    
    @Resource
    private SysRoleMapper roleMapper;
    
    @Override
    public IPage<SysRole> selectRolePage(IPage<SysRole> page, SysRole role) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(role.getRoleName()), SysRole::getRoleName, role.getRoleName())
               .like(StringUtils.hasText(role.getRoleKey()), SysRole::getRoleKey, role.getRoleKey())
               .eq(role.getStatus() != null, SysRole::getStatus, role.getStatus())
               .orderByAsc(SysRole::getId);
        return this.page(page, wrapper);
    }
    
    @Override
    public List<SysRole> selectRoleAll() {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getStatus, 1)
               .orderByAsc(SysRole::getId);
        return this.list(wrapper);
    }
    
    @Override
    public boolean insertRole(SysRole role) {
        // 检查角色名称唯一性
        if (!checkRoleNameUnique(role.getRoleName(), null)) {
            throw new BusinessException(ResultCode.ROLE_EXISTS);
        }
        
        // 检查角色权限字符串唯一性
        if (!checkRoleKeyUnique(role.getRoleKey(), null)) {
            throw new BusinessException("角色权限字符串已存在");
        }
        
        return this.save(role);
    }
    
    @Override
    public boolean updateRole(SysRole role) {
        // 检查角色是否允许操作
        checkRoleAllowed(role);
        
        // 检查角色名称唯一性
        if (!checkRoleNameUnique(role.getRoleName(), role.getId())) {
            throw new BusinessException(ResultCode.ROLE_EXISTS);
        }
        
        // 检查角色权限字符串唯一性
        if (!checkRoleKeyUnique(role.getRoleKey(), role.getId())) {
            throw new BusinessException("角色权限字符串已存在");
        }
        
        return this.updateById(role);
    }
    
    @Override
    public boolean deleteRoleByIds(Long[] roleIds) {
        for (Long roleId : roleIds) {
            SysRole role = this.getById(roleId);
            if (role != null) {
                checkRoleAllowed(role);
                // TODO: 检查角色是否已分配给用户
            }
        }
        return this.removeByIds(Arrays.asList(roleIds));
    }
    
    @Override
    public boolean checkRoleNameUnique(String roleName, Long roleId) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getRoleName, roleName);
        if (roleId != null) {
            wrapper.ne(SysRole::getId, roleId);
        }
        return this.count(wrapper) == 0;
    }
    
    @Override
    public boolean checkRoleKeyUnique(String roleKey, Long roleId) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getRoleKey, roleKey);
        if (roleId != null) {
            wrapper.ne(SysRole::getId, roleId);
        }
        return this.count(wrapper) == 0;
    }
    
    @Override
    public boolean checkRoleAllowed(SysRole role) {
        if (role.getId() != null && role.getId() == 1L) {
            throw new BusinessException("不允许操作超级管理员角色");
        }
        return true;
    }
    
    @Override
    public List<SysRole> selectRolesByUserId(Long userId) {
        // TODO: 实现根据用户ID查询角色
        return null;
    }
}
