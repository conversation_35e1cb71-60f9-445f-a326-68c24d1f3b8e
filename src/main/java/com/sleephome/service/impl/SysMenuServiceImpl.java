package com.sleephome.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sleephome.entity.SysMenu;
import com.sleephome.mapper.SysMenuMapper;
import com.sleephome.service.SysMenuService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统菜单服务实现类
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {
    
    @Override
    public List<SysMenu> getMenuTree() {
        QueryWrapper<SysMenu> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        queryWrapper.eq("status", 1);
        queryWrapper.orderByAsc("sort");
        
        List<SysMenu> allMenus = list(queryWrapper);
        return buildMenuTree(allMenus, 0L);
    }
    
    @Override
    public List<SysMenu> getMenusByUserId(Long userId) {
        // 简化实现：根据用户类型返回不同菜单
        // 实际应该通过用户-角色-菜单关联查询
        return getMenuTree();
    }
    
    @Override
    public List<SysMenu> getMenusByRoleId(Long roleId) {
        // 简化实现：根据角色返回不同菜单
        // 实际应该通过角色-菜单关联查询
        return getMenuTree();
    }
    
    /**
     * 构建菜单树
     */
    private List<SysMenu> buildMenuTree(List<SysMenu> menus, Long parentId) {
        List<SysMenu> tree = new ArrayList<>();
        
        Map<Long, List<SysMenu>> menuMap = menus.stream()
            .collect(Collectors.groupingBy(SysMenu::getParentId));
        
        List<SysMenu> children = menuMap.get(parentId);
        if (children != null) {
            for (SysMenu menu : children) {
                List<SysMenu> subChildren = buildMenuTree(menus, menu.getId());
                menu.setChildren(subChildren);
                tree.add(menu);
            }
        }
        
        return tree;
    }
}
