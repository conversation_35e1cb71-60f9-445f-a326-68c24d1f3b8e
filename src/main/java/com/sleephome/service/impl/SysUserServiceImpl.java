package com.sleephome.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sleephome.common.exception.BusinessException;
import com.sleephome.common.result.ResultCode;
import com.sleephome.entity.SysUser;
import com.sleephome.mapper.SysUserMapper;
import com.sleephome.service.SysUserService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.Arrays;

/**
 * 用户服务实现类
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    
    @Resource
    private SysUserMapper userMapper;
    
    @Resource
    private PasswordEncoder passwordEncoder;
    
    @Override
    public SysUser selectByUsername(String username) {
        return userMapper.selectByUsername(username);
    }
    
    @Override
    public IPage<SysUser> selectUserPage(IPage<SysUser> page, SysUser user) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(user.getUsername()), SysUser::getUsername, user.getUsername())
               .like(StringUtils.hasText(user.getRealName()), SysUser::getRealName, user.getRealName())
               .eq(user.getUserType() != null, SysUser::getUserType, user.getUserType())
               .eq(user.getStatus() != null, SysUser::getStatus, user.getStatus())
               .orderByDesc(SysUser::getCreateTime);
        return this.page(page, wrapper);
    }
    
    @Override
    public boolean insertUser(SysUser user) {
        // 检查用户名唯一性
        if (!checkUsernameUnique(user.getUsername(), null)) {
            throw new BusinessException(ResultCode.USERNAME_EXISTS);
        }
        
        // 检查邮箱唯一性
        if (StringUtils.hasText(user.getEmail()) && !checkEmailUnique(user.getEmail(), null)) {
            throw new BusinessException("邮箱已存在");
        }
        
        // 检查手机号唯一性
        if (StringUtils.hasText(user.getPhone()) && !checkPhoneUnique(user.getPhone(), null)) {
            throw new BusinessException("手机号已存在");
        }
        
        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        return this.save(user);
    }
    
    @Override
    public boolean updateUser(SysUser user) {
        // 检查用户名唯一性
        if (!checkUsernameUnique(user.getUsername(), user.getId())) {
            throw new BusinessException(ResultCode.USERNAME_EXISTS);
        }
        
        // 检查邮箱唯一性
        if (StringUtils.hasText(user.getEmail()) && !checkEmailUnique(user.getEmail(), user.getId())) {
            throw new BusinessException("邮箱已存在");
        }
        
        // 检查手机号唯一性
        if (StringUtils.hasText(user.getPhone()) && !checkPhoneUnique(user.getPhone(), user.getId())) {
            throw new BusinessException("手机号已存在");
        }
        
        // 不更新密码字段
        user.setPassword(null);
        
        return this.updateById(user);
    }
    
    @Override
    public boolean deleteUserByIds(Long[] userIds) {
        return this.removeByIds(Arrays.asList(userIds));
    }
    
    @Override
    public boolean resetUserPassword(Long userId, String password) {
        SysUser user = new SysUser();
        user.setId(userId);
        user.setPassword(passwordEncoder.encode(password));
        return this.updateById(user);
    }
    
    @Override
    public boolean changeUserStatus(Long userId, Integer status) {
        SysUser user = new SysUser();
        user.setId(userId);
        user.setStatus(status);
        return this.updateById(user);
    }
    
    @Override
    public boolean checkUsernameUnique(String username, Long userId) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getUsername, username);
        if (userId != null) {
            wrapper.ne(SysUser::getId, userId);
        }
        return this.count(wrapper) == 0;
    }
    
    @Override
    public boolean checkEmailUnique(String email, Long userId) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getEmail, email);
        if (userId != null) {
            wrapper.ne(SysUser::getId, userId);
        }
        return this.count(wrapper) == 0;
    }
    
    @Override
    public boolean checkPhoneUnique(String phone, Long userId) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getPhone, phone);
        if (userId != null) {
            wrapper.ne(SysUser::getId, userId);
        }
        return this.count(wrapper) == 0;
    }
}
