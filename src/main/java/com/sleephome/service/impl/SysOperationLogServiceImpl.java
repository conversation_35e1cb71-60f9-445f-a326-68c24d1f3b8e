package com.sleephome.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sleephome.entity.SysOperationLog;
import com.sleephome.mapper.SysOperationLogMapper;
import com.sleephome.service.SysOperationLogService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 操作日志服务实现类
 */
@Service
public class SysOperationLogServiceImpl extends ServiceImpl<SysOperationLogMapper, SysOperationLog> implements SysOperationLogService {
    
    @Resource
    private SysOperationLogMapper operationLogMapper;
    
    @Override
    public IPage<SysOperationLog> selectOperationLogPage(IPage<SysOperationLog> page, SysOperationLog operationLog) {
        LambdaQueryWrapper<SysOperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(operationLog.getOperationName()), SysOperationLog::getOperationName, operationLog.getOperationName())
               .like(StringUtils.hasText(operationLog.getOperationType()), SysOperationLog::getOperationType, operationLog.getOperationType())
               .like(StringUtils.hasText(operationLog.getOperatorName()), SysOperationLog::getOperatorName, operationLog.getOperatorName())
               .eq(operationLog.getStatus() != null, SysOperationLog::getStatus, operationLog.getStatus())
               .orderByDesc(SysOperationLog::getOperationTime);
        return this.page(page, wrapper);
    }
    
    @Override
    public boolean deleteOperationLogByIds(Long[] operIds) {
        return this.removeByIds(Arrays.asList(operIds));
    }
    
    @Override
    public boolean cleanOperationLog() {
        // 删除所有操作日志
        return this.remove(new LambdaQueryWrapper<>());
    }
}
