package com.sleephome.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sleephome.entity.SysMenu;

import java.util.List;

/**
 * 系统菜单服务接口
 */
public interface SysMenuService extends IService<SysMenu> {
    
    /**
     * 获取菜单树形列表
     */
    List<SysMenu> getMenuTree();
    
    /**
     * 根据用户ID获取菜单权限
     */
    List<SysMenu> getMenusByUserId(Long userId);
    
    /**
     * 根据角色ID获取菜单权限
     */
    List<SysMenu> getMenusByRoleId(Long roleId);
}
