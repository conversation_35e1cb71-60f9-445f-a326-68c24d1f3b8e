package com.sleephome.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sleephome.entity.SysUser;

/**
 * 用户服务接口
 */
public interface SysUserService extends IService<SysUser> {
    
    /**
     * 根据用户名查询用户
     */
    SysUser selectByUsername(String username);
    
    /**
     * 分页查询用户列表
     */
    IPage<SysUser> selectUserPage(IPage<SysUser> page, SysUser user);
    
    /**
     * 新增用户
     */
    boolean insertUser(SysUser user);
    
    /**
     * 修改用户
     */
    boolean updateUser(SysUser user);
    
    /**
     * 删除用户
     */
    boolean deleteUserByIds(Long[] userIds);
    
    /**
     * 重置用户密码
     */
    boolean resetUserPassword(Long userId, String password);
    
    /**
     * 修改用户状态
     */
    boolean changeUserStatus(Long userId, Integer status);
    
    /**
     * 检查用户名是否唯一
     */
    boolean checkUsernameUnique(String username, Long userId);
    
    /**
     * 检查邮箱是否唯一
     */
    boolean checkEmailUnique(String email, Long userId);
    
    /**
     * 检查手机号是否唯一
     */
    boolean checkPhoneUnique(String phone, Long userId);
}
