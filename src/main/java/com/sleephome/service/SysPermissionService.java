package com.sleephome.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sleephome.entity.SysPermission;

import java.util.List;

/**
 * 权限服务接口
 */
public interface SysPermissionService extends IService<SysPermission> {
    
    /**
     * 查询权限列表
     */
    List<SysPermission> selectPermissionList(SysPermission permission);
    
    /**
     * 构建权限树
     */
    List<SysPermission> buildPermissionTree(List<SysPermission> permissions);

    /**
     * 查询权限树结构
     */
    List<SysPermission> selectPermissionTree();
    
    /**
     * 根据角色ID查询权限
     */
    List<SysPermission> selectPermissionsByRoleId(Long roleId);
    
    /**
     * 根据用户ID查询权限
     */
    List<SysPermission> selectPermissionsByUserId(Long userId);
    
    /**
     * 新增权限
     */
    boolean insertPermission(SysPermission permission);
    
    /**
     * 修改权限
     */
    boolean updatePermission(SysPermission permission);
    
    /**
     * 删除权限
     */
    boolean deletePermissionById(Long permissionId);
    
    /**
     * 检查权限名称是否唯一
     */
    boolean checkPermissionNameUnique(String permissionName, Long permissionId);
    
    /**
     * 检查权限标识是否唯一
     */
    boolean checkPermissionKeyUnique(String permissionKey, Long permissionId);
    
    /**
     * 检查是否存在子权限
     */
    boolean hasChildByPermissionId(Long permissionId);
    
    /**
     * 检查权限是否存在角色
     */
    boolean checkPermissionExistRole(Long permissionId);
}
