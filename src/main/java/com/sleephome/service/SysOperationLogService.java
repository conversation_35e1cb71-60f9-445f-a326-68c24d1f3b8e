package com.sleephome.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sleephome.entity.SysOperationLog;

/**
 * 操作日志服务接口
 */
public interface SysOperationLogService extends IService<SysOperationLog> {
    
    /**
     * 分页查询操作日志列表
     */
    IPage<SysOperationLog> selectOperationLogPage(IPage<SysOperationLog> page, SysOperationLog operationLog);
    
    /**
     * 批量删除操作日志
     */
    boolean deleteOperationLogByIds(Long[] operIds);
    
    /**
     * 清空操作日志
     */
    boolean cleanOperationLog();
}
