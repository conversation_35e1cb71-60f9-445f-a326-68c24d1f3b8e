package com.sleephome.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sleephome.common.base.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 角色实体类
 */
@TableName("sys_role")
public class SysRole extends BaseEntity {
    
    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String roleName;
    
    /**
     * 角色权限字符串
     */
    @NotBlank(message = "角色权限字符串不能为空")
    @Size(max = 50, message = "角色权限字符串长度不能超过50个字符")
    private String roleKey;
    
    /**
     * 角色描述
     */
    @Size(max = 200, message = "角色描述长度不能超过200个字符")
    private String description;
    
    /**
     * 状态（0禁用 1正常）
     */
    private Integer status;
    
    // Getters and Setters
    public String getRoleName() {
        return roleName;
    }
    
    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
    
    public String getRoleKey() {
        return roleKey;
    }
    
    public void setRoleKey(String roleKey) {
        this.roleKey = roleKey;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
}
