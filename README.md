# 居家睡眠远程管理平台

## 项目简介

居家睡眠远程管理平台是一套完整的睡眠健康管理系统，包括个人端H5页面和管理后台（医生端、社区端公用一套管理后台，根据角色控制功能权限）。

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.2.0
- **安全**: Spring Security + JWT
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus 3.5.4
- **构建工具**: Maven
- **Java版本**: JDK 17

### 前端技术栈
- **模板引擎**: Thymeleaf
- **CSS框架**: Tailwind CSS
- **图标**: Font Awesome
- **HTTP客户端**: Axios

## 功能模块

### 第一期功能
- [x] 系统架构设计和数据库设计
- [x] 用户管理（增删改查、状态管理）
- [x] 角色管理（增删改查、权限分配）
- [x] 权限管理（增删改查、菜单权限控制）
- [x] 操作日志（记录、查询、统计）
- [x] 登录认证（JWT Token）
- [x] 管理后台前端界面

## 快速开始

### 环境要求
- JDK 17+
- MySQL 8.0+
- Maven 3.6+

### 数据库初始化
1. 创建数据库
```sql
CREATE DATABASE sleep_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行初始化脚本
```bash
mysql -u root -p sleep_management < src/main/resources/sql/init.sql
```

### 配置文件
修改 `src/main/resources/application.yml` 中的数据库连接配置：
```yaml
spring:
  datasource:
    url: *********************************************************************************************************************************************************
    username: root
    password: your_password
```

### 启动应用
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

应用启动后访问：http://localhost:8080

## 默认账号

系统初始化时会创建以下默认账号：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 系统管理员 | 拥有所有权限 |
| doctor | admin123 | 医生 | 医生角色权限 |
| community | admin123 | 社区管理员 | 社区管理员权限 |

## 访问地址

- **系统首页**: http://localhost:8080/
- **医生端登录**: http://localhost:8080/doctor/login
- **社区端登录**: http://localhost:8080/community/login
- **系统管理**: http://localhost:8080/system

## API接口

### 认证接口
- POST `/api/auth/login` - 用户登录
- POST `/api/auth/logout` - 用户登出

### 用户管理接口
- GET `/api/system/user/list` - 分页查询用户列表
- GET `/api/system/user/{userId}` - 查询用户详情
- POST `/api/system/user` - 新增用户
- PUT `/api/system/user` - 修改用户
- DELETE `/api/system/user/{userIds}` - 删除用户

### 角色管理接口
- GET `/api/system/role/list` - 分页查询角色列表
- GET `/api/system/role/{roleId}` - 查询角色详情
- POST `/api/system/role` - 新增角色
- PUT `/api/system/role` - 修改角色
- DELETE `/api/system/role/{roleIds}` - 删除角色

### 权限管理接口
- GET `/api/system/permission/list` - 查询权限列表
- GET `/api/system/permission/treeselect` - 查询权限树
- POST `/api/system/permission` - 新增权限
- PUT `/api/system/permission` - 修改权限
- DELETE `/api/system/permission/{permissionId}` - 删除权限

### 操作日志接口
- GET `/api/system/operlog/list` - 分页查询操作日志
- DELETE `/api/system/operlog/{operIds}` - 删除操作日志
- DELETE `/api/system/operlog/clean` - 清空操作日志

## 项目结构

```
src/
├── main/
│   ├── java/com/sleephome/
│   │   ├── annotation/          # 自定义注解
│   │   ├── aspect/              # AOP切面
│   │   ├── common/              # 公共类
│   │   │   ├── base/            # 基础类
│   │   │   ├── exception/       # 异常处理
│   │   │   └── result/          # 统一返回结果
│   │   ├── config/              # 配置类
│   │   ├── controller/          # 控制器
│   │   ├── dto/                 # 数据传输对象
│   │   ├── entity/              # 实体类
│   │   ├── mapper/              # Mapper接口
│   │   ├── security/            # 安全相关
│   │   ├── service/             # 服务接口
│   │   │   └── impl/            # 服务实现
│   │   └── utils/               # 工具类
│   └── resources/
│       ├── sql/                 # SQL脚本
│       ├── static/              # 静态资源
│       └── templates/           # 模板文件
└── test/                        # 测试代码
```

## 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 所有公共方法必须添加注释

### 数据库规范
- 表名使用下划线命名法
- 字段名使用下划线命名法
- 必须包含创建时间、更新时间字段
- 支持逻辑删除

### API规范
- RESTful API设计
- 统一返回结果格式
- 统一异常处理
- 接口权限控制

## 部署说明

### 生产环境配置
1. 修改数据库连接配置
2. 修改JWT密钥
3. 配置日志输出路径
4. 配置文件上传路径

### 打包部署
```bash
# 打包
mvn clean package -Dmaven.test.skip=true

# 运行
java -jar target/sleep-management-system-1.0.0.jar
```

## 后续开发计划

### 第二期功能
- [ ] 患者管理模块
- [ ] 问卷管理模块
- [ ] 报告管理模块
- [ ] 消息管理模块
- [ ] 个人端H5页面

### 第三期功能
- [ ] 数据统计分析
- [ ] 移动端APP
- [ ] 微信小程序
- [ ] 第三方系统集成

## 联系方式

如有问题，请联系开发团队。
