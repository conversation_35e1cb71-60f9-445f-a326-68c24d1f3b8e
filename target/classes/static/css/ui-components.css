/**
 * 统一的UI组件样式
 * 提供项目中所有通用的UI组件样式
 */

/* ==================== 消息提示样式 ==================== */
.ui-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 320px;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    transition: all 0.3s ease;
    opacity: 0;
}

.ui-message.ui-message-show {
    transform: translateX(0);
    opacity: 1;
}

.ui-message-content {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    border-left: 4px solid;
}

.ui-message-content i:first-child {
    font-size: 18px;
    margin-right: 12px;
    flex-shrink: 0;
}

.ui-message-text {
    flex: 1;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    font-weight: 500;
}

.ui-message-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 4px;
    margin-left: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.ui-message-close:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #666;
}

/* 成功消息 */
.ui-message-success .ui-message-content {
    border-left-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
}

.ui-message-success .ui-message-content i:first-child {
    color: #10b981;
}

/* 错误消息 */
.ui-message-error .ui-message-content {
    border-left-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fef2f2 100%);
}

.ui-message-error .ui-message-content i:first-child {
    color: #ef4444;
}

/* 警告消息 */
.ui-message-warning .ui-message-content {
    border-left-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
}

.ui-message-warning .ui-message-content i:first-child {
    color: #f59e0b;
}

/* 信息消息 */
.ui-message-info .ui-message-content {
    border-left-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
}

.ui-message-info .ui-message-content i:first-child {
    color: #3b82f6;
}

/* ==================== 对话框样式 ==================== */
.ui-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.ui-dialog-overlay.ui-dialog-show {
    opacity: 1;
}

.ui-dialog {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    min-width: 400px;
    max-width: 500px;
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.ui-dialog-overlay.ui-dialog-show .ui-dialog {
    transform: scale(1);
}

.ui-dialog-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.ui-dialog-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
}

.ui-dialog-title i {
    margin-right: 8px;
    font-size: 20px;
}

.ui-dialog-body {
    padding: 20px 24px;
}

.ui-dialog-message {
    margin: 0;
    font-size: 14px;
    line-height: 1.6;
    color: #6b7280;
}

.ui-dialog-footer {
    padding: 16px 24px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* ==================== 按钮样式 ==================== */
.ui-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    min-width: 80px;
}

.ui-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ui-btn-primary {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.ui-btn-primary:hover {
    background: #2563eb;
    border-color: #2563eb;
}

.ui-btn-secondary {
    background: white;
    color: #6b7280;
    border-color: #d1d5db;
}

.ui-btn-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.ui-btn-danger {
    background: #ef4444;
    color: white;
    border-color: #ef4444;
}

.ui-btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
}

/* ==================== 空状态样式 ==================== */
.ui-empty-state {
    padding: 60px 20px !important;
    text-align: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: none !important;
}

.ui-empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: 400px;
    margin: 0 auto;
}

.ui-empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ui-empty-icon i {
    font-size: 32px;
    color: #64748b;
}

.ui-empty-text {
    text-align: center;
}

.ui-empty-title {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #374151;
}

.ui-empty-description {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 640px) {
    .ui-message {
        left: 20px;
        right: 20px;
        min-width: auto;
        transform: translateY(-100%);
    }
    
    .ui-message.ui-message-show {
        transform: translateY(0);
    }
    
    .ui-dialog {
        margin: 20px;
        min-width: auto;
        width: calc(100% - 40px);
    }
    
    .ui-empty-icon {
        width: 60px;
        height: 60px;
    }
    
    .ui-empty-icon i {
        font-size: 24px;
    }
    
    .ui-empty-title {
        font-size: 16px;
    }
}

/* ==================== 侧边栏样式 ==================== */
.sidebar {
    width: 250px;
    min-height: calc(100vh - 64px);
    background-color: #f9fafb;
    border-right: 1px solid #e5e7eb;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
    border-right: 4px solid transparent;
}

.menu-item:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.menu-item.active {
    background-color: #eff6ff;
    color: #2563eb;
    border-right-color: #2563eb;
}

.menu-item i {
    width: 24px;
    margin-right: 12px;
    text-align: center;
}

/* 主内容区域样式 */
.content {
    margin-left: 250px;
    min-height: calc(100vh - 64px);
}

/* 侧边栏响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }

    .content {
        margin-left: 200px;
    }
}

@media (max-width: 640px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .content {
        margin-left: 0;
    }
}
