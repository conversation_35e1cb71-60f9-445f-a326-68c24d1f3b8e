-- 创建菜单表
CREATE TABLE IF NOT EXISTS `sys_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `menu_key` varchar(100) NOT NULL COMMENT '菜单标识',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `sort` int DEFAULT '0' COMMENT '显示顺序',
  `menu_type` char(1) DEFAULT 'C' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '1' COMMENT '菜单状态（0隐藏 1显示）',
  `status` char(1) DEFAULT '1' COMMENT '菜单状态（0禁用 1正常）',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除（0未删除 1已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_menu_key` (`menu_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单权限表';

-- 插入基础菜单数据
INSERT INTO `sys_menu` (`id`, `menu_name`, `menu_key`, `parent_id`, `path`, `icon`, `sort`, `menu_type`, `visible`, `status`, `remark`) VALUES
(1, '首页', 'dashboard', 0, '/dashboard', 'fas fa-home', 1, 'C', '1', '1', '系统首页'),
(2, '患者管理', 'patients', 0, '/patients', 'fas fa-users', 2, 'C', '1', '1', '患者信息管理'),
(3, '问卷管理', 'questionnaires', 0, '/questionnaires', 'fas fa-clipboard-list', 3, 'C', '1', '1', '问卷调查管理'),
(4, '报告管理', 'reports', 0, '/reports', 'fas fa-file-medical', 4, 'C', '1', '1', '睡眠报告管理'),
(5, '消息管理', 'messages', 0, '/messages', 'fas fa-envelope', 5, 'C', '1', '1', '消息通知管理'),
(6, '系统管理', 'system', 0, '/system', 'fas fa-cog', 6, 'C', '1', '1', '系统设置管理');

-- 创建角色菜单关联表
CREATE TABLE IF NOT EXISTS `sys_role_menu` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色和菜单关联表';

-- 插入角色菜单关联数据
-- 医生角色（ID=1）拥有所有菜单权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6);

-- 社区管理员角色（ID=2）拥有所有菜单权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(2, 1), (2, 2), (2, 3), (2, 4), (2, 5), (2, 6);

-- 系统管理员角色（ID=3）拥有所有菜单权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
(3, 1), (3, 2), (3, 3), (3, 4), (3, 5), (3, 6);
