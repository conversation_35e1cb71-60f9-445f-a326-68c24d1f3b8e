<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>患者管理 - 居家睡眠远程管理平台</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/css/ui-components.css">
    <script src="/js/notification.js"></script>
</head>
<body class="bg-gray-100">
    <!-- 头部栏和侧边栏将通过JavaScript动态创建 -->
    
    <!-- 主内容区 -->
    <div class="content p-8" style="margin-top:64px;">
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">
                    <i class="fas fa-users mr-3 text-blue-600"></i>
                    患者管理
                </h1>
                <p class="text-gray-600 mb-6">管理和查看所有患者信息</p>
                
                <!-- 功能按钮 -->
                <div class="mb-6">
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        添加患者
                    </button>
                </div>
                
                <!-- 患者列表 -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">患者姓名</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年龄</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张三</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">138****1234</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        正常
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3">查看</a>
                                    <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</a>
                                    <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李四</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">38</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">女</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">139****5678</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        观察中
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3">查看</a>
                                    <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</a>
                                    <a href="#" class="text-red-600 hover:text-red-900">删除</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/ui-components.js"></script>
    <script src="/js/header.js"></script>
    <script src="/js/sidebar.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化统一头部栏
            HeaderComponent.init();
            
            // 初始化统一侧边栏
            SidebarComponent.init('patients');
        });
    </script>
</body>
</html>
