<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区端登录 - 居家睡眠远程管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="/js/notification.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="flex items-center justify-center">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto bg-white rounded-2xl shadow-2xl p-10">
            <div class="flex flex-col items-center mb-8">
                <img src="/images/logo.png" alt="logo" class="h-12 mb-4">
                <h1 class="text-2xl font-bold text-gray-800">居家睡眠远程管理平台</h1>
                <p class="text-gray-600 mt-2 px-4 py-1 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">社区端登录</p>
            </div>
            
            <form id="loginForm" class="space-y-8">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <div class="mt-1 relative rounded-xl shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <i class="fas fa-user text-purple-400"></i>
                        </div>
                        <input type="text" id="username" name="username" required
                               class="focus:ring-purple-500 focus:border-purple-500 block w-full pl-12 pr-4 py-3 text-base border-2 border-gray-200 rounded-xl transition-all duration-200 focus:border-purple-500 focus:bg-purple-50 bg-gray-50" 
                               placeholder="请输入用户名">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <div class="mt-1 relative rounded-xl shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-purple-400"></i>
                        </div>
                        <input type="password" id="password" name="password" required
                               class="focus:ring-purple-500 focus:border-purple-500 block w-full pl-12 pr-4 py-3 text-base border-2 border-gray-200 rounded-xl transition-all duration-200 focus:border-purple-500 focus:bg-purple-50 bg-gray-50" 
                               placeholder="请输入密码">
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label class="ml-2 block text-sm text-gray-900">记住我</label>
                    </div>
                    <a href="#" onclick="showForgotPassword()" class="text-sm text-purple-600 hover:text-purple-500">忘记密码？</a>
                </div>

                <div>
                    <button type="submit" 
                            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-base font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200">
                        登录
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // 安全调用notification的辅助函数
        function safeNotification(method, message, duration) {
            if (window.notification && window.notification[method]) {
                window.notification[method](message, duration);
            } else {
                // 降级处理
                console.log(`${method.toUpperCase()}: ${message}`);
                alert(message);
            }
        }

        // 忘记密码功能
        function showForgotPassword() {
            safeNotification('info', '请联系系统管理员重置密码', 5000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                safeNotification('warning', '请输入用户名和密码');
                return;
            }

            // 显示加载状态
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '登录中...';
            submitBtn.disabled = true;

            try {
                const response = await axios.post('/api/auth/login', {
                    username: username,
                    password: password
                });

                if (response.data.code === 200) {
                    safeNotification('success', '登录成功，正在跳转...');

                    // 保存token
                    localStorage.setItem('token', response.data.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(response.data.data));

                    // 延迟跳转，让用户看到成功提示
                    setTimeout(() => {
                        window.location.href = '/community/dashboard';
                    }, 1000);
                } else {
                    safeNotification('error', response.data.message || '登录失败');
                }
            } catch (error) {
                console.error('登录错误:', error);
                if (error.response && error.response.data && error.response.data.message) {
                    safeNotification('error', error.response.data.message);
                } else {
                    safeNotification('error', '登录失败，请检查网络连接');
                }
            } finally {
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
            });
        });
    </script>
</body>
</html>
