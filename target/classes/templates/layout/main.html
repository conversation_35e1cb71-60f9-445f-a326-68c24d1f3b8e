<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle} + ' - 居家睡眠远程管理平台'">居家睡眠远程管理平台</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="/js/notification.js"></script>
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-light: #dbeafe;
            --primary-dark: #2563eb;
        }
        
        .theme-doctor {
            --primary-color: #10b981;
            --primary-light: #d1fae5;
            --primary-dark: #059669;
        }
        
        .theme-community {
            --primary-color: #3b82f6;
            --primary-light: #dbeafe;
            --primary-dark: #2563eb;
        }
        
        .theme-system {
            --primary-color: #8b5cf6;
            --primary-light: #ede9fe;
            --primary-dark: #7c3aed;
        }
        
        .sidebar {
            width: 250px;
            min-height: calc(100vh - 64px);
        }
        
        .content {
            margin-left: 250px;
            min-height: calc(100vh - 64px);
        }
        
        .menu-item {
            transition: all 0.2s ease;
        }
        
        .menu-item:hover {
            background-color: #f3f4f6;
        }
        
        .menu-item.active {
            background-color: var(--primary-light);
            border-left: 4px solid var(--primary-color);
            color: var(--primary-dark);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        .text-primary {
            color: var(--primary-color);
        }
        
        .bg-primary {
            background-color: var(--primary-color);
        }
        
        .bg-primary-light {
            background-color: var(--primary-light);
        }
        
        .border-primary {
            border-color: var(--primary-color);
        }
    </style>
    <th:block th:fragment="head-extra">
        <!-- 页面特定的head内容 -->
    </th:block>
</head>
<body class="bg-gray-100" th:classappend="${userRole == 'doctor'} ? 'theme-doctor' : (${userRole == 'community'} ? 'theme-community' : 'theme-system')">
    <!-- 头部栏 -->
    <div class="fixed top-0 left-0 right-0 h-16 bg-white shadow flex items-center justify-between z-40 px-6 w-full border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <img src="/images/logo.png" alt="logo" class="h-10 w-auto">
            <div class="flex items-center">
                <span class="text-xl font-bold text-gray-800">居家睡眠远程管理平台</span>
                <span th:class="'ml-2 px-2 py-0.5 rounded-full text-sm font-medium ' + 
                    (${userRole == 'doctor'} ? 'bg-green-100 text-green-600' : 
                     ${userRole == 'community'} ? 'bg-blue-100 text-blue-600' : 
                     'bg-purple-100 text-purple-600')" 
                     th:text="${userRole == 'doctor'} ? '医生端' : (${userRole == 'community'} ? '社区端' : '系统管理')">
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <!-- 通知图标 -->
            <div class="relative">
                <button class="text-gray-500 hover:text-gray-700 relative">
                    <i class="fas fa-bell text-xl"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
                </button>
            </div>
            <!-- 用户信息 -->
            <div class="flex items-center space-x-2">
                <div th:class="'w-8 h-8 rounded-full flex items-center justify-center ' + 
                    (${userRole == 'doctor'} ? 'bg-green-100' : 
                     ${userRole == 'community'} ? 'bg-blue-100' : 
                     'bg-purple-100')">
                    <i th:class="'text-white ' + 
                        (${userRole == 'doctor'} ? 'fas fa-user-md text-green-600' : 
                         ${userRole == 'community'} ? 'fas fa-users text-blue-600' : 
                         'fas fa-cog text-purple-600')"></i>
                </div>
                <span class="text-sm font-medium text-gray-700" id="currentUser">用户</span>
            </div>
            <!-- 退出按钮 -->
            <button onclick="logout()" class="text-gray-500 hover:text-red-600 transition-colors">
                <i class="fas fa-sign-out-alt"></i>
                <span class="ml-1">退出</span>
            </button>
        </div>
    </div>
    
    <!-- 左侧菜单栏 -->
    <div class="sidebar fixed top-16 left-0 bg-gray-50 shadow-lg z-30 border-t border-gray-200">
        <nav class="mt-4" th:fragment="sidebar">
            <!-- 通用菜单项 -->
            <a href="/dashboard" id="menu-dashboard" class="menu-item flex items-center px-4 py-3 text-gray-600">
                <i class="fas fa-home w-6"></i>
                <span>首页</span>
            </a>
            
            <!-- 医生端和社区端共有的菜单 -->
            <div th:if="${userRole == 'doctor' or userRole == 'community'}">
                <a href="/patients" id="menu-patients" class="menu-item flex items-center px-4 py-3 text-gray-600">
                    <i class="fas fa-users w-6"></i>
                    <span th:text="${userRole == 'doctor'} ? '患者管理' : '居民管理'">用户管理</span>
                </a>
                <a href="/questionnaires" id="menu-questionnaires" class="menu-item flex items-center px-4 py-3 text-gray-600">
                    <i class="fas fa-clipboard-list w-6"></i>
                    <span>问卷管理</span>
                </a>
                <a href="/reports" id="menu-reports" class="menu-item flex items-center px-4 py-3 text-gray-600">
                    <i class="fas fa-file-medical w-6"></i>
                    <span>报告管理</span>
                </a>
                <a href="/messages" id="menu-messages" class="menu-item flex items-center px-4 py-3 text-gray-600">
                    <i class="fas fa-envelope w-6"></i>
                    <span>消息管理</span>
                </a>
            </div>
            
            <!-- 社区端特有菜单 -->
            <div th:if="${userRole == 'community'}">
                <a href="/activities" id="menu-activities" class="menu-item flex items-center px-4 py-3 text-gray-600">
                    <i class="fas fa-calendar-alt w-6"></i>
                    <span>活动管理</span>
                </a>
                <a href="/notifications" id="menu-notifications" class="menu-item flex items-center px-4 py-3 text-gray-600">
                    <i class="fas fa-bullhorn w-6"></i>
                    <span>通知管理</span>
                </a>
            </div>
            
            <!-- 系统管理菜单（所有角色都可以看到，但权限控制在后端） -->
            <div class="border-t border-gray-200 mt-4 pt-4">
                <a href="/system" id="menu-system" class="menu-item flex items-center px-4 py-3 text-gray-600">
                    <i class="fas fa-cog w-6"></i>
                    <span>系统管理</span>
                </a>
            </div>
        </nav>
    </div>

    <!-- 右侧内容区 -->
    <div class="content p-8" style="margin-top:64px;">
        <th:block th:fragment="content">
            <!-- 页面内容将在子模板中定义 -->
        </th:block>
    </div>

    <!-- 通用JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // 检查登录状态并设置用户信息
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/';
                return false;
            }
            
            try {
                const user = JSON.parse(userInfo);
                document.getElementById('currentUser').textContent = user.realName || user.username;
                
                // 设置axios默认header
                axios.defaults.headers.common['Authorization'] = 'Bearer ' + token;
                
                return true;
            } catch (error) {
                console.error('解析用户信息失败:', error);
                window.location.href = '/';
                return false;
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            if (window.notification) {
                window.notification.success('已安全退出');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } else {
                window.location.href = '/';
            }
        }

        // 设置活动菜单项
        function setActiveMenu(menuId) {
            // 清除所有菜单的活动状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 设置当前菜单为活动状态
            const activeMenu = document.getElementById(menuId);
            if (activeMenu) {
                activeMenu.classList.add('active');
            }
        }

        // 页面加载时检查认证
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });
    </script>
    
    <th:block th:fragment="scripts">
        <!-- 页面特定的脚本 -->
    </th:block>
</body>
</html>
