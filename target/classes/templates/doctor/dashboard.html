<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医生工作台 - 居家睡眠远程管理平台</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="/js/notification.js"></script>
    <style>
        .sidebar {
            width: 250px;
            min-height: 100vh;
        }
        .content {
            margin-left: 250px;
        }
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .menu-item {
            transition: all 0.2s ease;
        }
        .menu-item:hover {
            background-color: #f3f4f6;
        }
        .menu-item.active {
            background-color: #dbeafe;
            border-left: 4px solid #3b82f6;
            color: #2563eb;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 头部栏 -->
    <div class="fixed top-0 left-0 right-0 h-16 bg-white shadow flex items-center justify-between z-40 px-6 w-full border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <img src="/images/logo.png" alt="logo" class="h-10 w-auto">
            <div class="flex items-center">
                <span class="text-xl font-bold text-gray-800">居家睡眠远程管理平台</span>
                <span class="ml-2 px-2 py-0.5 bg-green-100 text-green-600 rounded-full text-sm font-medium">医生端</span>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <!-- 通知图标 -->
            <div class="relative">
                <button class="text-gray-500 hover:text-gray-700 relative">
                    <i class="fas fa-bell text-xl"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
                </button>
            </div>
            <!-- 用户信息 -->
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-user-md text-green-600"></i>
                </div>
                <span class="text-sm font-medium text-gray-700" id="currentUser">张医生</span>
            </div>
            <!-- 退出按钮 -->
            <button onclick="logout()" class="text-gray-500 hover:text-red-600 transition-colors">
                <i class="fas fa-sign-out-alt"></i>
                <span class="ml-1">退出</span>
            </button>
        </div>
    </div>

    <!-- 左侧菜单栏 -->
    <div class="sidebar fixed top-16 left-0 bg-gray-50 shadow-lg z-30 border-t border-gray-200" style="height:calc(100vh-4rem);">
        <nav class="mt-4">
            <a href="/doctor/dashboard" id="menu-dashboard" class="menu-item active flex items-center px-4 py-3">
                <i class="fas fa-home w-6"></i>
                <span>首页</span>
            </a>
            <a href="/doctor/patients" id="menu-patients" class="menu-item flex items-center px-4 py-3 text-gray-600">
                <i class="fas fa-users w-6"></i>
                <span>患者管理</span>
            </a>
            <a href="/doctor/questionnaires" id="menu-questionnaires" class="menu-item flex items-center px-4 py-3 text-gray-600">
                <i class="fas fa-clipboard-list w-6"></i>
                <span>问卷管理</span>
            </a>
            <a href="/doctor/reports" id="menu-reports" class="menu-item flex items-center px-4 py-3 text-gray-600">
                <i class="fas fa-file-medical w-6"></i>
                <span>报告管理</span>
            </a>
            <a href="/doctor/messages" id="menu-messages" class="menu-item flex items-center px-4 py-3 text-gray-600">
                <i class="fas fa-envelope w-6"></i>
                <span>消息管理</span>
            </a>
            <a href="/system" id="menu-system" class="menu-item flex items-center px-4 py-3 text-gray-600">
                <i class="fas fa-cog w-6"></i>
                <span>系统管理</span>
            </a>
        </nav>
    </div>

    <!-- 右侧内容区 -->
    <div class="content p-8" style="margin-top:64px;">
        <!-- 欢迎信息 -->
        <div class="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user-md text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-gray-900">欢迎回来，<span id="welcomeName">张医生</span>！</h1>
                        <p class="text-gray-600">今天是 <span id="currentDate"></span>，祝您工作愉快！</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-users text-blue-600"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">管理患者</dt>
                                <dd class="text-lg font-medium text-gray-900">156</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-file-medical text-green-600"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">待处理报告</dt>
                                <dd class="text-lg font-medium text-gray-900">23</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-clipboard-list text-yellow-600"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">本月问卷</dt>
                                <dd class="text-lg font-medium text-gray-900">89</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-chart-line text-purple-600"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">改善率</dt>
                                <dd class="text-lg font-medium text-gray-900">78%</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">快捷操作</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-user-plus text-blue-600"></i>
                        </div>
                        <span class="text-sm font-medium text-gray-900">添加患者</span>
                    </button>
                    
                    <button class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-file-medical-alt text-green-600"></i>
                        </div>
                        <span class="text-sm font-medium text-gray-900">查看报告</span>
                    </button>
                    
                    <button class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-edit text-yellow-600"></i>
                        </div>
                        <span class="text-sm font-medium text-gray-900">创建问卷</span>
                    </button>
                    
                    <button class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-chart-bar text-purple-600"></i>
                        </div>
                        <span class="text-sm font-medium text-gray-900">数据分析</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">最近活动</h3>
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span class="text-sm text-gray-600">患者张三提交了睡眠质量问卷</span>
                        <span class="text-xs text-gray-400">2小时前</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <span class="text-sm text-gray-600">新增患者李四的睡眠监测报告</span>
                        <span class="text-xs text-gray-400">4小时前</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                        <span class="text-sm text-gray-600">患者王五的治疗方案需要调整</span>
                        <span class="text-xs text-gray-400">6小时前</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                        <span class="text-sm text-gray-600">完成了本周的数据统计分析</span>
                        <span class="text-xs text-gray-400">1天前</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // 检查登录状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (!token || !userInfo) {
                window.location.href = '/doctor/login';
                return;
            }
            
            try {
                const user = JSON.parse(userInfo);
                document.getElementById('userName').textContent = user.realName || user.username;
                document.getElementById('welcomeName').textContent = user.realName || user.username;
            } catch (error) {
                console.error('解析用户信息失败:', error);
                window.location.href = '/doctor/login';
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            notification.success('已安全退出');
            setTimeout(() => {
                window.location.href = '/doctor/login';
            }, 1000);
        }

        // 设置当前日期
        function setCurrentDate() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long'
            };
            document.getElementById('currentDate').textContent = now.toLocaleDateString('zh-CN', options);
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            setCurrentDate();
        });
    </script>
</body>
</html>
