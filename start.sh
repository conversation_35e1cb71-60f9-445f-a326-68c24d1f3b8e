#!/bin/bash

# 居家睡眠远程管理平台启动脚本

echo "==================================="
echo "居家睡眠远程管理平台启动脚本"
echo "==================================="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请安装JDK 17或更高版本"
    exit 1
fi

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F '.' '{print $1}')
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "错误: Java版本过低，需要JDK 17或更高版本"
    exit 1
fi

echo "Java版本检查通过"

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven环境，请安装Maven 3.6或更高版本"
    exit 1
fi

echo "Maven环境检查通过"

# 检查MySQL连接
echo "检查数据库连接..."
mysql -u root -p -e "SELECT 1;" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: 无法连接到MySQL数据库，请确保MySQL服务已启动并配置正确"
    echo "请手动执行以下步骤："
    echo "1. 启动MySQL服务"
    echo "2. 创建数据库: CREATE DATABASE sleep_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    echo "3. 执行初始化脚本: mysql -u root -p sleep_management < src/main/resources/sql/init.sql"
    echo ""
    read -p "是否继续启动应用？(y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 编译项目
echo "编译项目..."
mvn clean compile
if [ $? -ne 0 ]; then
    echo "错误: 项目编译失败"
    exit 1
fi

echo "项目编译成功"

# 启动应用
echo "启动应用..."
echo "应用将在 http://localhost:8080 启动"
echo "默认账号："
echo "  管理员: admin / admin123"
echo "  医生: doctor / admin123"
echo "  社区管理员: community / admin123"
echo ""
echo "按 Ctrl+C 停止应用"
echo "==================================="

mvn spring-boot:run
