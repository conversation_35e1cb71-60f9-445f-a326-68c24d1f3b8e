import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class PasswordGenerator {
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password = "admin123";
        String encoded = encoder.encode(password);
        System.out.println("Original password: " + password);
        System.out.println("Encoded password: " + encoded);
        
        // 验证密码
        boolean matches = encoder.matches(password, encoded);
        System.out.println("Password matches: " + matches);
        
        // 验证数据库中的密码
        String dbPassword = "$2a$10$7JB720yubVSOfvVWbfXCNOaWpDDvNVjpBBUuUdwXaWqOdd4sgO1S6";
        boolean dbMatches = encoder.matches(password, dbPassword);
        System.out.println("DB password matches: " + dbMatches);
    }
}
