@echo off
chcp 65001 >nul

echo ===================================
echo 居家睡眠远程管理平台启动脚本
echo ===================================

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请安装JDK 17或更高版本
    pause
    exit /b 1
)

echo Java环境检查通过

REM 检查Maven环境
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven环境，请安装Maven 3.6或更高版本
    pause
    exit /b 1
)

echo Maven环境检查通过

REM 编译项目
echo 编译项目...
call mvn clean compile
if %errorlevel% neq 0 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo 项目编译成功

REM 启动应用
echo 启动应用...
echo 应用将在 http://localhost:8080 启动
echo 默认账号：
echo   管理员: admin / admin123
echo   医生: doctor / admin123
echo   社区管理员: community / admin123
echo.
echo 按 Ctrl+C 停止应用
echo ===================================

call mvn spring-boot:run

pause
