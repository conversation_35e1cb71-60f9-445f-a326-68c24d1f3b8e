2025-07-18 16:16:13.270 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 16:16:13.283 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 48763 (/Users/<USER>/AI/vscode/sleephome/target/classes started by song<PERSON><PERSON>an in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 16:16:13.283 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 16:16:13.284 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 16:16:13.735 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port(s): 8888 (http)
2025-07-18 16:16:13.738 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 16:16:13.739 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 16:16:13.739 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 16:16:13.771 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 16:16:13.771 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 468 ms
2025-07-18 16:16:14.040 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 16:16:14.130 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@48cf8414, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@27dbaa33, org.springframework.security.web.context.SecurityContextHolderFilter@6f3b13d0, org.springframework.security.web.header.HeaderWriterFilter@701d2b59, org.springframework.security.web.authentication.logout.LogoutFilter@455f4483, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@1377b1a0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6e9a10cd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@59b447a4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15d65fcf, org.springframework.security.web.session.SessionManagementFilter@65d73bd, org.springframework.security.web.access.ExceptionTranslationFilter@d62472f, org.springframework.security.web.access.intercept.AuthorizationFilter@7e0986c9]
2025-07-18 16:16:14.215 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8888"]
2025-07-18 16:16:14.225 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8888 (http) with context path ''
2025-07-18 16:16:14.228 [main] INFO  com.sleephome.SleepManagementSystemApplication - Started SleepManagementSystemApplication in 1.121 seconds (process running for 1.239)
2025-07-18 16:16:18.183 [http-nio-8888-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 16:16:18.183 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 16:16:18.184 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 16:16:18.186 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:16:18.188 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:16:18.188 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 58E68FA5FCB317DF33319BA1924EB3F7
2025-07-18 16:16:18.188 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:16:18.282 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:16:18.282 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:16:18.282 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:16:18.282 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:16:18.282 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 58E68FA5FCB317DF33319BA1924EB3F7
2025-07-18 16:16:18.282 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 58E68FA5FCB317DF33319BA1924EB3F7
2025-07-18 16:16:18.284 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:16:18.284 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:16:18.284 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 58E68FA5FCB317DF33319BA1924EB3F7
2025-07-18 16:16:18.290 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:16:18.291 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:16:18.304 [http-nio-8888-exec-4] DEBUG o.s.s.web.savedrequest.HttpSessionRequestCache - Saved request http://localhost:8888/.well-known/appspecific/com.chrome.devtools.json?continue to session
2025-07-18 16:16:18.306 [http-nio-8888-exec-4] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8888/doctor/login
2025-07-18 16:16:18.309 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:16:18.309 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:16:18.309 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:16:22.453 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:16:22.454 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:16:22.457 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:16:22.600 [http-nio-8888-exec-9] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 16:16:22.750 [http-nio-8888-exec-9] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@396a0db3
2025-07-18 16:16:22.750 [http-nio-8888-exec-9] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 16:16:22.850 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:16:23.890 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:16:23.891 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:23.891 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:16:23.960 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:16:23.960 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:16:23.960 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:16:23.962 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:16:23.963 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:16:23.964 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:23.964 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:16:23.964 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:23.965 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:23.970 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:16:23.977 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:23.977 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 16:16:24.014 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 16:16:24.015 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 16:16:24.019 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:24.022 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:16:24.022 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:16:24.027 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:24.034 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:16:24.035 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:16:24.035 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:16:24.037 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:16:24.037 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:16:24.041 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:24.040 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:24.041 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:16:24.042 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:24.043 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:16:24.044 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@300c770, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:16:24.045 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
