2025-07-18 21:59:28.369 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing PUT /api/system/user
2025-07-18 21:59:28.372 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 21:59:28.372 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured PUT /api/system/user
2025-07-18 21:59:28.378 [http-nio-8888-exec-5] WARN  c.s.common.exception.GlobalExceptionHandler - 参数校验异常: password: 密码不能为空; 
2025-07-18 22:00:00.587 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:00:00.596 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:00.597 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:00:00.605 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:00:00.606 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:00:01.221 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:00:01.222 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:01.222 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:00:01.223 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:00:01.223 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:00:01.453 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:00:01.454 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:01.454 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:00:01.455 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:00:01.455 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:00:02.631 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 22:00:02.632 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:02.632 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 22:00:02.651 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:00:02.651 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:00:02.651 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:00:02.652 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:00:02.652 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:00:02.653 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:00:02.654 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:02.654 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:02.654 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:02.656 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:00:02.657 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:00:02.660 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:02.664 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:00:02.664 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:00:02.666 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:02.674 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:00:02.675 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:02.675 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:00:02.676 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:00:02.676 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:00:03.913 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:00:03.914 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:00:03.914 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:00:03.914 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:00:03.915 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:01:15.329 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:01:15.333 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:01:15.333 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:01:15.339 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:01:15.340 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:01:17.152 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles/1
2025-07-18 22:01:17.154 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:01:17.154 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles/1
2025-07-18 22:01:17.156 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:01:17.158 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:01:17.158 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:01:48.372 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles/1
2025-07-18 22:01:48.376 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:01:48.376 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles/1
2025-07-18 22:01:48.384 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:01:48.385 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:01:48.385 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:02:07.929 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/5
2025-07-18 22:02:07.935 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:02:07.935 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/5
2025-07-18 22:02:07.938 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:02:07.938 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:02:12.218 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 22:02:12.219 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:02:12.220 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 22:02:12.220 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:02:12.220 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:02:15.630 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 22:02:15.632 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:02:15.632 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 22:02:15.632 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:02:15.632 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:02:17.758 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/2
2025-07-18 22:02:17.760 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:02:17.760 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/2
2025-07-18 22:02:17.761 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:02:17.761 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:02:20.808 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/3
2025-07-18 22:02:20.810 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:02:20.810 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/3
2025-07-18 22:02:20.810 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:02:20.811 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:02:57.792 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:02:57.797 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@219abad6, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:02:57.797 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:02:57.806 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:02:57.806 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:07:49.854 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 22:07:49.854 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 22:07:49.855 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 22:07:49.947 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 22:07:58.590 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/1
2025-07-18 22:07:58.592 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5e5cce61, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:07:58.593 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/1
2025-07-18 22:07:58.597 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:07:58.606 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:08:40.250 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 22:08:40.253 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5e5cce61, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:08:40.253 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 22:08:40.255 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:08:40.256 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:09:57.779 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:09:57.781 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5e5cce61, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:09:57.781 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:09:57.785 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:09:57.786 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:10:24.876 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 22:10:24.886 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-18 22:10:40.197 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 22:10:40.218 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 21753 (/Users/<USER>/AI/vscode/sleephome/target/classes started by songdaijian in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 22:10:40.218 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 22:10:40.219 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 22:10:40.827 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 22:10:40.831 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 22:10:40.833 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 22:10:40.833 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 22:10:40.872 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 22:10:40.872 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 632 ms
2025-07-18 22:10:41.206 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 22:10:41.316 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@f59da34, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@752973de, org.springframework.security.web.context.SecurityContextHolderFilter@59b447a4, org.springframework.security.web.header.HeaderWriterFilter@3ed7dd70, org.springframework.security.web.authentication.logout.LogoutFilter@300bb303, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@3b021664, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@579846cc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@71f0806b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@52a67293, org.springframework.security.web.session.SessionManagementFilter@2964511, org.springframework.security.web.access.ExceptionTranslationFilter@65d73bd, org.springframework.security.web.access.intercept.AuthorizationFilter@15d65fcf]
2025-07-18 22:10:41.435 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8888"]
2025-07-18 22:10:41.448 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8888 (http) with context path ''
2025-07-18 22:10:41.453 [main] INFO  com.sleephome.SleepManagementSystemApplication - Started SleepManagementSystemApplication in 1.476 seconds (process running for 1.653)
2025-07-18 22:11:06.272 [http-nio-8888-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 22:11:06.273 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 22:11:06.275 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-18 22:11:06.283 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 22:11:06.287 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 22:11:06.287 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 3884BF430F4917244326BCD2BB011453
2025-07-18 22:11:06.306 [http-nio-8888-exec-1] DEBUG o.s.s.web.savedrequest.HttpSessionRequestCache - Saved request http://localhost:8888/system?continue to session
2025-07-18 22:11:06.307 [http-nio-8888-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8888/doctor/login
2025-07-18 22:11:06.311 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 22:11:06.312 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 22:11:06.312 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 22:11:06.409 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:11:06.409 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:11:06.409 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 22:11:06.409 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 22:11:06.412 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:11:06.413 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:11:10.655 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 22:11:10.656 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 22:11:10.656 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 22:11:10.786 [http-nio-8888-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 22:11:10.952 [http-nio-8888-exec-7] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7d65ada
2025-07-18 22:11:10.953 [http-nio-8888-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 22:11:11.078 [http-nio-8888-exec-7] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 22:11:12.115 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 22:11:12.116 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:12.116 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 22:11:12.200 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:11:12.200 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:11:12.200 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:11:12.202 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:11:12.202 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:11:12.202 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:11:12.203 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:12.203 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:12.203 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:12.208 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:11:12.208 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:11:12.209 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:13.285 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 22:11:13.287 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:13.288 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 22:11:13.318 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:11:13.318 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:11:13.318 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:11:13.319 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:11:13.319 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:11:13.320 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:11:13.321 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:13.321 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:13.321 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:13.324 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:11:13.326 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:11:13.329 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:13.334 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:11:13.335 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:11:13.335 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:13.344 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:11:13.354 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:13.354 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:11:13.359 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:13.364 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:13.386 [http-nio-8888-exec-10] WARN  c.b.m.core.toolkit.support.ReflectLambdaMeta - Unable to make field private final java.lang.Class java.lang.invoke.SerializedLambda.capturingClass accessible: module java.base does not "opens java.lang.invoke" to unnamed module @1ad282e0
2025-07-18 22:11:14.763 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:11:14.768 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:14.768 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:11:14.780 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:11:14.781 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:11:18.271 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:11:18.272 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:18.273 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:11:18.274 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:11:18.274 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:11:18.819 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:11:18.822 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:18.822 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:11:18.825 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:18.826 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:20.073 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/3
2025-07-18 22:11:20.076 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:20.076 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/3
2025-07-18 22:11:20.080 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:20.081 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:23.127 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing PUT /api/system/user
2025-07-18 22:11:23.128 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:23.128 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured PUT /api/system/user
2025-07-18 22:11:23.156 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.edit(com.sleephome.dto.SysUserEditDto); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:23.157 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.edit(com.sleephome.dto.SysUserEditDto); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:23.284 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:11:23.285 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:23.285 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:11:23.285 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:23.286 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:26.295 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/3
2025-07-18 22:11:26.325 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:26.325 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/3
2025-07-18 22:11:26.360 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:26.360 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:26.759 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 22:11:26.760 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:26.761 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 22:11:26.778 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:11:26.780 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:11:26.784 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:11:26.784 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:26.787 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:11:26.792 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:26.793 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:11:26.794 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:11:26.795 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:26.804 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:11:26.806 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:11:26.810 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:26.969 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:11:26.970 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:11:26.972 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:26.988 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:11:26.989 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:26.990 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:11:26.991 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:26.991 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:36.124 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:11:36.128 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:36.129 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:11:36.133 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:11:36.134 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:11:38.099 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles/1
2025-07-18 22:11:38.105 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:38.105 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles/1
2025-07-18 22:11:38.119 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:11:38.122 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:38.122 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:11:39.737 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles/2
2025-07-18 22:11:39.744 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:39.744 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles/2
2025-07-18 22:11:39.755 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:11:39.765 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:39.766 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:11:40.492 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles/3
2025-07-18 22:11:40.502 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:40.502 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles/3
2025-07-18 22:11:40.512 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:11:40.517 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:40.517 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:11:40.876 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 22:11:40.876 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 22:11:40.876 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 22:11:40.989 [http-nio-8888-exec-6] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 22:11:43.001 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 22:11:43.004 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:43.004 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 22:11:43.010 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:11:43.015 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:11:50.401 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing PUT /api/system/user
2025-07-18 22:11:50.404 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@14dd9c9a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:50.404 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured PUT /api/system/user
2025-07-18 22:11:50.409 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.edit(com.sleephome.dto.SysUserEditDto); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:50.410 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.edit(com.sleephome.dto.SysUserEditDto); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:11:51.044 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/5
2025-07-18 22:11:51.045 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:11:51.045 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/5
2025-07-18 22:11:51.045 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:11:51.045 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:12:04.557 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:12:04.567 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:04.567 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:12:04.577 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:12:04.578 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:12:20.568 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/1
2025-07-18 22:12:20.571 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@14dd9c9a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:20.571 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/1
2025-07-18 22:12:20.573 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:12:20.574 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:12:22.736 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:12:22.736 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:22.737 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:12:22.738 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:12:22.738 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:12:24.227 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles/3
2025-07-18 22:12:24.230 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:24.230 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles/3
2025-07-18 22:12:24.234 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:12:24.235 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:24.235 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:12:31.037 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:12:31.043 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:31.043 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:12:31.046 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:12:31.047 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:12:32.384 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:12:32.385 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:32.385 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:12:32.385 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:12:32.386 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:12:34.239 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles/3
2025-07-18 22:12:34.251 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:34.252 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles/3
2025-07-18 22:12:34.259 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:12:34.264 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:34.264 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:12:38.194 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 22:12:38.204 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:38.204 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 22:12:38.208 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:12:38.209 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:38.209 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:12:41.684 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles/3
2025-07-18 22:12:41.685 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:41.685 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles/3
2025-07-18 22:12:41.688 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:12:41.691 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:41.691 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:12:42.567 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 22:12:42.568 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@14dd9c9a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:12:42.568 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 22:12:42.568 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:12:42.569 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:13:26.764 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 22:13:26.766 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@14dd9c9a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:13:26.766 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 22:13:26.767 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:13:26.767 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:13:30.166 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles/2
2025-07-18 22:13:30.170 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:13:30.170 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles/2
2025-07-18 22:13:30.173 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:13:30.175 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:13:30.175 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:14:31.337 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 22:14:31.343 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:14:31.343 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 22:14:31.347 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:14:31.348 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:15:41.937 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 22:15:41.941 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:41.942 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 22:15:42.019 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:15:42.030 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:15:42.032 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:15:42.035 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:15:42.044 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:15:42.044 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:15:42.046 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:42.048 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:42.048 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:42.050 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:15:42.067 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:15:42.157 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:42.161 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:15:42.161 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:15:42.162 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:42.193 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:15:42.194 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:42.194 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:15:42.196 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:15:42.197 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:15:45.854 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /messages
2025-07-18 22:15:45.874 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:45.875 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /messages
2025-07-18 22:15:45.877 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 22:15:45.877 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:45.877 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 22:15:47.469 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 22:15:47.470 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:47.470 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 22:15:47.491 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:15:47.491 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:15:47.491 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:15:47.492 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:15:47.492 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:15:47.492 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:15:47.494 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:47.494 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:47.495 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:47.504 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:15:47.505 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:15:47.505 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:47.514 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:15:47.515 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:47.515 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:15:47.516 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:15:47.516 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:15:48.443 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 22:15:48.444 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.444 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 22:15:48.458 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:15:48.458 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:15:48.458 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:15:48.460 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:15:48.460 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:15:48.460 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:15:48.462 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.462 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.463 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.466 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:15:48.469 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:15:48.470 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.476 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:15:48.477 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:15:48.477 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.484 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:15:48.485 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.485 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:15:48.486 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:15:48.486 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:15:48.914 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 22:15:48.915 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.915 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 22:15:48.933 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:15:48.933 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:15:48.933 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:15:48.934 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:15:48.934 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:15:48.935 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:15:48.936 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.936 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.936 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.938 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:15:48.938 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:15:48.940 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.945 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:15:48.945 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:15:48.946 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.950 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:15:48.950 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:48.950 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:15:48.951 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:15:48.951 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:15:50.742 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 22:15:50.743 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:50.743 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 22:15:50.758 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:15:50.758 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:15:50.758 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:15:50.758 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:15:50.758 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:15:50.759 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:15:50.760 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:50.760 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:50.760 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:50.763 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:15:50.764 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:15:50.765 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:50.770 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:15:50.770 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:15:50.770 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:50.778 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:15:50.779 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:50.779 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:15:50.780 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:15:50.780 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:15:56.469 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:15:56.470 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:56.471 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:15:56.472 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:15:56.472 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:15:59.404 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/1
2025-07-18 22:15:59.405 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:15:59.406 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/1
2025-07-18 22:15:59.406 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:15:59.406 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:11.622 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/3
2025-07-18 22:16:11.625 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:11.625 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/3
2025-07-18 22:16:11.626 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:11.627 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:14.038 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&sort=0&status=0&remark=
2025-07-18 22:16:14.039 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:14.039 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&sort=0&status=0&remark=
2025-07-18 22:16:14.055 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:16:14.055 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:16:14.057 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:14.057 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:16:14.058 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:16:14.060 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:16:14.061 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:16:14.062 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:16:14.063 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:14.064 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:14.065 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:16:14.067 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:14.072 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:16:14.073 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:16:14.075 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:14.080 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:16:14.081 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:14.081 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:16:14.081 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:16:14.081 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:16:16.250 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:16:16.250 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:16.251 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:16:16.251 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:16.251 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:18.777 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/3
2025-07-18 22:16:18.780 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:18.781 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/3
2025-07-18 22:16:18.781 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:18.782 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:22.921 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&sort=0&status=0&remark=
2025-07-18 22:16:22.922 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:22.922 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&sort=0&status=0&remark=
2025-07-18 22:16:22.933 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:16:22.933 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:16:22.933 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:16:22.933 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:16:22.934 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:16:22.934 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:16:22.934 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:22.935 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:22.936 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:22.936 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:16:22.937 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:16:22.939 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:22.944 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:16:22.945 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:16:22.945 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:22.953 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:16:22.953 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:22.953 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:16:22.954 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:16:22.954 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:16:25.181 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:16:25.182 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:25.182 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:16:25.182 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:25.182 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:27.593 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/3
2025-07-18 22:16:27.594 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:27.594 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/3
2025-07-18 22:16:27.594 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:27.595 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:36.584 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/5
2025-07-18 22:16:36.587 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:36.587 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/5
2025-07-18 22:16:36.589 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:16:36.589 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:16:38.401 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:16:38.402 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:38.402 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:16:38.403 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:16:38.403 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:16:39.766 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:16:39.767 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:39.767 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:16:39.768 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:39.769 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:41.749 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/3
2025-07-18 22:16:41.752 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:41.752 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/3
2025-07-18 22:16:41.752 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:41.753 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:50.680 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/3
2025-07-18 22:16:50.683 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:16:50.683 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/3
2025-07-18 22:16:50.684 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:16:50.684 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:07.106 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/3
2025-07-18 22:17:07.109 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:07.109 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/3
2025-07-18 22:17:07.110 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:07.110 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:11.980 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&sort=1&status=1&remark=
2025-07-18 22:17:11.982 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:11.982 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&sort=1&status=1&remark=
2025-07-18 22:17:12.007 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:17:12.007 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:17:12.007 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:17:12.009 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:17:12.009 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:17:12.010 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:17:12.012 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:12.012 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:12.012 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:12.014 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:17:12.015 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:17:12.017 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:12.026 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:17:12.026 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:17:12.027 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:12.034 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:17:12.035 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:12.035 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:17:12.037 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:17:12.037 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:17:13.856 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:17:13.858 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:13.858 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:17:13.860 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:13.860 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:15.561 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 22:17:15.563 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:15.563 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 22:17:15.564 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:15.564 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:18.080 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 22:17:18.083 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:18.083 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 22:17:18.084 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:18.084 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:20.122 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/3
2025-07-18 22:17:20.126 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:20.126 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/3
2025-07-18 22:17:20.127 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:20.128 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:17:24.906 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:17:24.907 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:24.907 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:17:24.908 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:17:24.908 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:17:25.933 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:17:25.933 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:17:25.933 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:17:25.933 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:17:25.934 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:18:07.461 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&sort=1&status=1&remark=
2025-07-18 22:18:07.463 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:18:07.463 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&sort=1&status=1&remark=
2025-07-18 22:18:07.489 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:18:07.489 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:18:07.490 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:18:07.491 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:18:07.491 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:18:07.491 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:18:07.501 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:18:07.501 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:18:07.501 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:18:07.503 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:18:07.503 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:18:07.505 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:18:07.511 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:18:07.512 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:18:07.512 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:18:07.521 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:18:07.521 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:18:07.521 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:18:07.521 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:18:07.522 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:18:10.760 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/3
2025-07-18 22:18:10.762 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:18:10.762 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/3
2025-07-18 22:18:10.762 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:18:10.763 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:18:15.868 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:18:15.869 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:18:15.869 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:18:15.870 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:18:15.871 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:18:59.106 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/1
2025-07-18 22:18:59.111 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:18:59.111 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/1
2025-07-18 22:18:59.119 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:18:59.122 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:19:17.039 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/1
2025-07-18 22:19:17.045 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:19:17.045 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/1
2025-07-18 22:19:17.048 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:19:17.048 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:19:53.442 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/5
2025-07-18 22:19:53.447 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:19:53.448 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/5
2025-07-18 22:19:53.452 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:19:53.453 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:19:59.955 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/5
2025-07-18 22:19:59.958 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:19:59.958 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/5
2025-07-18 22:19:59.959 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:19:59.960 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:20:02.471 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/4
2025-07-18 22:20:02.473 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:20:02.473 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/4
2025-07-18 22:20:02.474 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:20:02.475 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:20:05.012 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 22:20:05.013 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:20:05.013 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 22:20:05.014 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:20:05.014 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:20:07.758 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/2
2025-07-18 22:20:07.760 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:20:07.760 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/2
2025-07-18 22:20:07.760 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:20:07.760 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 22:20:10.474 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&sort=1&status=1&remark=
2025-07-18 22:20:10.475 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:20:10.475 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&sort=1&status=1&remark=
2025-07-18 22:20:10.487 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 22:20:10.487 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 22:20:10.487 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 22:20:10.488 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 22:20:10.488 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 22:20:10.489 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 22:20:10.492 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:20:10.492 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:20:10.492 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:20:10.494 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 22:20:10.495 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 22:20:10.497 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:20:10.500 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 22:20:10.501 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 22:20:10.501 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:20:10.508 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 22:20:10.509 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:20:10.509 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 22:20:10.510 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:20:10.511 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 22:21:08.200 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:21:08.206 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:21:08.206 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 22:21:08.211 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:21:08.212 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:21:09.721 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:21:09.722 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@c4154e9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:21:09.722 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/operlog/list?pageNum=1&pageSize=10
2025-07-18 22:21:09.724 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:21:09.724 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysOperationLogController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysOperationLog); target is of class [com.sleephome.controller.SysOperationLogController]
2025-07-18 22:24:20.312 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing PUT /api/system/role
2025-07-18 22:24:20.313 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@14dd9c9a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:24:20.313 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured PUT /api/system/role
2025-07-18 22:24:20.323 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.edit(com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:24:20.324 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.edit(com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:24:20.329 [http-nio-8888-exec-4] WARN  c.s.common.exception.GlobalExceptionHandler - 业务异常: 不允许操作超级管理员角色
2025-07-18 22:24:47.655 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing PUT /api/system/role
2025-07-18 22:24:47.656 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@14dd9c9a, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 22:24:47.656 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured PUT /api/system/role
2025-07-18 22:24:47.658 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.edit(com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:24:47.659 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.edit(com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 22:24:47.659 [http-nio-8888-exec-5] WARN  c.s.common.exception.GlobalExceptionHandler - 业务异常: 不允许操作超级管理员角色
2025-07-18 23:01:41.190 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Retrograde clock change detected (housekeeper delta=29s855ms), soft-evicting connections from pool.
2025-07-18 23:12:00.249 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 23:12:00.252 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
