2025-07-18 16:28:03.697 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:28:03.699 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:28:03.782 [http-nio-8888-exec-4] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:28:03.782 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@8518360, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.800 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:28:04.800 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.801 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:28:04.833 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:28:04.833 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:28:04.835 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:28:04.835 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:28:04.838 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.840 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.840 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:28:04.843 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.843 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:28:04.849 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:28:04.850 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.850 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 16:28:04.900 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:28:04.900 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:28:04.900 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.915 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:28:04.916 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:28:04.918 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.929 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:28:04.929 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:28:04.929 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:28:04.934 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:28:04.933 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:28:04.935 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.936 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.936 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.936 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:28:04.941 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:28:04.942 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.943 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 16:28:48.630 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:28:48.643 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:28:48.744 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:48.807 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:28:48.809 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:28:48.811 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:28:48.833 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:28:48.833 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:48.836 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:53.827 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:28:53.829 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:28:53.939 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:28:53.939 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:54.965 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:28:54.968 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:54.968 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:28:54.998 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:28:54.999 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:28:54.999 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:28:55.000 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:28:55.000 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:55.002 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:55.004 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:28:55.005 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:28:55.005 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:55.011 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:28:55.011 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:28:55.012 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:55.021 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:28:55.021 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:28:55.023 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:28:55.023 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:28:55.023 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:55.025 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:32:48.038 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:32:48.044 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:32:48.046 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:32:48.121 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:33:43.793 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:33:43.794 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:33:43.796 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@11d2e8f2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:33:55.266 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Retrograde clock change detected (housekeeper delta=29s825ms), soft-evicting connections from pool.
2025-07-18 16:36:02.342 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 16:36:02.348 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-18 16:37:39.263 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 16:37:39.277 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 53242 (/Users/<USER>/AI/vscode/sleephome/target/classes started by songdaijian in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 16:37:39.277 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 16:37:39.277 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 16:37:39.733 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 16:37:39.737 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 16:37:39.738 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 16:37:39.738 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 16:37:39.770 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 470 ms
2025-07-18 16:37:39.770 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 16:37:40.053 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 16:37:40.137 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@48cf8414, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@27dbaa33, org.springframework.security.web.context.SecurityContextHolderFilter@6f3b13d0, org.springframework.security.web.header.HeaderWriterFilter@701d2b59, org.springframework.security.web.authentication.logout.LogoutFilter@455f4483, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@1377b1a0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6e9a10cd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@59b447a4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15d65fcf, org.springframework.security.web.session.SessionManagementFilter@65d73bd, org.springframework.security.web.access.ExceptionTranslationFilter@d62472f, org.springframework.security.web.access.intercept.AuthorizationFilter@7e0986c9]
2025-07-18 16:37:40.227 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8888"]
2025-07-18 16:37:40.237 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8888 (http) with context path ''
2025-07-18 16:37:40.240 [main] INFO  com.sleephome.SleepManagementSystemApplication - Started SleepManagementSystemApplication in 1.152 seconds (process running for 1.275)
2025-07-18 16:39:47.617 [http-nio-8888-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 16:39:47.617 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 16:39:47.619 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-18 16:39:47.622 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:39:47.625 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:39:47.625 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id C43497A3436311CECC494A0960920FAA
2025-07-18 16:39:47.626 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:39:47.729 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:39:47.729 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:39:47.730 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:39:47.730 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:39:47.730 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id C43497A3436311CECC494A0960920FAA
2025-07-18 16:39:47.730 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id C43497A3436311CECC494A0960920FAA
2025-07-18 16:39:47.739 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:39:47.739 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:39:47.739 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:39:47.739 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id C43497A3436311CECC494A0960920FAA
2025-07-18 16:39:47.740 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:39:47.761 [http-nio-8888-exec-4] DEBUG o.s.s.web.savedrequest.HttpSessionRequestCache - Saved request http://localhost:8888/.well-known/appspecific/com.chrome.devtools.json?continue to session
2025-07-18 16:39:47.762 [http-nio-8888-exec-4] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8888/doctor/login
2025-07-18 16:39:47.764 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:39:47.764 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:39:47.764 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:39:53.050 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:39:53.053 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:39:53.054 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:39:53.187 [http-nio-8888-exec-9] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 16:39:53.318 [http-nio-8888-exec-9] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6fdddc0
2025-07-18 16:39:53.319 [http-nio-8888-exec-9] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 16:39:53.423 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:39:54.452 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:39:54.453 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:39:54.453 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:39:54.517 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:39:54.517 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:39:54.518 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:39:54.519 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:39:54.520 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:39:54.521 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:39:54.521 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:39:54.521 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:39:54.523 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:39:54.528 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:39:54.537 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:39:54.537 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 16:39:54.578 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:39:54.579 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:39:54.579 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:00.171 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 16:40:00.172 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:00.172 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 16:40:00.196 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:40:00.196 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 16:40:00.196 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:40:00.198 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:40:00.198 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 16:40:00.198 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:40:00.199 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:00.199 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:00.201 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:00.217 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 16:40:00.217 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:40:00.217 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:40:00.222 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:00.228 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:00.229 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 16:40:00.234 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:40:00.235 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 16:40:00.235 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:40:00.236 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:40:00.237 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:40:00.237 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:00.237 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error?page=1&size=10
2025-07-18 16:40:00.239 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:00.239 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:05.049 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:40:05.050 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:40:05.120 [http-nio-8888-exec-6] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:40:05.120 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@177519e2, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:06.135 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:40:06.135 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:06.136 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:40:06.179 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:40:06.179 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:40:06.182 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:40:06.183 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:40:06.191 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:06.194 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:06.209 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:40:06.211 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:40:06.211 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:08.077 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 16:40:08.078 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:08.079 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 16:40:08.100 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:40:08.100 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 16:40:08.101 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:40:08.102 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 16:40:08.102 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:40:08.102 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:40:08.103 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:08.103 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:08.103 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:08.116 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 16:40:08.116 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:40:08.116 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:40:08.119 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:08.119 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 16:40:08.120 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:08.124 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 16:40:08.126 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:40:08.126 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:40:08.126 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:08.126 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error?page=1&size=10
2025-07-18 16:40:08.128 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:40:08.128 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:40:08.129 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:08.129 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:18.410 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:40:18.414 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:40:18.508 [http-nio-8888-exec-1] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:40:18.508 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5bb6822b, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:40:19.523 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:40:19.524 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:19.524 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:40:19.542 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:40:19.544 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:40:19.544 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:40:19.545 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:40:19.546 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:19.548 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:19.557 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:40:19.557 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:40:19.557 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:23.666 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 16:40:23.668 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:23.668 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 16:40:23.694 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:40:23.694 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 16:40:23.694 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:40:23.696 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:40:23.696 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 16:40:23.696 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:40:23.697 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:23.697 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:23.697 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:23.709 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:40:23.709 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 16:40:23.709 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:40:23.727 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:23.727 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:23.728 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 16:40:23.729 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 16:40:23.731 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:23.731 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error?page=1&size=10
2025-07-18 16:40:23.734 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:40:23.734 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:40:23.735 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:40:23.735 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:40:23.735 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:23.735 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:25.508 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:40:25.510 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:25.511 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:40:25.529 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:40:25.530 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:40:25.531 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:40:25.535 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:40:25.535 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:40:25.536 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:05.770 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /patients
2025-07-18 16:41:05.775 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:05.776 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /patients
2025-07-18 16:41:05.779 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:41:05.781 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:05.781 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 16:41:07.132 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:41:07.133 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:07.133 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:41:07.152 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:41:07.153 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:41:07.156 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:07.165 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:41:07.166 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:41:07.167 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:07.994 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /questionnaires
2025-07-18 16:41:07.997 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:07.997 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /questionnaires
2025-07-18 16:41:07.999 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:41:08.001 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:08.001 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 16:41:09.041 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:41:09.042 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:09.043 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:41:09.059 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:41:09.059 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:41:09.060 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:09.068 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:41:09.068 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:41:09.068 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:09.916 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /reports
2025-07-18 16:41:09.918 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:09.918 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /reports
2025-07-18 16:41:09.919 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:41:09.921 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:09.921 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 16:41:11.130 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:41:11.131 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:11.132 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:41:11.148 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:41:11.149 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:41:11.150 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:11.157 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:41:11.157 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:41:11.158 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:12.383 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /messages
2025-07-18 16:41:12.388 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:12.388 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /messages
2025-07-18 16:41:12.391 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:41:12.393 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:12.393 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 16:41:13.640 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:41:13.641 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:13.641 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:41:13.657 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:41:13.658 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:41:13.659 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:13.663 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:41:13.663 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:41:13.663 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:27.765 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 16:41:27.766 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:27.767 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 16:41:27.792 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:41:27.792 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:41:27.792 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 16:41:27.795 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:41:27.795 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 16:41:27.796 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:41:27.797 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:27.797 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:27.798 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:27.813 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:41:27.813 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 16:41:27.813 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:41:27.816 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:27.817 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:27.817 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 16:41:27.819 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 16:41:27.821 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:27.821 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error?page=1&size=10
2025-07-18 16:41:27.823 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:41:27.823 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:41:27.824 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:41:27.824 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:27.824 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:41:27.825 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:29.648 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:41:29.649 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:29.649 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:41:29.667 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:41:29.669 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:41:29.671 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:29.678 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:41:29.678 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:41:29.679 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:32.537 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 16:41:32.538 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:32.538 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 16:41:32.558 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:41:32.558 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:41:32.558 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 16:41:32.560 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:41:32.559 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 16:41:32.560 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:41:32.562 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:32.562 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:32.563 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:32.573 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:41:32.573 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 16:41:32.573 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:41:32.576 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:32.576 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:32.577 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 16:41:32.578 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 16:41:32.580 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:32.580 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error?page=1&size=10
2025-07-18 16:41:32.583 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:41:32.583 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:41:32.583 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:41:32.583 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:41:32.584 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:41:32.584 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:46:07.570 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:46:07.571 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:46:07.579 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:46:07.588 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:46:07.588 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:46:07.589 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:46:07.589 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:46:07.592 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:46:07.592 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:46:32.411 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:46:32.412 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:46:32.545 [http-nio-8888-exec-5] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:46:32.546 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@64f312a3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:log, system:user:query, system:log:query]]]
2025-07-18 16:46:33.559 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:46:33.559 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@1af722b7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:46:33.560 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:46:33.578 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:46:33.578 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:46:33.580 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:46:33.580 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:46:33.581 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@1af722b7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:46:33.581 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@1af722b7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:46:33.595 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:46:33.595 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:46:33.595 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@1af722b7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:46:40.275 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /logout
2025-07-18 16:46:40.277 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@1af722b7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:46:40.277 [http-nio-8888-exec-2] DEBUG o.s.s.web.authentication.logout.LogoutFilter - Logging out [UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@1af722b7, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:46:40.278 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.logout.SecurityContextLogoutHandler - Invalidated session 94ADE31F767DA7BB934A666BB88C4303
2025-07-18 16:46:40.281 [http-nio-8888-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to /
2025-07-18 16:46:40.287 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 16:46:40.287 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:46:40.287 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 94ADE31F767DA7BB934A666BB88C4303
2025-07-18 16:46:40.287 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 16:46:40.305 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:46:40.305 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:46:40.305 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 94ADE31F767DA7BB934A666BB88C4303
2025-07-18 16:46:40.305 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:46:41.328 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 16:46:41.328 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:46:41.328 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 94ADE31F767DA7BB934A666BB88C4303
2025-07-18 16:46:41.328 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 16:46:41.333 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:46:41.333 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:46:41.333 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 94ADE31F767DA7BB934A666BB88C4303
2025-07-18 16:46:41.333 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:46:41.341 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:46:41.341 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:46:41.342 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:46:41.342 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 94ADE31F767DA7BB934A666BB88C4303
2025-07-18 16:46:41.342 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:46:41.342 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 94ADE31F767DA7BB934A666BB88C4303
2025-07-18 16:46:41.343 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:46:41.343 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:50:04.695 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:50:04.696 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 16:50:04.698 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 94ADE31F767DA7BB934A666BB88C4303
2025-07-18 16:50:04.703 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:50:04.778 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:50:05.793 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:50:05.793 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:05.793 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:50:05.809 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:50:05.809 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:50:05.809 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:50:05.810 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:05.810 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:50:05.813 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:05.836 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:50:05.836 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:50:05.837 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:09.475 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 16:50:09.476 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:09.476 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 16:50:09.496 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:50:09.496 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:50:09.496 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 16:50:09.500 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 16:50:09.500 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:50:09.500 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:50:09.501 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:09.501 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:09.501 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:09.514 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:50:09.515 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:50:09.516 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 16:50:09.518 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:09.518 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:09.518 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 16:50:09.519 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 16:50:09.521 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:09.521 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error?page=1&size=10
2025-07-18 16:50:09.524 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:50:09.525 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:50:09.525 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:50:09.531 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:09.531 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:50:09.532 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:15.364 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:50:15.365 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:50:15.479 [http-nio-8888-exec-8] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:50:15.482 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@77302fa3, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:16.503 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:50:16.503 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@4bacb568, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:16.503 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:50:16.521 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:50:16.521 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:50:16.522 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:50:16.523 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:50:16.524 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@4bacb568, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:16.525 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@4bacb568, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:50:16.533 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:50:16.533 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:50:16.534 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@4bacb568, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
