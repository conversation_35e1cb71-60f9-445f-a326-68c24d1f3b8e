2025-07-18 12:23:09.318 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 12:23:09.332 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 652 (/Users/<USER>/AI/vscode/sleephome/target/classes started by song<PERSON><PERSON>an in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 12:23:09.332 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 12:23:09.332 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 12:23:09.834 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 12:23:09.838 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 12:23:09.839 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 12:23:09.839 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 12:23:09.871 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 12:23:09.871 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 521 ms
2025-07-18 12:23:10.010 [main] DEBUG com.sleephome.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-18 12:23:10.160 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 12:23:10.180 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Unsatisfied dependency expressed through method 'setFilterChains' parameter 0: Error creating bean with name 'filterChain' defined in class path resource [com/sleephome/config/SecurityConfig.class]: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
2025-07-18 12:23:10.181 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 12:23:10.185 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-18 12:23:10.190 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Unsatisfied dependency expressed through method 'setFilterChains' parameter 0: Error creating bean with name 'filterChain' defined in class path resource [com/sleephome/config/SecurityConfig.class]: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.resolveMethodArguments(AutowiredAnnotationBeanPostProcessor.java:875)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.inject(AutowiredAnnotationBeanPostProcessor.java:828)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:492)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:950)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.sleephome.SleepManagementSystemApplication.main(SleepManagementSystemApplication.java:12)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'filterChain' defined in class path resource [com/sleephome/config/SecurityConfig.class]: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:654)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:642)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1162)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:560)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1633)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1597)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1488)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1337)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.resolveMethodArguments(AutowiredAnnotationBeanPostProcessor.java:867)
	... 20 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:650)
	... 36 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1770)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:598)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1172)
	at org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry.createMvcMatchers(AbstractRequestMatcherRegistry.java:112)
	at org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry.requestMatchers(AbstractRequestMatcherRegistry.java:210)
	at org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry.requestMatchers(AbstractRequestMatcherRegistry.java:276)
	at com.sleephome.config.SecurityConfig.lambda$filterChain$4(SecurityConfig.java:65)
	at org.springframework.security.config.annotation.web.builders.HttpSecurity.authorizeHttpRequests(HttpSecurity.java:1466)
	at com.sleephome.config.SecurityConfig.filterChain(SecurityConfig.java:64)
	at com.sleephome.config.SecurityConfig$$SpringCGLIB$$0.CGLIB$filterChain$2(<generated>)
	at com.sleephome.config.SecurityConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.sleephome.config.SecurityConfig$$SpringCGLIB$$0.filterChain(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:139)
	... 37 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1770)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:598)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:663)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1320)
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:368)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.initHandlerMappings(HandlerMappingIntrospector.java:110)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.afterPropertiesSet(HandlerMappingIntrospector.java:98)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1817)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1766)
	... 58 common frames omitted
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:667)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:633)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:331)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:441)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:76)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:298)
	at java.base/java.util.LinkedHashMap.forEach(LinkedHashMap.java:987)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:296)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:224)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:212)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:225)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1817)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1766)
	... 71 common frames omitted
2025-07-18 12:24:05.334 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 12:24:05.348 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 951 (/Users/<USER>/AI/vscode/sleephome/target/classes started by songdaijian in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 12:24:05.348 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 12:24:05.348 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 12:24:05.805 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 12:24:05.808 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:05.809 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 12:24:05.809 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 12:24:05.842 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 12:24:05.842 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 475 ms
2025-07-18 12:24:05.976 [main] DEBUG com.sleephome.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-18 12:24:06.130 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 12:24:06.214 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7d284f15, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@44bbb7c6, org.springframework.security.web.context.SecurityContextHolderFilter@c262f2f, org.springframework.security.web.header.HeaderWriterFilter@3b57f915, org.springframework.security.web.authentication.logout.LogoutFilter@4276ad40, com.sleephome.security.jwt.AuthTokenFilter@51ac12ac, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@11148dc2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@60a01cb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2342f1ff, org.springframework.security.web.session.SessionManagementFilter@45d389f2, org.springframework.security.web.access.ExceptionTranslationFilter@64b73e0a, org.springframework.security.web.access.intercept.AuthorizationFilter@2a19a0fe]
2025-07-18 12:24:06.301 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:06.308 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-18 12:24:06.310 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:06.310 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 12:24:06.314 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:06.314 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:06.317 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-18 12:24:06.322 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8888 was already in use.

Action:

Identify and stop the process that's listening on port 8888 or configure this application to listen on another port.

2025-07-18 12:24:48.851 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 12:24:48.867 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 1199 (/Users/<USER>/AI/vscode/sleephome/target/classes started by songdaijian in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 12:24:48.867 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 12:24:48.868 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 12:24:49.343 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 12:24:49.346 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:49.347 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 12:24:49.347 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 12:24:49.380 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 12:24:49.380 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 494 ms
2025-07-18 12:24:49.523 [main] DEBUG com.sleephome.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-18 12:24:49.679 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 12:24:49.760 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7d284f15, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@44bbb7c6, org.springframework.security.web.context.SecurityContextHolderFilter@c262f2f, org.springframework.security.web.header.HeaderWriterFilter@3b57f915, org.springframework.security.web.authentication.logout.LogoutFilter@4276ad40, com.sleephome.security.jwt.AuthTokenFilter@51ac12ac, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@11148dc2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@60a01cb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2342f1ff, org.springframework.security.web.session.SessionManagementFilter@45d389f2, org.springframework.security.web.access.ExceptionTranslationFilter@64b73e0a, org.springframework.security.web.access.intercept.AuthorizationFilter@2a19a0fe]
2025-07-18 12:24:49.850 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:49.860 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8888 (http) with context path ''
2025-07-18 12:24:49.863 [main] INFO  com.sleephome.SleepManagementSystemApplication - Started SleepManagementSystemApplication in 1.221 seconds (process running for 1.348)
2025-07-18 12:24:53.815 [http-nio-8888-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 12:24:53.815 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 12:24:53.816 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 12:24:53.819 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community
2025-07-18 12:24:53.821 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:53.821 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:53.828 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community
2025-07-18 12:24:53.834 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 12:24:53.836 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:53.836 [http-nio-8888-exec-1] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:24:56.938 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 12:24:56.939 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:56.939 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:56.939 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 12:24:57.030 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:24:57.031 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:57.031 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:57.032 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:24:59.583 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:24:59.583 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:59.583 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:59.583 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:24:59.598 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:24:59.598 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:24:59.598 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:59.598 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:59.598 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:59.598 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:59.599 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:24:59.599 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:24:59.614 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:24:59.614 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:59.614 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:59.616 [http-nio-8888-exec-9] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:02.531 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:25:02.532 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:02.532 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:02.533 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:25:02.553 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:02.553 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:02.554 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:02.554 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:02.554 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:02.554 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:02.555 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:02.556 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:02.565 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:02.566 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:02.566 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:02.568 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:08.461 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community
2025-07-18 12:25:08.463 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:08.464 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:08.466 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community
2025-07-18 12:25:08.471 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 12:25:08.473 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:08.474 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:10.607 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 12:25:10.607 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:10.607 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:10.608 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 12:25:10.623 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:10.624 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:10.624 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:10.625 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:11.805 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:25:11.805 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:11.806 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:11.806 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:25:11.825 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:11.825 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:11.825 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:11.826 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:11.826 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:11.826 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:11.827 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:11.827 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:11.842 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:11.842 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:11.842 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:11.844 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:16.624 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:25:16.625 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:16.625 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:16.626 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:25:16.774 [http-nio-8888-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 12:25:16.931 [http-nio-8888-exec-5] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@bcab44c
2025-07-18 12:25:16.932 [http-nio-8888-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 12:25:17.132 [http-nio-8888-exec-5] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:25:18.188 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/dashboard
2025-07-18 12:25:18.188 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:18.188 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:18.189 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/dashboard
2025-07-18 12:25:18.223 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:18.223 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:18.224 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:18.224 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:18.224 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:18.224 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:18.225 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:18.225 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:30.691 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:25:30.693 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:30.693 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:30.694 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:25:30.716 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:30.716 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:30.716 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:30.717 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:30.717 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:30.717 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:30.719 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:30.719 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:30.733 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:30.734 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:30.734 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:30.735 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:35.850 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:25:35.851 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:35.851 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:35.851 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:25:35.938 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:25:35.942 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:35.942 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:35.943 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:25:35.972 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:35.972 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:35.972 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:35.972 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:35.972 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:35.972 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:35.973 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:35.974 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:36.007 [http-nio-8888-exec-7] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:25:36.026 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:36.026 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:36.026 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:36.029 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:37.355 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/dashboard
2025-07-18 12:25:37.355 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:37.356 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:37.357 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/dashboard
2025-07-18 12:25:37.374 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:37.374 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:37.375 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:37.375 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:37.375 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:37.375 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:37.377 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:37.377 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:40.042 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:25:40.043 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:40.043 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:40.043 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:25:40.145 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:25:41.161 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/dashboard
2025-07-18 12:25:41.161 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:41.162 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:41.162 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/dashboard
2025-07-18 12:25:41.185 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:41.185 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:41.185 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:41.185 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:41.185 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:41.185 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:41.202 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:41.203 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:46.504 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:25:46.505 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:46.505 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:46.506 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:25:46.519 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:46.519 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:46.519 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:46.519 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:46.519 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:46.519 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:46.520 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:46.520 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:46.529 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:46.529 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:46.530 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:46.531 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:53.737 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:25:53.740 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:53.744 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:53.747 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:25:53.768 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:53.768 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:53.768 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:53.768 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:53.768 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:53.768 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:53.769 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:53.770 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:53.778 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:53.778 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:53.778 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:53.779 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:26:00.199 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:26:00.200 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:00.200 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:00.200 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:26:00.306 [http-nio-8888-exec-5] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:26:01.319 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:26:01.319 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.319 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.320 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:26:01.349 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:26:01.349 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:26:01.350 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.350 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.350 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.350 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.351 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:26:01.351 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:26:01.373 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:26:01.373 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.373 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.374 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:26:01.382 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:26:01.382 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:26:01.383 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.383 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.383 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.383 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.383 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:26:01.383 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:26:01.392 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:26:01.392 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.392 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.393 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:26:10.107 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:26:10.107 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:10.108 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:10.108 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:26:10.202 [http-nio-8888-exec-5] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:26:11.224 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:26:11.224 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.225 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.225 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:26:11.247 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:26:11.247 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:26:11.247 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.247 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.247 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.247 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.248 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:26:11.249 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:26:11.264 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:26:11.264 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.264 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.265 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:26:11.273 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:26:11.273 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:26:11.273 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.273 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.273 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.273 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.273 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:26:11.274 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:26:11.284 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:26:11.284 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.284 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.284 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:26:13.799 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:26:13.800 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:13.800 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:13.808 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:10.064 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:28:10.065 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:10.067 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:10.073 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:28:10.100 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:28:10.100 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:28:10.100 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:10.100 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:10.100 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:10.100 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:10.102 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:28:10.102 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:28:10.116 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:28:10.116 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:10.116 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:10.120 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:14.842 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:28:14.843 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:14.843 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:14.843 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:28:14.937 [http-nio-8888-exec-8] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:28:15.953 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:28:15.953 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:15.953 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:15.954 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:28:15.979 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:28:15.979 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:28:15.980 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:15.980 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:15.980 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:15.980 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:15.980 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:28:15.981 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:28:15.997 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:28:15.998 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:15.998 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:15.998 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:28:16.007 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:28:16.007 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:28:16.007 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:16.007 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:16.007 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:16.007 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:16.007 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:28:16.007 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:28:16.016 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:28:16.017 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:16.017 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:16.018 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:23.810 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:28:23.810 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:23.810 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:23.815 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:27.531 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:28:27.531 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:27.531 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:27.534 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:35.661 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:28:35.662 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:35.663 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:35.665 [http-nio-8888-exec-9] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:58.444 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-18 12:28:58.445 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:58.445 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:58.448 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /favicon.ico
2025-07-18 12:29:08.569 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:29:08.570 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:29:08.570 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:29:08.573 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:29:08.608 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:29:08.608 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:29:08.608 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:29:08.608 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:29:08.608 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:29:08.608 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:29:08.610 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:29:08.610 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:29:08.610 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:29:08.610 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:29:08.610 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:29:08.612 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:29:08.674 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:29:08.674 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:29:08.674 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:29:08.675 [http-nio-8888-exec-9] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:31:11.244 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:31:11.247 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:31:11.248 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:31:11.250 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:31:11.273 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:31:11.273 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:31:11.273 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:31:11.273 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:31:11.273 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:31:11.273 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:31:11.274 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:31:11.275 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:31:11.284 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:31:11.284 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:31:11.284 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:31:11.285 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:31:29.832 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-18 12:31:29.833 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:31:29.834 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:31:29.836 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /favicon.ico
2025-07-18 12:33:43.862 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:33:43.865 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:43.865 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:43.867 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:33:43.929 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:33:43.929 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:33:43.929 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:43.929 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:43.930 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:43.930 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:43.932 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:33:43.932 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:33:43.943 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:33:43.944 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:43.944 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:43.945 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:33:48.399 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:33:48.399 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:48.399 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:48.401 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:33:51.262 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:33:51.263 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:51.263 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:51.263 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:33:51.385 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:33:52.404 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:33:52.404 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.404 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.405 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:33:52.424 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:33:52.424 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.424 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.425 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:33:52.425 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:33:52.425 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.425 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.425 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.425 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.426 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:33:52.426 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:33:52.427 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:33:52.488 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:33:52.488 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.488 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.491 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:33:52.505 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:33:52.506 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.506 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.506 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:33:52.506 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.506 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.506 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:33:52.507 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:33:52.507 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:33:52.509 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.509 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.509 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:33:52.559 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:33:52.560 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.560 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.561 [http-nio-8888-exec-9] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:40:26.286 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:40:26.287 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:26.287 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:40:26.362 [http-nio-8888-exec-8] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:40:36.174 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:40:36.174 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:36.175 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:36.178 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:40:36.208 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:40:36.208 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:40:36.208 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:36.208 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:36.208 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:36.208 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:36.209 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:40:36.210 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:40:50.768 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:40:50.768 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:50.769 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:50.775 [http-nio-8888-exec-1] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:40:54.959 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:40:54.960 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:54.961 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:54.962 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:40:55.072 [http-nio-8888-exec-4] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:40:56.097 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:40:56.097 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.097 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.097 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:40:56.117 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:40:56.118 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.118 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.124 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:40:56.125 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.125 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.124 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:40:56.125 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.125 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.124 [http-nio-8888-exec-7] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:40:56.126 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:40:56.126 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:40:56.184 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:40:56.184 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.184 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.184 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:40:56.196 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:40:56.196 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.196 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.197 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:40:56.197 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:40:56.197 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.197 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.197 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.197 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.198 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:40:56.198 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:40:56.198 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:41:17.054 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:41:17.061 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:17.061 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:17.062 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:41:17.089 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:41:17.089 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:17.089 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:17.091 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:41:17.097 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:41:17.097 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:17.097 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:17.099 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:41:47.753 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:41:47.755 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:47.755 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:47.756 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:41:47.842 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:41:48.865 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:41:48.865 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.865 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.865 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:41:48.890 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:41:48.890 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:41:48.891 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.891 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.891 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.891 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.894 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:41:48.894 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:41:48.915 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:41:48.915 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.915 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.915 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:41:48.929 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:41:48.929 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:41:48.930 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.930 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.930 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.930 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.931 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:41:48.931 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:43:09.309 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:43:09.310 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:09.310 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:09.312 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:43:09.328 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:43:09.328 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:43:09.328 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:09.328 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:09.328 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:09.328 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:09.328 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:43:09.329 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:43:12.490 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:43:12.491 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:12.491 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:12.495 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:43:14.426 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:43:14.427 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:14.427 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:14.427 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:43:14.439 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:43:14.439 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:43:14.439 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:14.439 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:14.439 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:14.439 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:14.440 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:43:14.440 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:14.440 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:14.440 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:43:14.441 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:43:14.441 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:44:22.098 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:44:22.100 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:44:22.100 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:44:22.105 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:44:22.132 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:44:22.132 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:44:22.132 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:44:22.134 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:44:22.145 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:44:22.145 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:44:22.145 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:44:22.146 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:44:34.118 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:44:34.119 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:44:34.119 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:44:34.197 [http-nio-8888-exec-3] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:45:54.245 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:45:54.257 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:54.258 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:54.260 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:45:54.300 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:45:54.300 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:45:54.301 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:54.301 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:54.301 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:54.301 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:54.303 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:45:54.304 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:45:58.828 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:45:58.829 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:58.829 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:58.829 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:45:58.945 [http-nio-8888-exec-8] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:45:59.962 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:45:59.962 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:59.962 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:59.963 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:45:59.986 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:45:59.986 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:45:59.987 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:59.987 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:59.987 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:59.987 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:59.995 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:45:59.996 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:46:00.014 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:46:00.014 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:46:00.015 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:46:00.017 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:46:00.028 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:46:00.028 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:46:00.029 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:46:00.029 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:46:00.029 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:46:00.029 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:46:00.029 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:46:00.029 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:50:06.888 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 12:50:06.890 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:50:06.891 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:50:06.893 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:52:32.372 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:52:32.376 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:32.377 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:32.381 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:52:32.446 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:52:32.446 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:52:32.448 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:32.449 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:32.449 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:32.449 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:32.451 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:52:32.451 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:52:32.457 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:52:32.457 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:32.457 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:32.459 [http-nio-8888-exec-1] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:52:35.984 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:52:35.985 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:35.985 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:35.985 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:52:36.128 [http-nio-8888-exec-4] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:52:37.150 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:52:37.150 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.150 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.150 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:52:37.167 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:52:37.168 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.168 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.169 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:52:37.171 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:52:37.172 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.172 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.172 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:52:37.172 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.172 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.172 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:52:37.173 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:52:37.230 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:52:37.230 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.230 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.230 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:52:37.239 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:52:37.240 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.240 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.240 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:52:37.240 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:52:37.240 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.241 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.240 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.241 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.241 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:52:37.241 [http-nio-8888-exec-7] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:52:37.241 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:03:12.504 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:03:12.505 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:03:12.505 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:03:12.506 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:03:12.546 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:03:12.547 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:03:12.547 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:03:12.548 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:03:12.549 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:03:12.549 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:03:12.550 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:03:12.550 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:06:19.490 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:06:19.492 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:19.493 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:19.494 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:06:19.557 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:06:19.557 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:06:19.557 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:19.557 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:19.557 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:19.557 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:19.557 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:06:19.558 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:06:24.664 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:06:24.664 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:24.664 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:24.664 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:06:24.815 [http-nio-8888-exec-10] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:06:25.831 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:06:25.831 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:25.831 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:25.832 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:37.028 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 13:06:37.029 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:37.029 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:37.030 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:39.927 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:06:39.927 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:39.927 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:39.929 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:39.948 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 13:06:39.949 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:39.949 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:39.950 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:45.473 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:06:45.474 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:45.474 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:45.476 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:50.269 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:06:50.270 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:50.270 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:50.270 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:06:50.290 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:06:50.291 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:50.291 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:50.292 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:06:50.292 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:50.293 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:50.294 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 13:06:50.294 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:50.294 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:50.298 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:06:50.299 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:06:50.299 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:53.836 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:06:53.837 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:53.837 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:53.837 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:06:53.944 [http-nio-8888-exec-10] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:06:54.957 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:06:54.957 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:54.957 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:54.958 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:54.973 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 13:06:54.974 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:54.974 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:54.975 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:11:42.219 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:11:42.221 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:11:42.221 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:11:42.222 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:11:42.297 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:11:42.297 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:11:42.298 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:11:42.319 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:11:42.328 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:11:42.350 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:11:42.350 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:11:42.354 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:12:03.505 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:12:03.506 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:12:03.509 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:12:03.509 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:12:03.615 [http-nio-8888-exec-2] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:12:04.632 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:12:04.633 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:12:04.633 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:12:04.634 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:13:25.307 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:13:25.307 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:13:25.308 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:13:25.386 [http-nio-8888-exec-3] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:15:22.195 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:15:22.196 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:15:22.196 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:17:37.004 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:17:37.005 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:17:37.006 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:18:53.623 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 13:18:53.626 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
