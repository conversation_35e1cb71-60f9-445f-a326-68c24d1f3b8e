2025-07-18 16:28:03.697 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:28:03.699 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:28:03.782 [http-nio-8888-exec-4] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:28:03.782 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@8518360, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.800 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:28:04.800 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.801 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:28:04.833 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:28:04.833 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:28:04.835 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:28:04.835 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:28:04.838 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.840 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.840 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:28:04.843 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.843 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:28:04.849 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:28:04.850 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.850 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 16:28:04.900 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:28:04.900 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:28:04.900 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.915 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:28:04.916 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:28:04.918 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.929 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:28:04.929 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:28:04.929 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:28:04.934 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:28:04.933 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:28:04.935 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.936 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.936 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.936 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 16:28:04.941 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 16:28:04.942 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:04.943 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 16:28:48.630 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:28:48.643 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:28:48.744 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:48.807 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:28:48.809 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:28:48.811 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:28:48.833 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:28:48.833 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:48.836 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:53.827 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 16:28:53.829 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 16:28:53.939 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 16:28:53.939 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@cae7ab9, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:54.965 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 16:28:54.968 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:54.968 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 16:28:54.998 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:28:54.999 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:28:54.999 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:28:55.000 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:28:55.000 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:55.002 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:55.004 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 16:28:55.005 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 16:28:55.005 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:55.011 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 16:28:55.011 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 16:28:55.012 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:55.021 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 16:28:55.021 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 16:28:55.023 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 16:28:55.023 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 16:28:55.023 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 16:28:55.025 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@5c705275, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
