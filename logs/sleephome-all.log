2025-07-18 23:39:04.634 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:39:04.639 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:04.639 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:39:04.656 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:04.657 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:06.460 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:39:06.461 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:06.462 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:39:06.462 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:06.462 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:12.695 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:39:12.699 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:12.699 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:39:12.723 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:39:12.723 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:39:12.723 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:39:12.723 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:39:12.725 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:39:12.725 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:39:12.725 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:39:12.725 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:39:12.729 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:12.729 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:12.729 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:12.732 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:12.732 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:39:12.733 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:39:12.733 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:39:12.733 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:39:12.736 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:12.738 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:12.750 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:39:12.751 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:39:12.752 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:12.773 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:39:12.774 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:12.774 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:39:12.775 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:39:12.775 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:39:14.045 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:39:14.047 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:14.047 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:39:14.048 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:14.048 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:52.075 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/3
2025-07-18 23:39:52.087 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:52.087 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/3
2025-07-18 23:39:52.089 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:52.090 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:54.523 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&description=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98%E8%A7%92%E8%89%B2
2025-07-18 23:39:54.524 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:54.524 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=3&roleName=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98&roleKey=ROLE_COMMUNITY&description=%E7%A4%BE%E5%8C%BA%E7%AE%A1%E7%90%86%E5%91%98%E8%A7%92%E8%89%B2
2025-07-18 23:39:54.537 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:39:54.537 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:39:54.537 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:39:54.537 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:39:54.539 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:39:54.539 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:39:54.539 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:39:54.540 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:39:54.545 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:54.545 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:54.545 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:54.546 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:54.550 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:39:54.550 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:39:54.551 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:39:54.551 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:39:54.553 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:54.554 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:54.562 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:39:54.562 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:39:54.563 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:54.575 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:39:54.575 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:54.575 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:39:54.576 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:39:54.577 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:39:56.084 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:39:56.084 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:56.084 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:39:56.085 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:56.085 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:57.858 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:39:57.860 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:39:57.860 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:39:57.861 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:39:57.861 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:01.083 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F%EF%BC%88%E5%B7%B2%E4%BF%AE%E6%94%B9%EF%BC%89&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:40:01.085 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:01.085 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F%EF%BC%88%E5%B7%B2%E4%BF%AE%E6%94%B9%EF%BC%89&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:40:01.100 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:40:01.100 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:40:01.100 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:40:01.100 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:40:01.102 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:40:01.102 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:40:01.102 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:40:01.102 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:40:01.104 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:01.104 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:01.106 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:01.104 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:01.107 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:40:01.107 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:40:01.109 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:40:01.109 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:40:01.110 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:01.111 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:01.119 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:40:01.119 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:40:01.120 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:01.129 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:40:01.131 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:01.131 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:40:01.131 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:40:01.132 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:40:34.118 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:40:34.121 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:34.121 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:40:34.127 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:34.129 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:35.626 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:40:35.628 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:40:35.628 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:40:35.639 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:35.639 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:40:35.641 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:35.654 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:40:35.659 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:35.659 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:40:37.457 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/1
2025-07-18 23:40:37.463 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:37.463 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/1
2025-07-18 23:40:37.464 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:37.464 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:42.099 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:40:42.103 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:42.103 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:40:42.105 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:42.106 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:46.622 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing PUT /api/system/role
2025-07-18 23:40:46.622 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:46.622 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured PUT /api/system/role
2025-07-18 23:40:46.624 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.edit(com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:46.625 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.edit(com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:46.638 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:40:46.639 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:46.639 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:40:46.640 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:46.640 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:50.887 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/3
2025-07-18 23:40:50.890 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:50.890 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/3
2025-07-18 23:40:50.890 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:50.891 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:53.653 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing PUT /api/system/role
2025-07-18 23:40:53.653 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:53.653 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured PUT /api/system/role
2025-07-18 23:40:53.654 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.edit(com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:53.654 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.edit(com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:53.674 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:40:53.675 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:40:53.675 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:40:53.675 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:40:53.676 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:02.195 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:02.210 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:02.210 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:02.212 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:02.212 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:03.232 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 23:41:03.233 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.233 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 23:41:03.250 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:41:03.252 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:41:03.252 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:41:03.253 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:41:03.253 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:41:03.254 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:41:03.254 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:41:03.258 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:41:03.258 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:03.258 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.259 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.258 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.266 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:41:03.268 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.267 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:41:03.273 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.273 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:41:03.273 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:03.273 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:41:03.275 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.279 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.285 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:41:03.285 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.286 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:41:03.326 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:41:03.327 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:41:03.329 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.341 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:03.342 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:03.342 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:03.343 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:03.343 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:04.339 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:04.340 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:04.340 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:04.341 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:04.341 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:07.904 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:41:07.906 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:07.907 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:41:07.907 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:07.908 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:11.138 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:41:11.140 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:11.140 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:41:11.158 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:41:11.158 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:41:11.158 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:41:11.158 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:41:11.161 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:41:11.161 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:41:11.161 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:41:11.161 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:41:11.162 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:11.162 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:11.162 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:11.162 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:11.165 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:41:11.165 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:41:11.166 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:41:11.166 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:41:11.167 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:11.167 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:11.172 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:41:11.172 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:41:11.172 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:11.179 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:11.179 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:11.179 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:11.180 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:11.180 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:12.570 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:12.571 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:12.571 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:12.571 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:12.572 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:14.138 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:41:14.139 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:41:14.141 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:14.148 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:14.168 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:14.169 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:14.175 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:41:14.177 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:14.177 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:41:16.936 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:41:16.943 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:16.944 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:41:16.946 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:16.946 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:21.597 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:41:21.598 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.599 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:41:21.614 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:41:21.615 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:41:21.615 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:41:21.615 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:41:21.615 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:21.616 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:41:21.616 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:41:21.616 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:41:21.617 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:41:21.618 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.619 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.620 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.619 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.622 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.623 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:21.623 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:41:21.623 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:41:21.624 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:41:21.624 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:41:21.624 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:41:21.625 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.625 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.626 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.626 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:41:21.677 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:41:21.677 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:41:21.678 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.687 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:21.688 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:21.688 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:21.689 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:21.689 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:23.553 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:23.555 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:23.555 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:23.556 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:23.556 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:26.968 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 23:41:26.969 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:26.969 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 23:41:26.984 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:26.984 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:41:26.984 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:41:26.984 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:41:26.985 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:41:26.986 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:41:26.986 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:41:26.986 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:41:26.986 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:26.986 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:26.987 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:41:26.987 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:26.987 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:26.988 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:26.992 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:26.992 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:41:26.992 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:41:26.994 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:41:26.995 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:26.995 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:41:26.995 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:41:26.995 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:41:26.996 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:26.996 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:27.042 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:41:27.042 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:41:27.043 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:27.055 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:27.057 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:27.057 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:27.058 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:27.058 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:27.749 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:27.749 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:27.749 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:27.750 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:27.751 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:37.671 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:37.677 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:37.678 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:37.682 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:37.683 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:46.529 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:46.533 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:46.535 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:46.537 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:46.537 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:47.873 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:47.874 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:47.874 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:47.874 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:47.875 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:49.472 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:41:49.473 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:49.474 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:41:49.474 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:49.474 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:53.177 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:41:53.178 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.179 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:41:53.199 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:53.201 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:41:53.201 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:41:53.201 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:41:53.201 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:41:53.202 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.202 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:53.202 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:41:53.202 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:41:53.202 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:41:53.203 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:41:53.206 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.206 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.207 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.206 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.207 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:41:53.209 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:41:53.209 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:41:53.209 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.209 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:41:53.210 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:41:53.210 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:41:53.211 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.213 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.267 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:41:53.267 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:41:53.267 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.277 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:53.278 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:53.278 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:53.278 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:53.279 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:54.790 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:54.791 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:54.791 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:54.791 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:54.792 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:56.434 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 23:41:56.434 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.434 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 23:41:56.449 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:56.451 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.451 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:41:56.453 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:41:56.453 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:41:56.453 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:41:56.454 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:41:56.454 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:41:56.454 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.454 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:41:56.454 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:41:56.455 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:41:56.455 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:41:56.455 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:41:56.456 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.456 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.462 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:41:56.462 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:41:56.462 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.461 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.463 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:41:56.463 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:41:56.464 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.464 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.513 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:41:56.514 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:41:56.514 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.525 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:56.525 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:56.525 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:41:56.526 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:56.526 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:41:57.964 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:57.965 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:57.965 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:41:57.966 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:57.966 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:59.648 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:41:59.650 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:41:59.650 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:41:59.650 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:41:59.650 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:03.961 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:42:03.962 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:03.963 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:42:03.990 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:42:03.990 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:03.990 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:03.990 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:03.990 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:42:03.992 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:42:03.992 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:03.992 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:42:03.993 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:03.993 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:03.994 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:03.993 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:03.999 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:42:04.000 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:42:04.000 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:04.000 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:04.001 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:04.001 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:42:04.001 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:42:04.002 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:04.004 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:04.004 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:04.005 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:04.005 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:04.050 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:42:04.051 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:42:04.051 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:04.061 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:04.061 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:04.062 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:04.062 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:04.063 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:07.608 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 23:42:07.610 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.610 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 23:42:07.638 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:07.641 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.641 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:07.642 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:42:07.642 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:07.642 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:07.642 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:42:07.645 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:42:07.645 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:42:07.645 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:07.645 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:07.646 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:07.647 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.647 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.647 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.647 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.652 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:42:07.652 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:42:07.652 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.652 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:07.653 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:42:07.653 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:42:07.654 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.656 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.710 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:42:07.710 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:42:07.711 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.729 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:07.730 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:07.730 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:07.732 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:07.733 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:09.376 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 23:42:09.377 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.377 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 23:42:09.388 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:09.390 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:42:09.391 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:09.401 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:42:09.401 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:09.401 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.401 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:42:09.401 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:09.401 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:42:09.402 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:09.402 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:09.402 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:09.402 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.402 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.402 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.402 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.406 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.406 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:09.406 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:42:09.406 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:42:09.407 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:42:09.407 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:42:09.407 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.409 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.456 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:42:09.457 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:42:09.458 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.466 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:09.467 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:09.467 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:09.467 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:09.468 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:10.504 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:42:10.505 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:10.505 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:42:10.506 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:10.506 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:12.319 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:42:12.322 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:12.322 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:42:12.322 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:12.322 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:16.663 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:42:16.664 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.664 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:42:16.676 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:16.680 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.680 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:16.680 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:42:16.681 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:16.681 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:16.683 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:42:16.684 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:42:16.684 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:16.685 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:42:16.685 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:16.685 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:16.685 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.685 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.685 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.689 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.689 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.689 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:16.690 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:42:16.690 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:42:16.691 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:42:16.692 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:42:16.693 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.694 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.743 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:42:16.744 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:42:16.745 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.753 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:16.753 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:16.753 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:16.754 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:16.754 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:18.913 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:42:18.914 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:18.914 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:42:18.916 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:18.916 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:23.654 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:42:23.656 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:23.656 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:42:23.657 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:23.657 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:29.020 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:42:29.021 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.021 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:42:29.042 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:29.043 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:29.043 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:42:29.043 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:29.043 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:42:29.045 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:42:29.045 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:29.046 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:29.047 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:42:29.049 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.049 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:29.052 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.054 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.054 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.054 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.057 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:42:29.057 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:42:29.059 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:29.059 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:42:29.059 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:42:29.060 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.060 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:29.060 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.063 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.108 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:42:29.109 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:42:29.110 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.122 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:29.123 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:29.123 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:29.124 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:29.124 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:30.682 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 23:42:30.683 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.683 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 23:42:30.698 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:30.698 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:42:30.698 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:30.699 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:30.699 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:42:30.699 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:42:30.700 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:30.700 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:30.700 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:42:30.700 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.700 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:30.700 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.701 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.701 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.701 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.704 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:30.705 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:42:30.706 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:42:30.717 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.717 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:42:30.717 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:30.717 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:42:30.718 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.720 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.753 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:42:30.754 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:42:30.754 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.763 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:30.763 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:30.763 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:30.763 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:30.763 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:32.406 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 23:42:32.407 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.407 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 23:42:32.421 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:32.423 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:32.423 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:42:32.424 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:42:32.424 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.424 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:32.423 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:32.424 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:42:32.424 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:32.425 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.426 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:32.427 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:32.428 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.428 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:42:32.429 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.430 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:32.430 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.430 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.431 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:42:32.431 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:42:32.432 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:42:32.432 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:42:32.434 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.434 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.478 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:42:32.478 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:42:32.479 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.486 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:32.487 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:32.487 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:32.487 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:32.487 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:35.742 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 23:42:35.742 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 23:42:35.757 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:35.761 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 23:42:35.761 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 23:42:35.763 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:35.774 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:35.774 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:35.774 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:35.775 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:35.775 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:35.776 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:35.776 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:35.776 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:35.777 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:35.776 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:35.779 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:35.779 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:40.125 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 23:42:40.125 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 23:42:40.207 [http-nio-8888-exec-2] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 23:42:40.207 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@136c413e, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:41.219 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 23:42:41.220 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:41.220 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 23:42:41.243 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:41.243 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:41.246 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:41.246 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:41.246 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:42:41.247 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:41.247 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:41.247 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:42:41.247 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:41.248 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:41.248 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:41.248 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:41.248 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:41.251 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:41.251 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:41.301 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:42:41.301 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:42:41.301 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.277 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 23:42:42.278 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.278 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 23:42:42.294 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:42.297 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.297 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:42:42.297 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:42.297 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:42.297 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:42.298 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:42:42.298 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:42:42.298 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:42.298 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:42.298 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:42.298 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:42:42.299 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.299 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.299 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.299 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.300 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:42.300 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.302 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:42:42.303 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:42:42.303 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:42:42.304 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:42:42.305 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.306 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.354 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:42:42.355 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:42:42.358 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.368 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:42.369 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:42.369 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:42.369 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:42.370 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:44.968 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:42:44.969 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:44.969 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:42:44.970 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:44.970 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:48.387 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:42:48.389 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:48.390 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:42:48.390 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:48.390 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:52.089 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:42:52.090 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.090 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:42:52.108 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:52.110 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.110 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:42:52.113 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:42:52.113 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:42:52.113 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:42:52.114 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:42:52.113 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:42:52.114 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:42:52.115 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:42:52.115 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:42:52.116 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.116 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:42:52.116 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:42:52.129 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.129 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.132 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.130 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.132 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:42:52.132 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:42:52.133 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:42:52.134 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:42:52.139 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.140 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.189 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:42:52.189 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:42:52.189 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.205 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:52.205 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:52.205 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:42:52.207 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:52.207 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:42:55.193 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:42:55.194 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:42:55.194 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:42:55.195 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:42:55.195 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:44:36.831 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/2
2025-07-18 23:44:36.837 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:36.837 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/2
2025-07-18 23:44:36.838 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:44:36.839 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:44:41.882 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:44:41.884 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:41.884 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system?id=2&roleName=%E5%8C%BB%E7%94%9F&roleKey=ROLE_DOCTOR&description=%E5%8C%BB%E7%94%9F%E8%A7%92%E8%89%B2
2025-07-18 23:44:41.920 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:44:41.920 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:44:41.923 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:44:41.923 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:44:41.924 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:44:41.927 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:44:41.927 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:44:41.930 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:44:41.930 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:44:41.932 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:41.932 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:41.932 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:44:41.932 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:41.936 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:41.937 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:41.938 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:44:41.942 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:44:41.942 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:44:41.942 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:41.942 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:44:41.943 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:44:41.943 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:44:41.947 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:41.956 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:42.005 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:44:42.006 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:44:42.006 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:42.016 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:44:42.017 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:42.017 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:44:42.018 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:44:42.018 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:44:44.135 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:44:44.137 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:44.138 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:44:44.140 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:44:44.143 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:44:49.089 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 23:44:49.089 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.090 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 23:44:49.111 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:44:49.112 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /css/ui-components.css
2025-07-18 23:44:49.112 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 23:44:49.114 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 23:44:49.114 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/auth.js
2025-07-18 23:44:49.114 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /css/ui-components.css
2025-07-18 23:44:49.114 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 23:44:49.116 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/auth.js
2025-07-18 23:44:49.116 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.117 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 23:44:49.119 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 23:44:49.122 [http-nio-8888-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.122 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.123 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.123 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.128 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/ui-components.js
2025-07-18 23:44:49.128 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 23:44:49.128 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 23:44:49.129 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 23:44:49.130 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/ui-components.js
2025-07-18 23:44:49.130 [http-nio-8888-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.130 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /error
2025-07-18 23:44:49.133 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.133 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.202 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/auth/current
2025-07-18 23:44:49.203 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/auth/current
2025-07-18 23:44:49.203 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.216 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/user/list?page=1&size=10
2025-07-18 23:44:49.217 [http-nio-8888-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:49.217 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/user/list?page=1&size=10
2025-07-18 23:44:49.218 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:44:49.218 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysUserController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysUser); target is of class [com.sleephome.controller.SysUserController]
2025-07-18 23:44:50.902 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:44:50.903 [http-nio-8888-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:50.903 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:44:50.904 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:44:50.905 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:44:53.244 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 23:44:53.245 [http-nio-8888-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:53.246 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 23:44:53.250 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 23:44:53.252 [http-nio-8888-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 23:44:56.410 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 23:44:56.413 [http-nio-8888-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:56.413 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 23:44:56.414 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 23:44:56.415 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 23:44:59.370 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing PUT /api/system/permission
2025-07-18 23:44:59.371 [http-nio-8888-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:44:59.371 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured PUT /api/system/permission
2025-07-18 23:44:59.383 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.edit(com.sleephome.entity.SysPermission); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 23:44:59.384 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.edit(com.sleephome.entity.SysPermission); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 23:45:02.147 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/permission/1
2025-07-18 23:45:02.149 [http-nio-8888-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:45:02.150 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/permission/1
2025-07-18 23:45:02.150 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 23:45:02.150 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.getInfo(java.lang.Long); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 23:45:05.023 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing PUT /api/system/permission
2025-07-18 23:45:05.024 [http-nio-8888-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:45:05.024 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured PUT /api/system/permission
2025-07-18 23:45:05.025 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.edit(com.sleephome.entity.SysPermission); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 23:45:05.026 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysPermissionController.edit(com.sleephome.entity.SysPermission); target is of class [com.sleephome.controller.SysPermissionController]
2025-07-18 23:45:06.041 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:45:06.042 [http-nio-8888-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=com.sleephome.security.service.UserDetailsImpl@d0cb841, Credentials=[PROTECTED], Authenticated=true, Details=null, Granted Authorities=[system, system:user, system:role, system:permission, system:log, system:user:query, system:user:add, system:user:edit, system:user:remove, system:role:query, system:role:add, system:role:edit, system:role:remove, system:permission:query, system:permission:add, system:permission:edit, system:permission:remove, system:log:query, dashboard, patients, patients:query, patients:add, patients:edit, patients:remove, questionnaires, questionnaires:query, questionnaires:add, questionnaires:edit, questionnaires:remove, reports, reports:query, reports:add, reports:edit, reports:remove, messages, messages:query, messages:send, messages:remove]]]
2025-07-18 23:45:06.042 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/role/list?pageNum=1&pageSize=10
2025-07-18 23:45:06.044 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:45:06.044 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.SysRoleController.list(java.lang.Integer,java.lang.Integer,com.sleephome.entity.SysRole); target is of class [com.sleephome.controller.SysRoleController]
2025-07-18 23:46:33.803 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 23:46:33.809 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
