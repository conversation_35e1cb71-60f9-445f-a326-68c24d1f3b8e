2025-07-18 15:50:03.529 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 15:50:03.543 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 43151 (/Users/<USER>/AI/vscode/sleephome/target/classes started by song<PERSON><PERSON>an in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 15:50:03.543 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 15:50:03.544 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 15:50:04.012 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port(s): 8888 (http)
2025-07-18 15:50:04.016 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 15:50:04.016 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 15:50:04.016 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 15:50:04.052 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 15:50:04.053 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 489 ms
2025-07-18 15:50:04.196 [main] DEBUG com.sleephome.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-18 15:50:04.355 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 15:50:04.442 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6e9a10cd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6ea66c33, org.springframework.security.web.context.SecurityContextHolderFilter@67eeb310, org.springframework.security.web.header.HeaderWriterFilter@5563bb40, org.springframework.security.web.authentication.logout.LogoutFilter@d62472f, com.sleephome.security.jwt.AuthTokenFilter@51ac12ac, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@39ace1a7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@bd4ee01, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@59b447a4, org.springframework.security.web.session.SessionManagementFilter@15e08615, org.springframework.security.web.access.ExceptionTranslationFilter@66298fe9, org.springframework.security.web.access.intercept.AuthorizationFilter@6f3b13d0]
2025-07-18 15:50:04.538 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8888"]
2025-07-18 15:50:04.549 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8888 (http) with context path ''
2025-07-18 15:50:04.552 [main] INFO  com.sleephome.SleepManagementSystemApplication - Started SleepManagementSystemApplication in 1.192 seconds (process running for 1.315)
2025-07-18 15:50:08.768 [http-nio-8888-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 15:50:08.768 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 15:50:08.769 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 15:50:08.773 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 15:50:08.776 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 15:50:08.776 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id F304380A6311B4199A06D6E31F376F79
2025-07-18 15:50:08.781 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 15:50:08.859 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 15:50:08.859 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 15:50:08.859 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id F304380A6311B4199A06D6E31F376F79
2025-07-18 15:50:08.862 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 15:50:08.862 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 15:50:08.862 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id F304380A6311B4199A06D6E31F376F79
2025-07-18 15:50:08.864 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 15:50:08.866 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 15:50:12.487 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 15:50:12.488 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 15:50:12.488 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id F304380A6311B4199A06D6E31F376F79
2025-07-18 15:50:12.489 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 15:50:12.612 [http-nio-8888-exec-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 15:50:12.749 [http-nio-8888-exec-4] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4abf8c59
2025-07-18 15:50:12.749 [http-nio-8888-exec-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 15:50:12.859 [http-nio-8888-exec-4] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 15:50:13.897 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 15:50:13.897 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 15:50:13.898 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id F304380A6311B4199A06D6E31F376F79
2025-07-18 15:50:13.913 [http-nio-8888-exec-6] DEBUG o.s.s.web.savedrequest.HttpSessionRequestCache - Saved request http://localhost:8888/dashboard?continue to session
2025-07-18 15:50:13.914 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 15:52:05.650 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 15:52:05.653 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 15:52:05.656 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 15:52:05.692 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 15:52:05.692 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 15:52:05.707 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 15:52:05.714 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 15:52:05.724 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 15:52:05.732 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 15:52:38.096 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 15:52:38.098 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 15:52:38.100 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 15:52:38.209 [http-nio-8888-exec-5] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 15:52:39.232 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 15:52:39.232 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 15:52:39.234 [http-nio-8888-exec-4] DEBUG o.s.s.web.savedrequest.HttpSessionRequestCache - Saved request http://localhost:8888/dashboard?continue to session
2025-07-18 15:52:39.234 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
