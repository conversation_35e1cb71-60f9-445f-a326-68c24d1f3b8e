2025-07-18 12:23:09.318 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 12:23:09.332 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 652 (/Users/<USER>/AI/vscode/sleephome/target/classes started by song<PERSON><PERSON>an in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 12:23:09.332 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 12:23:09.332 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 12:23:09.834 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 12:23:09.838 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 12:23:09.839 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 12:23:09.839 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 12:23:09.871 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 12:23:09.871 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 521 ms
2025-07-18 12:23:10.010 [main] DEBUG com.sleephome.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-18 12:23:10.160 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 12:23:10.180 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Unsatisfied dependency expressed through method 'setFilterChains' parameter 0: Error creating bean with name 'filterChain' defined in class path resource [com/sleephome/config/SecurityConfig.class]: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
2025-07-18 12:23:10.181 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 12:23:10.185 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-18 12:23:10.190 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Unsatisfied dependency expressed through method 'setFilterChains' parameter 0: Error creating bean with name 'filterChain' defined in class path resource [com/sleephome/config/SecurityConfig.class]: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.resolveMethodArguments(AutowiredAnnotationBeanPostProcessor.java:875)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.inject(AutowiredAnnotationBeanPostProcessor.java:828)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:492)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:950)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.sleephome.SleepManagementSystemApplication.main(SleepManagementSystemApplication.java:12)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'filterChain' defined in class path resource [com/sleephome/config/SecurityConfig.class]: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:654)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:642)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1162)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:560)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1633)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1597)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1488)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1337)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.resolveMethodArguments(AutowiredAnnotationBeanPostProcessor.java:867)
	... 20 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:650)
	... 36 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1770)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:598)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1172)
	at org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry.createMvcMatchers(AbstractRequestMatcherRegistry.java:112)
	at org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry.requestMatchers(AbstractRequestMatcherRegistry.java:210)
	at org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry.requestMatchers(AbstractRequestMatcherRegistry.java:276)
	at com.sleephome.config.SecurityConfig.lambda$filterChain$4(SecurityConfig.java:65)
	at org.springframework.security.config.annotation.web.builders.HttpSecurity.authorizeHttpRequests(HttpSecurity.java:1466)
	at com.sleephome.config.SecurityConfig.filterChain(SecurityConfig.java:64)
	at com.sleephome.config.SecurityConfig$$SpringCGLIB$$0.CGLIB$filterChain$2(<generated>)
	at com.sleephome.config.SecurityConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.sleephome.config.SecurityConfig$$SpringCGLIB$$0.filterChain(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:139)
	... 37 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1770)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:598)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:663)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1320)
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:368)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.initHandlerMappings(HandlerMappingIntrospector.java:110)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.afterPropertiesSet(HandlerMappingIntrospector.java:98)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1817)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1766)
	... 58 common frames omitted
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'indexController' method 
com.sleephome.controller.IndexController#doctorDashboard()
to {GET [/doctor/dashboard]}: There is already 'dashboardController' bean method
com.sleephome.controller.DashboardController#doctorDashboard() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:667)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:633)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:331)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:441)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:76)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:298)
	at java.base/java.util.LinkedHashMap.forEach(LinkedHashMap.java:987)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:296)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:224)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:212)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:225)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1817)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1766)
	... 71 common frames omitted
2025-07-18 12:24:05.334 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 12:24:05.348 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 951 (/Users/<USER>/AI/vscode/sleephome/target/classes started by songdaijian in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 12:24:05.348 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 12:24:05.348 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 12:24:05.805 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 12:24:05.808 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:05.809 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 12:24:05.809 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 12:24:05.842 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 12:24:05.842 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 475 ms
2025-07-18 12:24:05.976 [main] DEBUG com.sleephome.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-18 12:24:06.130 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 12:24:06.214 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7d284f15, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@44bbb7c6, org.springframework.security.web.context.SecurityContextHolderFilter@c262f2f, org.springframework.security.web.header.HeaderWriterFilter@3b57f915, org.springframework.security.web.authentication.logout.LogoutFilter@4276ad40, com.sleephome.security.jwt.AuthTokenFilter@51ac12ac, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@11148dc2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@60a01cb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2342f1ff, org.springframework.security.web.session.SessionManagementFilter@45d389f2, org.springframework.security.web.access.ExceptionTranslationFilter@64b73e0a, org.springframework.security.web.access.intercept.AuthorizationFilter@2a19a0fe]
2025-07-18 12:24:06.301 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:06.308 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-18 12:24:06.310 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:06.310 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 12:24:06.314 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:06.314 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:06.317 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-18 12:24:06.322 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8888 was already in use.

Action:

Identify and stop the process that's listening on port 8888 or configure this application to listen on another port.

2025-07-18 12:24:48.851 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 12:24:48.867 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 1199 (/Users/<USER>/AI/vscode/sleephome/target/classes started by songdaijian in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 12:24:48.867 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 12:24:48.868 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 12:24:49.343 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 12:24:49.346 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:49.347 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 12:24:49.347 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 12:24:49.380 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 12:24:49.380 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 494 ms
2025-07-18 12:24:49.523 [main] DEBUG com.sleephome.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-18 12:24:49.679 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 12:24:49.760 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7d284f15, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@44bbb7c6, org.springframework.security.web.context.SecurityContextHolderFilter@c262f2f, org.springframework.security.web.header.HeaderWriterFilter@3b57f915, org.springframework.security.web.authentication.logout.LogoutFilter@4276ad40, com.sleephome.security.jwt.AuthTokenFilter@51ac12ac, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@11148dc2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@60a01cb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2342f1ff, org.springframework.security.web.session.SessionManagementFilter@45d389f2, org.springframework.security.web.access.ExceptionTranslationFilter@64b73e0a, org.springframework.security.web.access.intercept.AuthorizationFilter@2a19a0fe]
2025-07-18 12:24:49.850 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8888"]
2025-07-18 12:24:49.860 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8888 (http) with context path ''
2025-07-18 12:24:49.863 [main] INFO  com.sleephome.SleepManagementSystemApplication - Started SleepManagementSystemApplication in 1.221 seconds (process running for 1.348)
2025-07-18 12:24:53.815 [http-nio-8888-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 12:24:53.815 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 12:24:53.816 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 12:24:53.819 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community
2025-07-18 12:24:53.821 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:53.821 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:53.828 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community
2025-07-18 12:24:53.834 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 12:24:53.836 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:53.836 [http-nio-8888-exec-1] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:24:56.938 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 12:24:56.939 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:56.939 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:56.939 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 12:24:57.030 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:24:57.031 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:57.031 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:57.032 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:24:59.583 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:24:59.583 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:59.583 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:59.583 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:24:59.598 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:24:59.598 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:24:59.598 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:59.598 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:59.598 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:59.598 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:59.599 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:24:59.599 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:24:59.614 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:24:59.614 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:24:59.614 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:24:59.616 [http-nio-8888-exec-9] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:02.531 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:25:02.532 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:02.532 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:02.533 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:25:02.553 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:02.553 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:02.554 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:02.554 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:02.554 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:02.554 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:02.555 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:02.556 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:02.565 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:02.566 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:02.566 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:02.568 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:08.461 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community
2025-07-18 12:25:08.463 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:08.464 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:08.466 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community
2025-07-18 12:25:08.471 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 12:25:08.473 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:08.474 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:10.607 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 12:25:10.607 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:10.607 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:10.608 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 12:25:10.623 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:10.624 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:10.624 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:10.625 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:11.805 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:25:11.805 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:11.806 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:11.806 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:25:11.825 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:11.825 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:11.825 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:11.826 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:11.826 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:11.826 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:11.827 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:11.827 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:11.842 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:11.842 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:11.842 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:11.844 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:16.624 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:25:16.625 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:16.625 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:16.626 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:25:16.774 [http-nio-8888-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 12:25:16.931 [http-nio-8888-exec-5] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@bcab44c
2025-07-18 12:25:16.932 [http-nio-8888-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 12:25:17.132 [http-nio-8888-exec-5] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:25:18.188 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/dashboard
2025-07-18 12:25:18.188 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:18.188 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:18.189 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/dashboard
2025-07-18 12:25:18.223 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:18.223 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:18.224 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:18.224 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:18.224 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:18.224 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:18.225 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:18.225 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:30.691 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:25:30.693 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:30.693 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:30.694 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:25:30.716 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:30.716 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:30.716 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:30.717 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:30.717 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:30.717 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:30.719 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:30.719 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:30.733 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:30.734 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:30.734 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:30.735 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:35.850 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:25:35.851 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:35.851 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:35.851 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:25:35.938 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:25:35.942 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:35.942 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:35.943 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:25:35.972 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:35.972 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:35.972 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:35.972 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:35.972 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:35.972 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:35.973 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:35.974 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:36.007 [http-nio-8888-exec-7] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:25:36.026 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:36.026 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:36.026 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:36.029 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:37.355 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/dashboard
2025-07-18 12:25:37.355 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:37.356 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:37.357 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/dashboard
2025-07-18 12:25:37.374 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:37.374 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:37.375 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:37.375 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:37.375 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:37.375 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:37.377 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:37.377 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:40.042 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:25:40.043 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:40.043 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:40.043 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:25:40.145 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:25:41.161 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/dashboard
2025-07-18 12:25:41.161 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:41.162 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:41.162 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/dashboard
2025-07-18 12:25:41.185 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:41.185 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:41.185 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:41.185 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:41.185 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:41.185 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:41.202 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:41.203 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:46.504 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 12:25:46.505 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:46.505 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:46.506 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 12:25:46.519 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:46.519 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:46.519 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:46.519 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:46.519 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:46.519 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:46.520 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:46.520 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:46.529 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:46.529 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:46.530 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:46.531 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:25:53.737 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:25:53.740 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:53.744 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:53.747 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:25:53.768 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:25:53.768 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:25:53.768 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:53.768 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:53.768 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:53.768 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:53.769 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:25:53.770 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:25:53.778 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:25:53.778 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:25:53.778 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:25:53.779 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:26:00.199 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:26:00.200 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:00.200 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:00.200 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:26:00.306 [http-nio-8888-exec-5] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:26:01.319 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:26:01.319 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.319 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.320 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:26:01.349 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:26:01.349 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:26:01.350 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.350 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.350 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.350 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.351 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:26:01.351 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:26:01.373 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:26:01.373 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.373 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.374 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:26:01.382 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:26:01.382 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:26:01.383 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.383 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.383 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.383 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.383 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:26:01.383 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:26:01.392 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:26:01.392 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:01.392 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:01.393 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:26:10.107 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:26:10.107 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:10.108 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:10.108 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:26:10.202 [http-nio-8888-exec-5] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:26:11.224 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:26:11.224 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.225 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.225 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:26:11.247 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:26:11.247 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:26:11.247 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.247 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.247 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.247 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.248 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:26:11.249 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:26:11.264 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:26:11.264 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.264 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.265 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:26:11.273 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:26:11.273 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:26:11.273 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.273 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.273 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.273 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.273 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:26:11.274 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:26:11.284 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:26:11.284 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:11.284 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:11.284 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:26:13.799 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:26:13.800 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:26:13.800 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:26:13.808 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:10.064 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:28:10.065 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:10.067 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:10.073 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:28:10.100 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:28:10.100 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:28:10.100 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:10.100 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:10.100 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:10.100 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:10.102 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:28:10.102 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:28:10.116 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:28:10.116 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:10.116 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:10.120 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:14.842 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:28:14.843 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:14.843 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:14.843 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:28:14.937 [http-nio-8888-exec-8] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:28:15.953 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:28:15.953 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:15.953 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:15.954 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:28:15.979 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:28:15.979 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:28:15.980 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:15.980 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:15.980 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:15.980 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:15.980 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:28:15.981 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:28:15.997 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:28:15.998 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:15.998 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:15.998 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:28:16.007 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:28:16.007 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:28:16.007 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:16.007 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:16.007 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:16.007 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:16.007 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:28:16.007 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:28:16.016 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:28:16.017 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:16.017 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:16.018 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:23.810 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:28:23.810 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:23.810 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:23.815 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:27.531 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:28:27.531 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:27.531 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:27.534 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:35.661 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:28:35.662 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:35.663 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:35.665 [http-nio-8888-exec-9] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:28:58.444 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-18 12:28:58.445 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:28:58.445 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:28:58.448 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /favicon.ico
2025-07-18 12:29:08.569 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:29:08.570 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:29:08.570 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:29:08.573 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:29:08.608 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:29:08.608 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:29:08.608 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:29:08.608 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:29:08.608 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:29:08.608 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:29:08.610 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:29:08.610 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:29:08.610 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:29:08.610 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:29:08.610 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:29:08.612 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:29:08.674 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:29:08.674 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:29:08.674 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:29:08.675 [http-nio-8888-exec-9] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:31:11.244 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:31:11.247 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:31:11.248 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:31:11.250 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:31:11.273 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:31:11.273 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:31:11.273 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:31:11.273 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:31:11.273 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:31:11.273 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:31:11.274 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:31:11.275 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:31:11.284 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:31:11.284 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:31:11.284 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:31:11.285 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:31:29.832 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-18 12:31:29.833 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:31:29.834 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:31:29.836 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /favicon.ico
2025-07-18 12:33:43.862 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:33:43.865 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:43.865 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:43.867 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:33:43.929 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:33:43.929 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:33:43.929 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:43.929 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:43.930 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:43.930 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:43.932 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:33:43.932 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:33:43.943 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:33:43.944 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:43.944 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:43.945 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:33:48.399 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:33:48.399 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:48.399 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:48.401 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:33:51.262 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:33:51.263 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:51.263 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:51.263 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:33:51.385 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:33:52.404 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:33:52.404 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.404 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.405 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:33:52.424 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:33:52.424 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.424 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.425 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:33:52.425 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:33:52.425 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.425 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.425 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.425 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.426 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:33:52.426 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:33:52.427 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:33:52.488 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:33:52.488 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.488 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.491 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:33:52.505 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:33:52.506 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.506 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.506 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:33:52.506 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.506 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.506 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:33:52.507 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:33:52.507 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:33:52.509 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.509 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.509 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:33:52.559 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.svg
2025-07-18 12:33:52.560 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:33:52.560 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:33:52.561 [http-nio-8888-exec-9] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:40:26.286 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:40:26.287 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:26.287 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:40:26.362 [http-nio-8888-exec-8] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:40:36.174 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:40:36.174 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:36.175 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:36.178 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:40:36.208 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:40:36.208 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:40:36.208 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:36.208 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:36.208 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:36.208 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:36.209 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:40:36.210 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:40:50.768 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:40:50.768 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:50.769 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:50.775 [http-nio-8888-exec-1] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:40:54.959 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:40:54.960 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:54.961 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:54.962 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:40:55.072 [http-nio-8888-exec-4] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:40:56.097 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:40:56.097 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.097 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.097 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:40:56.117 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:40:56.118 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.118 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.124 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:40:56.125 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.125 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.124 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:40:56.125 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.125 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.124 [http-nio-8888-exec-7] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:40:56.126 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:40:56.126 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:40:56.184 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:40:56.184 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.184 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.184 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:40:56.196 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:40:56.196 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.196 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.197 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:40:56.197 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:40:56.197 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.197 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:40:56.197 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.197 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:40:56.198 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:40:56.198 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:40:56.198 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:41:17.054 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:41:17.061 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:17.061 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:17.062 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:41:17.089 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:41:17.089 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:17.089 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:17.091 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:41:17.097 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:41:17.097 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:17.097 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:17.099 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:41:47.753 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:41:47.755 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:47.755 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:47.756 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:41:47.842 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:41:48.865 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:41:48.865 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.865 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.865 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:41:48.890 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:41:48.890 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:41:48.891 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.891 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.891 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.891 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.894 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:41:48.894 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:41:48.915 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:41:48.915 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.915 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.915 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:41:48.929 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:41:48.929 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:41:48.930 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.930 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:41:48.930 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.930 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:41:48.931 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:41:48.931 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:43:09.309 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:43:09.310 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:09.310 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:09.312 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:43:09.328 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:43:09.328 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:43:09.328 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:09.328 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:09.328 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:09.328 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:09.328 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:43:09.329 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:43:12.490 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:43:12.491 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:12.491 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:12.495 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:43:14.426 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:43:14.427 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:14.427 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:14.427 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:43:14.439 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:43:14.439 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:43:14.439 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:14.439 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:14.439 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:14.439 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:14.440 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:43:14.440 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:43:14.440 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:43:14.440 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:43:14.441 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:43:14.441 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:44:22.098 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:44:22.100 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:44:22.100 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:44:22.105 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:44:22.132 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:44:22.132 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:44:22.132 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:44:22.134 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:44:22.145 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:44:22.145 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:44:22.145 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:44:22.146 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:44:34.118 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:44:34.119 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:44:34.119 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:44:34.197 [http-nio-8888-exec-3] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:45:54.245 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:45:54.257 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:54.258 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:54.260 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:45:54.300 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:45:54.300 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:45:54.301 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:54.301 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:54.301 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:54.301 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:54.303 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:45:54.304 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:45:58.828 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:45:58.829 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:58.829 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:58.829 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:45:58.945 [http-nio-8888-exec-8] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:45:59.962 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:45:59.962 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:59.962 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:59.963 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:45:59.986 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:45:59.986 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:45:59.987 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:59.987 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:59.987 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:45:59.987 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:45:59.995 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:45:59.996 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:46:00.014 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:46:00.014 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:46:00.015 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:46:00.017 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:46:00.028 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:46:00.028 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:46:00.029 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:46:00.029 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:46:00.029 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:46:00.029 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:46:00.029 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:46:00.029 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:50:06.888 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 12:50:06.890 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:50:06.891 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:50:06.893 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:52:32.372 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:52:32.376 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:32.377 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:32.381 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:52:32.446 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:52:32.446 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:52:32.448 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:32.449 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:32.449 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:32.449 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:32.451 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:52:32.451 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:52:32.457 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:52:32.457 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:32.457 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:32.459 [http-nio-8888-exec-1] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:52:35.984 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 12:52:35.985 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:35.985 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:35.985 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 12:52:36.128 [http-nio-8888-exec-4] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 12:52:37.150 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/dashboard
2025-07-18 12:52:37.150 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.150 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.150 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/dashboard
2025-07-18 12:52:37.167 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:52:37.168 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.168 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.169 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:52:37.171 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:52:37.172 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.172 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.172 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:52:37.172 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.172 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.172 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:52:37.173 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 12:52:37.230 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 12:52:37.230 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.230 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.230 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 12:52:37.239 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 12:52:37.240 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.240 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.240 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 12:52:37.240 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 12:52:37.240 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.241 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.240 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 12:52:37.241 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 12:52:37.241 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 12:52:37.241 [http-nio-8888-exec-7] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 12:52:37.241 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:03:12.504 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:03:12.505 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:03:12.505 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:03:12.506 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:03:12.546 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:03:12.547 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:03:12.547 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:03:12.548 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:03:12.549 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:03:12.549 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:03:12.550 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:03:12.550 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:06:19.490 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:06:19.492 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:19.493 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:19.494 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:06:19.557 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:06:19.557 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:06:19.557 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:19.557 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:19.557 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:19.557 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:19.557 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:06:19.558 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:06:24.664 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:06:24.664 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:24.664 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:24.664 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:06:24.815 [http-nio-8888-exec-10] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:06:25.831 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:06:25.831 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:25.831 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:25.832 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:37.028 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 13:06:37.029 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:37.029 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:37.030 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:39.927 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:06:39.927 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:39.927 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:39.929 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:39.948 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 13:06:39.949 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:39.949 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:39.950 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:45.473 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:06:45.474 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:45.474 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:45.476 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:50.269 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:06:50.270 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:50.270 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:50.270 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:06:50.290 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:06:50.291 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:50.291 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:50.292 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:06:50.292 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:50.293 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:50.294 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 13:06:50.294 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:50.294 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:50.298 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:06:50.299 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:06:50.299 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:53.836 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:06:53.837 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:53.837 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:53.837 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:06:53.944 [http-nio-8888-exec-10] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:06:54.957 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:06:54.957 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:54.957 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:54.958 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:06:54.973 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 13:06:54.974 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:06:54.974 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:06:54.975 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:11:42.219 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:11:42.221 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:11:42.221 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:11:42.222 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:11:42.297 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:11:42.297 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:11:42.298 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:11:42.319 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:11:42.328 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:11:42.350 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:11:42.350 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:11:42.354 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:12:03.505 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:12:03.506 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:12:03.509 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:12:03.509 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:12:03.615 [http-nio-8888-exec-2] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:12:04.632 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:12:04.633 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:12:04.633 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:12:04.634 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:13:25.307 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:13:25.307 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:13:25.308 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:13:25.386 [http-nio-8888-exec-3] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:15:22.195 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:15:22.196 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:15:22.196 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:17:37.004 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:17:37.005 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:17:37.006 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:18:53.623 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 13:18:53.626 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-18 13:20:19.872 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 13:20:19.884 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 12721 (/Users/<USER>/AI/vscode/sleephome/target/classes started by songdaijian in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 13:20:19.885 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 13:20:19.885 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 13:20:20.335 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 13:20:20.339 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 13:20:20.340 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 13:20:20.340 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 13:20:20.374 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 13:20:20.374 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 471 ms
2025-07-18 13:20:20.512 [main] DEBUG com.sleephome.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-18 13:20:20.676 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 13:20:20.761 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2964511, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6e9a10cd, org.springframework.security.web.context.SecurityContextHolderFilter@7e2bc2f4, org.springframework.security.web.header.HeaderWriterFilter@c262f2f, org.springframework.security.web.authentication.logout.LogoutFilter@21090c88, com.sleephome.security.jwt.AuthTokenFilter@5c3d4f05, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@76af51d6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1930a804, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6ea66c33, org.springframework.security.web.session.SessionManagementFilter@58882a93, org.springframework.security.web.access.ExceptionTranslationFilter@308e87a1, org.springframework.security.web.access.intercept.AuthorizationFilter@3ed7dd70]
2025-07-18 13:20:20.850 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8888"]
2025-07-18 13:20:20.860 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8888 (http) with context path ''
2025-07-18 13:20:20.863 [main] INFO  com.sleephome.SleepManagementSystemApplication - Started SleepManagementSystemApplication in 1.155 seconds (process running for 1.269)
2025-07-18 13:21:02.228 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 13:21:02.227 [http-nio-8888-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 13:21:02.229 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 13:21:02.232 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:21:02.235 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:21:02.238 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:21:02.330 [http-nio-8888-exec-1] ERROR org.thymeleaf.TemplateEngine - [THYMELEAF][http-nio-8888-exec-1] Exception processing template "dashboard": An error happened during template parsing (template: "class path resource [templates/dashboard.html]")
org.thymeleaf.exceptions.TemplateInputException: An error happened during template parsing (template: "class path resource [templates/dashboard.html]")
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parse(AbstractMarkupTemplateParser.java:241)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parseStandalone(AbstractMarkupTemplateParser.java:100)
	at org.thymeleaf.engine.TemplateManager.parseAndProcess(TemplateManager.java:666)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1103)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1077)
	at org.thymeleaf.spring6.view.ThymeleafView.renderFragment(ThymeleafView.java:372)
	at org.thymeleaf.spring6.view.ThymeleafView.render(ThymeleafView.java:192)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1415)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1159)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1098)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:974)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1011)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sleephome.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:52)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: org.attoparser.ParseException: Could not parse as expression: "'ml-2 px-2 py-0.5 rounded-full text-sm font-medium ' + 
                    (${userRole == 'doctor'} ? 'bg-green-100 text-green-600' : 
                     ${userRole == 'community'} ? 'bg-blue-100 text-blue-600' : 
                     'bg-purple-100 text-purple-600')" (template: "dashboard" - line 76, col 23)
	at org.attoparser.MarkupParser.parseDocument(MarkupParser.java:393)
	at org.attoparser.MarkupParser.parse(MarkupParser.java:257)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parse(AbstractMarkupTemplateParser.java:230)
	... 92 common frames omitted
Caused by: org.thymeleaf.exceptions.TemplateProcessingException: Could not parse as expression: "'ml-2 px-2 py-0.5 rounded-full text-sm font-medium ' + 
                    (${userRole == 'doctor'} ? 'bg-green-100 text-green-600' : 
                     ${userRole == 'community'} ? 'bg-blue-100 text-blue-600' : 
                     'bg-purple-100 text-purple-600')" (template: "dashboard" - line 76, col 23)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:131)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:62)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:44)
	at org.thymeleaf.engine.EngineEventUtils.parseAttributeExpression(EngineEventUtils.java:220)
	at org.thymeleaf.engine.EngineEventUtils.computeAttributeExpression(EngineEventUtils.java:207)
	at org.thymeleaf.standard.processor.AbstractStandardExpressionAttributeTagProcessor.doProcess(AbstractStandardExpressionAttributeTagProcessor.java:125)
	at org.thymeleaf.processor.element.AbstractAttributeTagProcessor.doProcess(AbstractAttributeTagProcessor.java:74)
	at org.thymeleaf.processor.element.AbstractElementTagProcessor.process(AbstractElementTagProcessor.java:95)
	at org.thymeleaf.util.ProcessorConfigurationUtils$ElementTagProcessorWrapper.process(ProcessorConfigurationUtils.java:633)
	at org.thymeleaf.engine.ProcessorTemplateHandler.handleOpenElement(ProcessorTemplateHandler.java:1314)
	at org.thymeleaf.engine.TemplateHandlerAdapterMarkupHandler.handleOpenElementEnd(TemplateHandlerAdapterMarkupHandler.java:304)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler$InlineMarkupAdapterPreProcessorHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:278)
	at org.thymeleaf.standard.inline.OutputExpressionInlinePreProcessorHandler.handleOpenElementEnd(OutputExpressionInlinePreProcessorHandler.java:186)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:124)
	at org.attoparser.HtmlElement.handleOpenElementEnd(HtmlElement.java:109)
	at org.attoparser.HtmlMarkupHandler.handleOpenElementEnd(HtmlMarkupHandler.java:297)
	at org.attoparser.MarkupEventProcessorHandler.handleOpenElementEnd(MarkupEventProcessorHandler.java:402)
	at org.attoparser.ParsingElementMarkupUtil.parseOpenElement(ParsingElementMarkupUtil.java:159)
	at org.attoparser.MarkupParser.parseBuffer(MarkupParser.java:710)
	at org.attoparser.MarkupParser.parseDocument(MarkupParser.java:301)
	... 94 common frames omitted
2025-07-18 13:21:02.331 [http-nio-8888-exec-1] ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.thymeleaf.exceptions.TemplateInputException: An error happened during template parsing (template: "class path resource [templates/dashboard.html]")] with root cause
org.thymeleaf.exceptions.TemplateProcessingException: Could not parse as expression: "'ml-2 px-2 py-0.5 rounded-full text-sm font-medium ' + 
                    (${userRole == 'doctor'} ? 'bg-green-100 text-green-600' : 
                     ${userRole == 'community'} ? 'bg-blue-100 text-blue-600' : 
                     'bg-purple-100 text-purple-600')" (template: "dashboard" - line 76, col 23)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:131)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:62)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:44)
	at org.thymeleaf.engine.EngineEventUtils.parseAttributeExpression(EngineEventUtils.java:220)
	at org.thymeleaf.engine.EngineEventUtils.computeAttributeExpression(EngineEventUtils.java:207)
	at org.thymeleaf.standard.processor.AbstractStandardExpressionAttributeTagProcessor.doProcess(AbstractStandardExpressionAttributeTagProcessor.java:125)
	at org.thymeleaf.processor.element.AbstractAttributeTagProcessor.doProcess(AbstractAttributeTagProcessor.java:74)
	at org.thymeleaf.processor.element.AbstractElementTagProcessor.process(AbstractElementTagProcessor.java:95)
	at org.thymeleaf.util.ProcessorConfigurationUtils$ElementTagProcessorWrapper.process(ProcessorConfigurationUtils.java:633)
	at org.thymeleaf.engine.ProcessorTemplateHandler.handleOpenElement(ProcessorTemplateHandler.java:1314)
	at org.thymeleaf.engine.TemplateHandlerAdapterMarkupHandler.handleOpenElementEnd(TemplateHandlerAdapterMarkupHandler.java:304)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler$InlineMarkupAdapterPreProcessorHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:278)
	at org.thymeleaf.standard.inline.OutputExpressionInlinePreProcessorHandler.handleOpenElementEnd(OutputExpressionInlinePreProcessorHandler.java:186)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:124)
	at org.attoparser.HtmlElement.handleOpenElementEnd(HtmlElement.java:109)
	at org.attoparser.HtmlMarkupHandler.handleOpenElementEnd(HtmlMarkupHandler.java:297)
	at org.attoparser.MarkupEventProcessorHandler.handleOpenElementEnd(MarkupEventProcessorHandler.java:402)
	at org.attoparser.ParsingElementMarkupUtil.parseOpenElement(ParsingElementMarkupUtil.java:159)
	at org.attoparser.MarkupParser.parseBuffer(MarkupParser.java:710)
	at org.attoparser.MarkupParser.parseDocument(MarkupParser.java:301)
	at org.attoparser.MarkupParser.parse(MarkupParser.java:257)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parse(AbstractMarkupTemplateParser.java:230)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parseStandalone(AbstractMarkupTemplateParser.java:100)
	at org.thymeleaf.engine.TemplateManager.parseAndProcess(TemplateManager.java:666)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1103)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1077)
	at org.thymeleaf.spring6.view.ThymeleafView.renderFragment(ThymeleafView.java:372)
	at org.thymeleaf.spring6.view.ThymeleafView.render(ThymeleafView.java:192)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1415)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1159)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1098)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:974)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1011)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sleephome.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:52)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-07-18 13:21:02.333 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 13:21:02.335 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:21:02.335 [http-nio-8888-exec-1] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:42:40.064 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:42:40.066 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:40.066 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:40.068 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:42:40.105 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:42:40.106 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:40.106 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:40.109 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:42:40.109 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:40.109 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:40.114 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:42:40.115 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:40.115 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:40.119 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:42:40.119 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:42:40.120 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:42:40.228 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:42:40.284 [http-nio-8888-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 13:42:40.444 [http-nio-8888-exec-6] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@44cbf456
2025-07-18 13:42:40.445 [http-nio-8888-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 13:42:40.488 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:42:40.491 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:42:40.495 [http-nio-8888-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:42:44.920 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:42:44.920 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:44.920 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:44.922 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:42:44.937 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:42:44.937 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:42:44.937 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:42:44.937 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:44.937 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:44.938 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:44.938 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:44.938 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:44.938 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:44.939 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:42:44.939 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:42:44.940 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:42:44.951 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:42:44.959 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:42:44.959 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:42:44.959 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:42:52.144 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 13:42:52.145 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:52.145 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:52.145 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 13:42:52.161 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:42:52.162 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:52.162 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:52.162 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:42:52.173 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:42:52.173 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:42:52.174 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:52.174 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:52.174 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:52.174 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:52.175 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:42:52.175 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:42:57.244 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:42:57.245 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:57.245 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:57.245 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:42:57.428 [http-nio-8888-exec-5] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:42:58.449 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:42:58.450 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:58.450 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:42:58.451 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:42:58.460 [http-nio-8888-exec-3] ERROR org.thymeleaf.TemplateEngine - [THYMELEAF][http-nio-8888-exec-3] Exception processing template "dashboard": An error happened during template parsing (template: "class path resource [templates/dashboard.html]")
org.thymeleaf.exceptions.TemplateInputException: An error happened during template parsing (template: "class path resource [templates/dashboard.html]")
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parse(AbstractMarkupTemplateParser.java:241)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parseStandalone(AbstractMarkupTemplateParser.java:100)
	at org.thymeleaf.engine.TemplateManager.parseAndProcess(TemplateManager.java:666)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1103)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1077)
	at org.thymeleaf.spring6.view.ThymeleafView.renderFragment(ThymeleafView.java:372)
	at org.thymeleaf.spring6.view.ThymeleafView.render(ThymeleafView.java:192)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1415)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1159)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1098)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:974)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1011)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sleephome.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:52)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: org.attoparser.ParseException: Could not parse as expression: "'w-12 h-12 rounded-full flex items-center justify-center ' + 
                            (${userRole == 'doctor'} ? 'bg-green-100' : 
                             ${userRole == 'community'} ? 'bg-blue-100' : 
                             'bg-purple-100')" (template: "dashboard" - line 126, col 30)
	at org.attoparser.MarkupParser.parseDocument(MarkupParser.java:393)
	at org.attoparser.MarkupParser.parse(MarkupParser.java:257)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parse(AbstractMarkupTemplateParser.java:230)
	... 92 common frames omitted
Caused by: org.thymeleaf.exceptions.TemplateProcessingException: Could not parse as expression: "'w-12 h-12 rounded-full flex items-center justify-center ' + 
                            (${userRole == 'doctor'} ? 'bg-green-100' : 
                             ${userRole == 'community'} ? 'bg-blue-100' : 
                             'bg-purple-100')" (template: "dashboard" - line 126, col 30)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:131)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:62)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:44)
	at org.thymeleaf.engine.EngineEventUtils.parseAttributeExpression(EngineEventUtils.java:220)
	at org.thymeleaf.engine.EngineEventUtils.computeAttributeExpression(EngineEventUtils.java:207)
	at org.thymeleaf.standard.processor.AbstractStandardExpressionAttributeTagProcessor.doProcess(AbstractStandardExpressionAttributeTagProcessor.java:125)
	at org.thymeleaf.processor.element.AbstractAttributeTagProcessor.doProcess(AbstractAttributeTagProcessor.java:74)
	at org.thymeleaf.processor.element.AbstractElementTagProcessor.process(AbstractElementTagProcessor.java:95)
	at org.thymeleaf.util.ProcessorConfigurationUtils$ElementTagProcessorWrapper.process(ProcessorConfigurationUtils.java:633)
	at org.thymeleaf.engine.ProcessorTemplateHandler.handleOpenElement(ProcessorTemplateHandler.java:1314)
	at org.thymeleaf.engine.TemplateHandlerAdapterMarkupHandler.handleOpenElementEnd(TemplateHandlerAdapterMarkupHandler.java:304)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler$InlineMarkupAdapterPreProcessorHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:278)
	at org.thymeleaf.standard.inline.OutputExpressionInlinePreProcessorHandler.handleOpenElementEnd(OutputExpressionInlinePreProcessorHandler.java:186)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:124)
	at org.attoparser.HtmlElement.handleOpenElementEnd(HtmlElement.java:109)
	at org.attoparser.HtmlMarkupHandler.handleOpenElementEnd(HtmlMarkupHandler.java:297)
	at org.attoparser.MarkupEventProcessorHandler.handleOpenElementEnd(MarkupEventProcessorHandler.java:402)
	at org.attoparser.ParsingElementMarkupUtil.parseOpenElement(ParsingElementMarkupUtil.java:159)
	at org.attoparser.MarkupParser.parseBuffer(MarkupParser.java:710)
	at org.attoparser.MarkupParser.parseDocument(MarkupParser.java:301)
	... 94 common frames omitted
2025-07-18 13:42:58.462 [http-nio-8888-exec-3] ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.thymeleaf.exceptions.TemplateInputException: An error happened during template parsing (template: "class path resource [templates/dashboard.html]")] with root cause
org.thymeleaf.exceptions.TemplateProcessingException: Could not parse as expression: "'w-12 h-12 rounded-full flex items-center justify-center ' + 
                            (${userRole == 'doctor'} ? 'bg-green-100' : 
                             ${userRole == 'community'} ? 'bg-blue-100' : 
                             'bg-purple-100')" (template: "dashboard" - line 126, col 30)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:131)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:62)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:44)
	at org.thymeleaf.engine.EngineEventUtils.parseAttributeExpression(EngineEventUtils.java:220)
	at org.thymeleaf.engine.EngineEventUtils.computeAttributeExpression(EngineEventUtils.java:207)
	at org.thymeleaf.standard.processor.AbstractStandardExpressionAttributeTagProcessor.doProcess(AbstractStandardExpressionAttributeTagProcessor.java:125)
	at org.thymeleaf.processor.element.AbstractAttributeTagProcessor.doProcess(AbstractAttributeTagProcessor.java:74)
	at org.thymeleaf.processor.element.AbstractElementTagProcessor.process(AbstractElementTagProcessor.java:95)
	at org.thymeleaf.util.ProcessorConfigurationUtils$ElementTagProcessorWrapper.process(ProcessorConfigurationUtils.java:633)
	at org.thymeleaf.engine.ProcessorTemplateHandler.handleOpenElement(ProcessorTemplateHandler.java:1314)
	at org.thymeleaf.engine.TemplateHandlerAdapterMarkupHandler.handleOpenElementEnd(TemplateHandlerAdapterMarkupHandler.java:304)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler$InlineMarkupAdapterPreProcessorHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:278)
	at org.thymeleaf.standard.inline.OutputExpressionInlinePreProcessorHandler.handleOpenElementEnd(OutputExpressionInlinePreProcessorHandler.java:186)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:124)
	at org.attoparser.HtmlElement.handleOpenElementEnd(HtmlElement.java:109)
	at org.attoparser.HtmlMarkupHandler.handleOpenElementEnd(HtmlMarkupHandler.java:297)
	at org.attoparser.MarkupEventProcessorHandler.handleOpenElementEnd(MarkupEventProcessorHandler.java:402)
	at org.attoparser.ParsingElementMarkupUtil.parseOpenElement(ParsingElementMarkupUtil.java:159)
	at org.attoparser.MarkupParser.parseBuffer(MarkupParser.java:710)
	at org.attoparser.MarkupParser.parseDocument(MarkupParser.java:301)
	at org.attoparser.MarkupParser.parse(MarkupParser.java:257)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parse(AbstractMarkupTemplateParser.java:230)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parseStandalone(AbstractMarkupTemplateParser.java:100)
	at org.thymeleaf.engine.TemplateManager.parseAndProcess(TemplateManager.java:666)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1103)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1077)
	at org.thymeleaf.spring6.view.ThymeleafView.renderFragment(ThymeleafView.java:372)
	at org.thymeleaf.spring6.view.ThymeleafView.render(ThymeleafView.java:192)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1415)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1159)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1098)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:974)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1011)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sleephome.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:52)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-07-18 13:42:58.463 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 13:42:58.468 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:42:58.468 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:43:01.525 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:43:01.525 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:01.525 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:01.526 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:43:01.529 [http-nio-8888-exec-6] ERROR org.thymeleaf.TemplateEngine - [THYMELEAF][http-nio-8888-exec-6] Exception processing template "dashboard": An error happened during template parsing (template: "class path resource [templates/dashboard.html]")
org.thymeleaf.exceptions.TemplateInputException: An error happened during template parsing (template: "class path resource [templates/dashboard.html]")
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parse(AbstractMarkupTemplateParser.java:241)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parseStandalone(AbstractMarkupTemplateParser.java:100)
	at org.thymeleaf.engine.TemplateManager.parseAndProcess(TemplateManager.java:666)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1103)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1077)
	at org.thymeleaf.spring6.view.ThymeleafView.renderFragment(ThymeleafView.java:372)
	at org.thymeleaf.spring6.view.ThymeleafView.render(ThymeleafView.java:192)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1415)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1159)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1098)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:974)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1011)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sleephome.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:52)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: org.attoparser.ParseException: Could not parse as expression: "'w-12 h-12 rounded-full flex items-center justify-center ' + 
                            (${userRole == 'doctor'} ? 'bg-green-100' : 
                             ${userRole == 'community'} ? 'bg-blue-100' : 
                             'bg-purple-100')" (template: "dashboard" - line 126, col 30)
	at org.attoparser.MarkupParser.parseDocument(MarkupParser.java:393)
	at org.attoparser.MarkupParser.parse(MarkupParser.java:257)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parse(AbstractMarkupTemplateParser.java:230)
	... 92 common frames omitted
Caused by: org.thymeleaf.exceptions.TemplateProcessingException: Could not parse as expression: "'w-12 h-12 rounded-full flex items-center justify-center ' + 
                            (${userRole == 'doctor'} ? 'bg-green-100' : 
                             ${userRole == 'community'} ? 'bg-blue-100' : 
                             'bg-purple-100')" (template: "dashboard" - line 126, col 30)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:131)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:62)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:44)
	at org.thymeleaf.engine.EngineEventUtils.parseAttributeExpression(EngineEventUtils.java:220)
	at org.thymeleaf.engine.EngineEventUtils.computeAttributeExpression(EngineEventUtils.java:207)
	at org.thymeleaf.standard.processor.AbstractStandardExpressionAttributeTagProcessor.doProcess(AbstractStandardExpressionAttributeTagProcessor.java:125)
	at org.thymeleaf.processor.element.AbstractAttributeTagProcessor.doProcess(AbstractAttributeTagProcessor.java:74)
	at org.thymeleaf.processor.element.AbstractElementTagProcessor.process(AbstractElementTagProcessor.java:95)
	at org.thymeleaf.util.ProcessorConfigurationUtils$ElementTagProcessorWrapper.process(ProcessorConfigurationUtils.java:633)
	at org.thymeleaf.engine.ProcessorTemplateHandler.handleOpenElement(ProcessorTemplateHandler.java:1314)
	at org.thymeleaf.engine.TemplateHandlerAdapterMarkupHandler.handleOpenElementEnd(TemplateHandlerAdapterMarkupHandler.java:304)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler$InlineMarkupAdapterPreProcessorHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:278)
	at org.thymeleaf.standard.inline.OutputExpressionInlinePreProcessorHandler.handleOpenElementEnd(OutputExpressionInlinePreProcessorHandler.java:186)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:124)
	at org.attoparser.HtmlElement.handleOpenElementEnd(HtmlElement.java:109)
	at org.attoparser.HtmlMarkupHandler.handleOpenElementEnd(HtmlMarkupHandler.java:297)
	at org.attoparser.MarkupEventProcessorHandler.handleOpenElementEnd(MarkupEventProcessorHandler.java:402)
	at org.attoparser.ParsingElementMarkupUtil.parseOpenElement(ParsingElementMarkupUtil.java:159)
	at org.attoparser.MarkupParser.parseBuffer(MarkupParser.java:710)
	at org.attoparser.MarkupParser.parseDocument(MarkupParser.java:301)
	... 94 common frames omitted
2025-07-18 13:43:01.530 [http-nio-8888-exec-6] ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.thymeleaf.exceptions.TemplateInputException: An error happened during template parsing (template: "class path resource [templates/dashboard.html]")] with root cause
org.thymeleaf.exceptions.TemplateProcessingException: Could not parse as expression: "'w-12 h-12 rounded-full flex items-center justify-center ' + 
                            (${userRole == 'doctor'} ? 'bg-green-100' : 
                             ${userRole == 'community'} ? 'bg-blue-100' : 
                             'bg-purple-100')" (template: "dashboard" - line 126, col 30)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:131)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:62)
	at org.thymeleaf.standard.expression.StandardExpressionParser.parseExpression(StandardExpressionParser.java:44)
	at org.thymeleaf.engine.EngineEventUtils.parseAttributeExpression(EngineEventUtils.java:220)
	at org.thymeleaf.engine.EngineEventUtils.computeAttributeExpression(EngineEventUtils.java:207)
	at org.thymeleaf.standard.processor.AbstractStandardExpressionAttributeTagProcessor.doProcess(AbstractStandardExpressionAttributeTagProcessor.java:125)
	at org.thymeleaf.processor.element.AbstractAttributeTagProcessor.doProcess(AbstractAttributeTagProcessor.java:74)
	at org.thymeleaf.processor.element.AbstractElementTagProcessor.process(AbstractElementTagProcessor.java:95)
	at org.thymeleaf.util.ProcessorConfigurationUtils$ElementTagProcessorWrapper.process(ProcessorConfigurationUtils.java:633)
	at org.thymeleaf.engine.ProcessorTemplateHandler.handleOpenElement(ProcessorTemplateHandler.java:1314)
	at org.thymeleaf.engine.TemplateHandlerAdapterMarkupHandler.handleOpenElementEnd(TemplateHandlerAdapterMarkupHandler.java:304)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler$InlineMarkupAdapterPreProcessorHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:278)
	at org.thymeleaf.standard.inline.OutputExpressionInlinePreProcessorHandler.handleOpenElementEnd(OutputExpressionInlinePreProcessorHandler.java:186)
	at org.thymeleaf.templateparser.markup.InlinedOutputExpressionMarkupHandler.handleOpenElementEnd(InlinedOutputExpressionMarkupHandler.java:124)
	at org.attoparser.HtmlElement.handleOpenElementEnd(HtmlElement.java:109)
	at org.attoparser.HtmlMarkupHandler.handleOpenElementEnd(HtmlMarkupHandler.java:297)
	at org.attoparser.MarkupEventProcessorHandler.handleOpenElementEnd(MarkupEventProcessorHandler.java:402)
	at org.attoparser.ParsingElementMarkupUtil.parseOpenElement(ParsingElementMarkupUtil.java:159)
	at org.attoparser.MarkupParser.parseBuffer(MarkupParser.java:710)
	at org.attoparser.MarkupParser.parseDocument(MarkupParser.java:301)
	at org.attoparser.MarkupParser.parse(MarkupParser.java:257)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parse(AbstractMarkupTemplateParser.java:230)
	at org.thymeleaf.templateparser.markup.AbstractMarkupTemplateParser.parseStandalone(AbstractMarkupTemplateParser.java:100)
	at org.thymeleaf.engine.TemplateManager.parseAndProcess(TemplateManager.java:666)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1103)
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1077)
	at org.thymeleaf.spring6.view.ThymeleafView.renderFragment(ThymeleafView.java:372)
	at org.thymeleaf.spring6.view.ThymeleafView.render(ThymeleafView.java:192)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1415)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1159)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1098)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:974)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1011)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sleephome.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:52)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-07-18 13:43:01.533 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 13:43:01.535 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:01.535 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:43:02.818 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:43:02.818 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:02.818 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:02.818 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:43:02.826 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:43:02.826 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:02.826 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:02.827 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:43:04.813 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:43:04.815 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:04.815 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:04.816 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:43:04.829 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:43:04.829 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:43:04.830 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:04.830 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:04.830 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:04.830 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:04.831 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:43:04.831 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:43:04.836 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:43:04.844 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:43:04.844 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:43:04.844 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:43:07.043 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:43:07.044 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:07.044 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:07.045 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:43:07.065 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:43:07.065 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:43:07.066 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:43:07.066 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:07.066 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:07.066 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:07.066 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:07.066 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:07.066 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:07.067 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:43:07.067 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:43:07.067 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:43:07.077 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:43:07.084 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:43:07.085 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:43:07.085 [http-nio-8888-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:43:11.516 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles?page=1&size=10
2025-07-18 13:43:11.531 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles?page=1&size=10
2025-07-18 13:43:11.534 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 13:43:11.536 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:11.536 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:43:11.542 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:43:11.542 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:11.542 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:11.543 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:43:11.552 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:43:11.552 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:11.553 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:11.553 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:43:11.553 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:43:11.553 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:11.554 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:11.554 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:43:14.610 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:43:14.610 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:14.610 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:14.611 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:43:14.629 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:43:14.629 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:43:14.629 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:14.629 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:14.629 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:14.629 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:14.630 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:43:14.630 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:43:14.643 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:43:14.643 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:43:14.643 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:14.643 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:14.643 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:14.643 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:14.644 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:43:14.644 [http-nio-8888-exec-2] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:43:14.650 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:43:14.650 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:14.650 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:14.651 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:43:14.659 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:43:14.659 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:43:14.659 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:14.659 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:14.659 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:14.659 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:43:14.660 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:43:14.660 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:43:53.427 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:43:53.428 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:43:53.429 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:44:14.771 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:44:14.772 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:14.772 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:14.773 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:44:14.794 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:44:14.794 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:14.794 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:14.796 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:44:14.796 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:44:14.796 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:14.796 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:14.798 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:44:14.835 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 13:44:14.835 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:14.835 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:14.835 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 13:44:14.841 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:44:14.841 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:14.841 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:14.842 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:44:14.851 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:44:14.851 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:44:14.851 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:14.851 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:14.851 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:14.851 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:14.852 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:44:14.852 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:44:20.216 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:44:20.216 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:20.216 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:20.216 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:44:20.323 [http-nio-8888-exec-10] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:44:21.344 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:44:21.344 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:21.344 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:21.345 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:44:21.374 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:44:21.374 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:44:21.374 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:21.374 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:21.374 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:21.374 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:21.376 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:44:21.377 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:44:23.595 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:44:23.596 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:23.596 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:23.596 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:44:23.613 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:44:23.613 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:44:23.613 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:44:23.613 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:23.613 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:23.613 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:23.613 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:23.613 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:23.613 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:23.614 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:44:23.614 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:44:23.614 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:44:23.620 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:44:23.628 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:44:23.629 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:44:23.629 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:44:33.888 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:44:33.890 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:33.890 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:33.891 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:44:33.915 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:44:33.915 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:44:33.916 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:33.916 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:33.916 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:33.916 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:33.917 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:44:33.917 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:44:39.725 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 13:44:39.726 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:39.726 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:39.726 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 13:44:39.734 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:44:39.734 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:39.734 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:39.735 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:44:39.750 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:44:39.751 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:39.751 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:39.751 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:44:39.751 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:39.751 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:39.752 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:44:39.752 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:44:55.109 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:44:55.110 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:55.110 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:55.110 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:44:55.213 [http-nio-8888-exec-8] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:44:56.232 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:44:56.233 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:56.233 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:56.233 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:44:56.245 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:44:56.245 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:44:56.245 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:56.245 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:56.245 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:44:56.245 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:44:56.246 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:44:56.246 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:45:06.620 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:45:06.621 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:06.621 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:06.632 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:45:06.656 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:45:06.656 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:06.656 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:06.658 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:45:06.658 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:45:06.658 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:06.658 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:06.659 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:45:16.250 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:45:16.252 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:16.252 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:16.253 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:45:16.359 [http-nio-8888-exec-3] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:45:17.380 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:45:17.380 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:17.381 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:17.381 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:45:17.399 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:45:17.399 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:45:17.400 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:17.400 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:17.400 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:17.400 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:17.401 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:45:17.401 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:45:27.837 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /patients
2025-07-18 13:45:27.838 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:27.838 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:27.839 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:45:30.630 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:45:30.631 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:30.631 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:30.631 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:45:30.648 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:45:30.648 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:45:30.648 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:30.648 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:30.648 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:30.648 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:30.649 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:45:30.649 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:30.649 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:30.649 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:45:30.650 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:45:30.650 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:45:30.665 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:45:30.672 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:45:30.673 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:45:30.673 [http-nio-8888-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:45:37.067 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /messages
2025-07-18 13:45:37.068 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:45:37.068 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:45:37.069 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:46:12.723 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 13:46:12.724 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:12.725 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:12.725 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 13:46:12.731 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:46:12.731 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:12.731 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:12.732 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:46:12.742 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:46:12.742 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:46:12.742 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:12.742 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:12.742 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:12.742 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:12.744 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:46:12.744 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:46:18.576 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 13:46:18.576 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:18.576 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:18.577 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 13:46:18.605 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:46:18.605 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:46:18.606 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:18.606 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:18.606 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:18.606 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:18.607 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:46:18.607 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:46:24.404 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:46:24.405 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:24.405 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:24.405 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:46:24.505 [http-nio-8888-exec-8] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:46:25.521 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:46:25.522 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:25.522 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:25.522 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:46:25.542 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:46:25.542 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:46:25.542 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:25.542 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:46:25.542 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:25.542 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:46:25.543 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:46:25.543 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:47:04.532 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:47:04.533 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:47:04.534 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:47:04.535 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:47:04.552 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:47:04.552 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:47:04.552 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:47:04.552 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:47:04.552 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:47:04.552 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:47:04.552 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:47:04.553 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:47:04.553 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:47:04.554 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:47:04.554 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:47:04.554 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:47:04.564 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:47:04.573 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:47:04.574 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:04.574 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:11.155 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10&userType=1
2025-07-18 13:47:11.165 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10&userType=1
2025-07-18 13:47:11.166 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:11.166 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:13.770 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10&userType=2
2025-07-18 13:47:13.778 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10&userType=2
2025-07-18 13:47:13.778 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:13.778 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:15.343 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10&userType=3
2025-07-18 13:47:15.354 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10&userType=3
2025-07-18 13:47:15.355 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:15.356 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:17.084 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:47:17.090 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:47:17.090 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:17.090 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:21.501 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users/1
2025-07-18 13:47:21.513 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users/1
2025-07-18 13:47:21.519 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserById(java.lang.Long); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:47:21.520 [http-nio-8888-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserById(java.lang.Long); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:48:21.397 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:48:21.400 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:48:21.400 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:48:21.401 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:48:21.429 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:48:21.429 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:48:21.429 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:48:21.429 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:48:21.429 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:48:21.429 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:48:21.430 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:48:21.430 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:48:21.430 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:48:21.430 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:48:21.431 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:48:21.431 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:48:21.445 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:48:21.465 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:48:21.466 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:48:21.466 [http-nio-8888-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:49:14.750 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles?page=1&size=10
2025-07-18 13:49:14.788 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles?page=1&size=10
2025-07-18 13:49:14.796 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 13:49:14.797 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:14.797 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:49:14.809 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:49:14.809 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:14.809 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:14.809 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:49:14.822 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:49:14.822 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:49:14.822 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:14.822 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:14.822 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:14.822 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:14.822 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:49:14.823 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:49:21.724 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:49:21.724 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:21.724 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:21.725 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:49:21.828 [http-nio-8888-exec-10] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:49:22.846 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:49:22.847 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:22.847 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:22.847 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:49:22.873 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:49:22.873 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:49:22.874 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:22.874 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:22.874 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:22.874 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:22.875 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:49:22.875 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:49:37.134 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:49:37.135 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:37.135 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:37.136 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:49:37.153 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:49:37.153 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:49:37.154 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:49:37.154 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:37.154 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:37.154 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:37.154 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:37.154 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:37.154 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:37.154 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:49:37.154 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:49:37.155 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:49:37.166 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:49:37.174 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:49:37.176 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:49:37.176 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:49:48.841 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 13:49:48.842 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:48.842 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:48.842 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 13:49:48.847 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:49:48.847 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:48.847 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:48.848 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:49:48.857 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:49:48.857 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:49:48.857 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:48.857 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:48.858 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:48.858 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:48.859 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:49:48.859 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:49:59.036 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:49:59.036 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:49:59.036 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:49:59.037 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:49:59.135 [http-nio-8888-exec-5] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:50:00.150 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:50:00.150 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:50:00.150 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:50:00.151 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:50:00.210 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:50:00.211 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:50:00.211 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:50:00.211 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:50:00.211 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:50:00.211 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:50:00.212 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:50:00.212 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:51:01.408 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 13:51:01.411 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:51:01.411 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:51:01.412 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 13:51:01.427 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:51:01.427 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:51:01.427 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:51:01.427 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:51:01.448 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:51:01.448 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:51:01.448 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:51:01.448 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:51:01.448 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:51:01.448 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:51:01.449 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:51:01.449 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:51:11.319 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:51:11.320 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:51:11.320 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:51:11.320 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:51:11.417 [http-nio-8888-exec-3] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:51:12.428 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:51:12.428 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:51:12.428 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:51:12.428 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:51:12.443 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:51:12.443 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:51:12.444 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:51:12.444 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:51:12.444 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:51:12.444 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:51:12.444 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:51:12.445 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:54:17.605 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:54:17.607 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:54:17.607 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:54:17.608 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:54:17.628 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:54:17.628 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:54:17.628 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:54:17.628 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:54:17.628 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:54:17.628 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:54:17.628 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:54:17.628 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:54:17.629 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:54:17.630 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:54:17.630 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:54:17.630 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:54:17.638 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:54:17.659 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:54:17.660 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:54:17.660 [http-nio-8888-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:55:44.641 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles?page=1&size=10
2025-07-18 13:55:44.675 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles?page=1&size=10
2025-07-18 13:55:44.677 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 13:55:44.678 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:44.678 [http-nio-8888-exec-1] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:55:44.684 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:55:44.684 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:44.684 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:44.684 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:55:44.710 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:55:44.710 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:55:44.711 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:44.711 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:44.711 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:44.711 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:44.711 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:55:44.712 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:55:49.573 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 13:55:49.574 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:49.574 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:49.574 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 13:55:49.678 [http-nio-8888-exec-3] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 13:55:50.692 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 13:55:50.692 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:50.692 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:50.692 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 13:55:50.707 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:55:50.707 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:50.707 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:50.708 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:55:50.708 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:50.708 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:50.708 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:55:50.708 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:55:51.811 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 13:55:51.812 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:51.812 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:51.812 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 13:55:51.832 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:55:51.832 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:55:51.832 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:51.832 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 13:55:51.832 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:51.832 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:51.832 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:51.832 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:51.832 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:51.833 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 13:55:51.833 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:55:51.833 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 13:55:51.843 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 13:55:51.851 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 13:55:51.852 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:55:51.852 [http-nio-8888-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 13:55:58.852 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/roles?page=1&size=10
2025-07-18 13:55:58.862 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/roles?page=1&size=10
2025-07-18 13:55:58.864 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 13:55:58.865 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:58.865 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 13:55:58.869 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 13:55:58.869 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:58.869 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:58.869 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 13:55:58.878 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 13:55:58.878 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 13:55:58.878 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:58.878 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 13:55:58.879 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:58.879 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 13:55:58.880 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 13:55:58.880 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:02:07.088 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/dashboard
2025-07-18 14:02:07.089 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:02:07.089 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:02:07.090 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/dashboard
2025-07-18 14:02:07.111 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:02:07.111 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:02:07.111 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:02:07.111 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:02:07.131 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:02:07.131 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:02:07.131 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:02:07.131 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:02:07.131 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:02:07.131 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:02:07.134 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:02:07.134 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:02:07.168 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 14:02:07.168 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:02:07.168 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:02:07.169 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 14:02:07.172 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:02:07.172 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:02:07.172 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:02:07.172 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:02:07.180 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:02:07.180 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:02:07.180 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:02:07.180 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:02:07.180 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:02:07.180 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:02:07.181 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:02:07.181 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:04:48.530 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 14:04:48.532 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:04:48.532 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:04:48.534 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 14:04:48.545 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:04:48.545 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:04:48.545 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:04:48.545 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:04:48.555 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:04:48.556 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:04:48.556 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:04:48.558 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:04:48.559 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:04:48.559 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:04:48.559 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:04:48.562 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:05:01.768 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 14:05:01.768 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:01.768 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:05:01.768 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 14:05:01.773 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:05:01.774 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:01.774 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:05:01.774 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:05:01.791 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:05:01.792 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:01.792 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:05:01.793 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:05:01.794 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:05:01.794 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:01.794 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:05:01.796 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:05:17.545 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 14:05:17.549 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:17.550 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 14:05:17.573 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:05:17.573 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:17.573 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:05:17.589 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:05:17.588 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:05:17.589 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:17.589 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:17.591 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:05:17.591 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:05:19.324 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-18 14:05:19.330 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:19.331 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /favicon.ico
2025-07-18 14:05:24.229 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /index
2025-07-18 14:05:24.233 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:24.239 [http-nio-8888-exec-1] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:05:29.624 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /index.html
2025-07-18 14:05:29.625 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:29.626 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /index.html
2025-07-18 14:05:29.630 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error
2025-07-18 14:05:29.632 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:29.632 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:05:35.132 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 14:05:35.132 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:35.133 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 14:05:35.146 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:05:35.146 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:35.146 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:05:35.153 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /favicon.ico
2025-07-18 14:05:35.154 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:35.155 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /favicon.ico
2025-07-18 14:05:35.207 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:05:35.207 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:05:35.207 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:35.207 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:35.208 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:05:35.208 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:05:40.088 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:05:40.088 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:40.089 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:05:40.209 [http-nio-8888-exec-6] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:05:41.545 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:05:41.546 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:41.546 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:05:41.616 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:05:41.616 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:05:41.616 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:41.616 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:41.617 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:05:41.617 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:05:52.272 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 14:05:52.273 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:52.274 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 14:05:52.283 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:05:52.283 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:52.283 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:05:52.288 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:05:52.288 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:52.288 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:05:52.288 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:52.288 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:05:52.288 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:05:53.352 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:05:53.352 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:05:53.352 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:06:06.935 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:06:06.937 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:06.937 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:06.938 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:06:06.971 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:06:06.971 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:06:06.971 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:06.971 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:06.971 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:06.971 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:06.972 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:06:06.972 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:06:08.815 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:06:08.816 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:08.816 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:08.817 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:06:08.828 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:06:08.829 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:08.829 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:08.829 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:06:08.831 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:06:08.832 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:08.832 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:08.834 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:06:08.837 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 14:06:08.837 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:08.837 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:08.839 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 14:06:08.913 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:06:08.913 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 14:06:08.913 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:08.913 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:08.913 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:08.913 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:08.914 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:06:08.914 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:06:08.924 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:06:08.924 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:08.924 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:08.925 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:06:08.925 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:06:08.925 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:08.925 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:08.926 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:06:13.326 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:06:13.326 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:13.327 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:13.327 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:06:13.409 [http-nio-8888-exec-7] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:06:14.422 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:06:14.423 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:14.423 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:14.423 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:06:14.445 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:06:14.445 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:06:14.445 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:14.445 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:14.445 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:14.445 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:14.446 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:06:14.446 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:06:15.840 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:06:15.841 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:15.841 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:15.842 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:06:15.862 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:06:15.862 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:06:15.862 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 14:06:15.863 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:15.863 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:15.863 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:06:15.863 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:15.863 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:15.863 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:06:15.863 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 14:06:15.863 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:06:15.864 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:06:15.869 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 14:06:15.876 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 14:06:15.877 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 14:06:15.877 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.sleephome.common.result.Result com.sleephome.controller.system.SystemUserController.getUserList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer); target is of class [com.sleephome.controller.system.SystemUserController]
2025-07-18 14:18:08.227 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 14:18:08.233 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-18 14:29:19.989 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 14:29:20.004 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 26780 (/Users/<USER>/AI/vscode/sleephome/target/classes started by songdaijian in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 14:29:20.004 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 14:29:20.004 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 14:29:20.467 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 14:29:20.470 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 14:29:20.471 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 14:29:20.471 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 14:29:20.503 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 14:29:20.504 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 480 ms
2025-07-18 14:29:20.638 [main] DEBUG com.sleephome.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-18 14:29:20.785 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 14:29:20.809 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Unsatisfied dependency expressed through method 'setFilterChains' parameter 0: Error creating bean with name 'filterChain' defined in class path resource [com/sleephome/config/SecurityConfig.class]: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'systemController' method 
com.sleephome.controller.SystemController#systemPage(Model)
to {GET [/system]}: There is already 'indexController' bean method
com.sleephome.controller.IndexController#system() mapped.
2025-07-18 14:29:20.810 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-18 14:29:20.816 [main] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-18 14:29:20.822 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration': Unsatisfied dependency expressed through method 'setFilterChains' parameter 0: Error creating bean with name 'filterChain' defined in class path resource [com/sleephome/config/SecurityConfig.class]: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'systemController' method 
com.sleephome.controller.SystemController#systemPage(Model)
to {GET [/system]}: There is already 'indexController' bean method
com.sleephome.controller.IndexController#system() mapped.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.resolveMethodArguments(AutowiredAnnotationBeanPostProcessor.java:875)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.inject(AutowiredAnnotationBeanPostProcessor.java:828)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:492)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1416)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:950)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:738)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:440)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.sleephome.SleepManagementSystemApplication.main(SleepManagementSystemApplication.java:12)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'filterChain' defined in class path resource [com/sleephome/config/SecurityConfig.class]: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'systemController' method 
com.sleephome.controller.SystemController#systemPage(Model)
to {GET [/system]}: There is already 'indexController' bean method
com.sleephome.controller.IndexController#system() mapped.
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:654)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:642)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1162)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:560)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1633)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1597)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1488)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1337)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredMethodElement.resolveMethodArguments(AutowiredAnnotationBeanPostProcessor.java:867)
	... 20 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.security.web.SecurityFilterChain]: Factory method 'filterChain' threw exception with message: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'systemController' method 
com.sleephome.controller.SystemController#systemPage(Model)
to {GET [/system]}: There is already 'indexController' bean method
com.sleephome.controller.IndexController#system() mapped.
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:171)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:650)
	... 36 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mvcHandlerMappingIntrospector' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'systemController' method 
com.sleephome.controller.SystemController#systemPage(Model)
to {GET [/system]}: There is already 'indexController' bean method
com.sleephome.controller.IndexController#system() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1770)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:598)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1172)
	at org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry.createMvcMatchers(AbstractRequestMatcherRegistry.java:112)
	at org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry.requestMatchers(AbstractRequestMatcherRegistry.java:210)
	at org.springframework.security.config.annotation.web.AbstractRequestMatcherRegistry.requestMatchers(AbstractRequestMatcherRegistry.java:276)
	at com.sleephome.config.SecurityConfig.lambda$filterChain$4(SecurityConfig.java:65)
	at org.springframework.security.config.annotation.web.builders.HttpSecurity.authorizeHttpRequests(HttpSecurity.java:1466)
	at com.sleephome.config.SecurityConfig.filterChain(SecurityConfig.java:64)
	at com.sleephome.config.SecurityConfig$$SpringCGLIB$$0.CGLIB$filterChain$2(<generated>)
	at com.sleephome.config.SecurityConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.sleephome.config.SecurityConfig$$SpringCGLIB$$0.filterChain(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:139)
	... 37 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Ambiguous mapping. Cannot map 'systemController' method 
com.sleephome.controller.SystemController#systemPage(Model)
to {GET [/system]}: There is already 'indexController' bean method
com.sleephome.controller.IndexController#system() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1770)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:598)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:663)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1320)
	at org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors(BeanFactoryUtils.java:368)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.initHandlerMappings(HandlerMappingIntrospector.java:110)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.afterPropertiesSet(HandlerMappingIntrospector.java:98)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1817)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1766)
	... 58 common frames omitted
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'systemController' method 
com.sleephome.controller.SystemController#systemPage(Model)
to {GET [/system]}: There is already 'indexController' bean method
com.sleephome.controller.IndexController#system() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:667)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:633)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:331)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:441)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:76)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:298)
	at java.base/java.util.LinkedHashMap.forEach(LinkedHashMap.java:987)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:296)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:224)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:212)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:225)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1817)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1766)
	... 71 common frames omitted
2025-07-18 14:31:07.109 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 14:31:07.121 [main] INFO  com.sleephome.SleepManagementSystemApplication - Starting SleepManagementSystemApplication using Java 23.0.2 with PID 27178 (/Users/<USER>/AI/vscode/sleephome/target/classes started by songdaijian in /Users/<USER>/AI/vscode/sleephome)
2025-07-18 14:31:07.123 [main] DEBUG com.sleephome.SleepManagementSystemApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-18 14:31:07.124 [main] INFO  com.sleephome.SleepManagementSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-18 14:31:07.577 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8888 (http)
2025-07-18 14:31:07.580 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8888"]
2025-07-18 14:31:07.581 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 14:31:07.581 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-18 14:31:07.613 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 14:31:07.613 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 467 ms
2025-07-18 14:31:07.751 [main] DEBUG com.sleephome.security.jwt.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-18 14:31:07.900 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-18 14:31:07.984 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1999149e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@65d73bd, org.springframework.security.web.context.SecurityContextHolderFilter@1c72189f, org.springframework.security.web.header.HeaderWriterFilter@340c5fb6, org.springframework.security.web.authentication.logout.LogoutFilter@44bbb7c6, com.sleephome.security.jwt.AuthTokenFilter@7ccd611e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@58882a93, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@19dac2d6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@27896d3b, org.springframework.security.web.session.SessionManagementFilter@5112b7, org.springframework.security.web.access.ExceptionTranslationFilter@56c42964, org.springframework.security.web.access.intercept.AuthorizationFilter@63a84bb6]
2025-07-18 14:31:08.075 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8888"]
2025-07-18 14:31:08.085 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8888 (http) with context path ''
2025-07-18 14:31:08.089 [main] INFO  com.sleephome.SleepManagementSystemApplication - Started SleepManagementSystemApplication in 1.15 seconds (process running for 1.263)
2025-07-18 14:31:09.129 [http-nio-8888-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 14:31:09.129 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 14:31:09.130 [http-nio-8888-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 14:31:09.134 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 14:31:09.137 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:09.137 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:09.140 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 14:31:09.152 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:31:09.153 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:09.153 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:09.153 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:31:09.234 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:31:09.235 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:09.235 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:09.239 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:31:09.239 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:09.239 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:09.243 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:31:09.245 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:31:34.427 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:31:34.434 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:34.435 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:34.439 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:31:34.470 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:31:34.470 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:31:34.470 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:34.470 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:34.470 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:34.470 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:34.472 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:31:34.473 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:31:38.585 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:31:38.586 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:38.586 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:38.586 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:31:38.739 [http-nio-8888-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 14:31:38.879 [http-nio-8888-exec-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f69f099
2025-07-18 14:31:38.879 [http-nio-8888-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 14:31:39.014 [http-nio-8888-exec-2] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:31:40.048 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:31:40.049 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:40.049 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:40.049 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:31:40.113 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:31:40.113 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:31:40.113 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:40.113 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:40.113 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:40.113 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:40.114 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:31:40.114 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:31:46.965 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 14:31:46.967 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:46.967 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:46.968 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 14:31:46.976 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:31:46.978 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:46.978 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:46.980 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:31:46.998 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:31:46.998 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:31:46.999 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:46.999 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:46.999 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:46.999 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:47.000 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:31:47.000 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:31:51.759 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:31:51.760 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:51.761 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:51.762 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:31:51.857 [http-nio-8888-exec-4] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:31:52.875 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:31:52.877 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:52.877 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:52.880 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:31:52.916 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:31:52.916 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:31:52.917 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:52.917 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:52.917 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:52.917 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:52.918 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:31:52.919 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:31:58.215 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /patients
2025-07-18 14:31:58.216 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:31:58.216 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:31:58.221 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:32:00.623 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:32:00.623 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:00.623 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:00.623 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:32:00.642 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:32:00.642 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:32:00.642 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:00.642 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 14:32:00.642 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:00.643 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:00.643 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:00.643 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:00.643 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:00.644 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 14:32:00.644 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:32:00.645 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:32:00.652 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 14:32:00.674 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 14:32:00.678 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 14:32:00.679 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:00.679 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:32:00.685 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:32:00.686 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:00.686 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:00.686 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:32:00.698 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:32:00.698 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:32:00.698 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:00.698 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:00.698 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:00.698 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:00.699 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:32:00.700 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:32:07.579 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:32:07.579 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:07.579 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:07.580 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:32:07.677 [http-nio-8888-exec-2] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:32:08.691 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:32:08.693 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:08.693 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:08.695 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:32:08.713 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:32:08.713 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:32:08.714 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:08.714 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:08.714 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:08.714 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:08.715 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:32:08.715 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:32:09.558 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:32:09.558 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:09.558 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:09.559 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:32:09.574 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:32:09.574 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 14:32:09.574 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:32:09.574 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:09.574 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:09.575 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:09.575 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:09.575 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:09.575 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:09.576 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:32:09.576 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 14:32:09.576 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:32:09.582 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 14:32:09.592 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 14:32:09.593 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 14:32:09.595 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:09.595 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:32:09.599 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:32:09.599 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:09.599 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:09.599 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:32:09.607 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:32:09.607 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:32:09.607 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:09.607 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:09.607 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:09.608 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:09.608 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:32:09.608 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:32:14.642 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:32:14.643 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:14.643 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:14.644 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:32:14.734 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:32:15.748 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:32:15.748 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:15.748 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:15.748 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:32:15.767 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:32:15.767 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:15.768 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:15.769 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:32:15.769 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:15.769 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:15.770 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:32:15.770 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:32:18.996 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:32:18.996 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:18.996 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:32:47.034 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:32:47.036 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:47.036 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:47.039 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:32:47.089 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:32:47.089 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:32:47.089 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 14:32:47.089 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:47.089 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:47.089 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:47.089 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:47.089 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:47.089 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:47.090 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:32:47.090 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 14:32:47.090 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:32:47.111 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 14:32:47.131 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 14:32:47.132 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 14:32:47.132 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:47.133 [http-nio-8888-exec-1] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:32:47.138 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:32:47.138 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:47.139 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:47.139 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:32:47.149 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:32:47.149 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:32:47.149 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:47.150 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:47.150 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:47.150 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:47.150 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:32:47.151 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:32:50.471 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:32:50.471 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:50.471 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:50.473 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:32:55.529 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:32:55.531 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:55.531 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:55.532 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:32:55.553 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:32:55.553 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:55.554 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:55.555 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:32:55.560 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:32:55.561 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:55.561 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:55.567 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:32:55.618 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 14:32:55.618 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:55.618 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:55.619 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 14:32:55.665 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 14:32:55.665 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:55.666 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:55.666 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:32:55.667 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:55.668 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:55.668 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:32:55.671 [http-nio-8888-exec-4] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:32:55.680 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:32:55.680 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:32:55.680 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:55.680 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:32:55.680 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:55.680 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:32:55.681 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:32:55.681 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:33:11.177 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:33:11.182 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:11.182 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:11.183 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:33:11.283 [http-nio-8888-exec-7] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:33:12.302 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:33:12.302 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:12.302 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:12.303 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:33:12.326 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:33:12.326 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:33:12.326 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:12.326 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:12.326 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:12.326 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:12.327 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:33:12.327 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:33:13.391 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:33:13.392 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:13.392 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:13.393 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:33:13.413 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:33:13.413 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:33:13.414 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:13.414 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:13.414 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 14:33:13.414 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:13.414 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:13.414 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:13.414 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:13.415 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:33:13.415 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 14:33:13.415 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:33:13.422 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 14:33:13.434 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 14:33:13.435 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 14:33:13.436 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:13.436 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:33:13.442 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:33:13.442 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:13.442 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:13.442 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:33:13.457 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:33:13.457 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:33:13.457 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:13.457 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:13.457 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:13.457 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:13.457 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:33:13.457 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:33:15.848 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:33:15.848 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:15.848 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:15.853 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:33:20.393 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:33:20.393 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:20.393 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:20.393 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:33:20.474 [http-nio-8888-exec-9] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:33:21.500 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:33:21.500 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:21.500 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:21.500 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:33:21.514 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:33:21.514 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:33:21.515 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:21.515 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:21.515 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:21.515 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:21.515 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:33:21.515 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:21.515 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:21.516 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:33:21.516 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:33:21.517 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:33:26.728 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:33:26.731 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:26.731 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:26.732 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:33:26.747 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:33:26.747 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:33:26.747 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:26.747 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:26.747 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:26.748 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:26.748 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:33:26.749 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:33:26.749 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:26.749 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:26.750 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 14:33:26.750 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:26.750 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:26.750 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:33:26.750 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:33:26.751 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 14:33:26.823 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 14:33:26.841 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 14:33:26.842 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 14:33:26.842 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:26.843 [http-nio-8888-exec-5] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:33:26.849 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:33:26.850 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:26.850 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:26.850 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:33:26.860 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:33:26.860 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:33:26.860 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:26.860 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:26.860 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:26.860 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:26.861 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:33:26.861 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:33:26.864 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:33:26.864 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:33:26.864 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:33:26.866 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:34:23.765 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /community/login
2025-07-18 14:34:23.769 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:23.769 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:23.770 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /community/login
2025-07-18 14:34:23.839 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:34:23.839 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:34:23.839 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:23.839 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:23.840 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:23.840 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:23.840 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:34:23.843 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:23.843 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:23.844 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:34:23.844 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:34:23.845 [http-nio-8888-exec-10] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:34:34.316 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:34:34.317 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:34.317 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:34.317 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:34:34.464 [http-nio-8888-exec-4] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:34:35.486 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:34:35.486 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:35.486 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:35.486 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:34:35.506 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:34:35.506 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:34:35.508 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:35.506 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:34:35.509 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:35.509 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:35.509 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:35.510 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:35.510 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:35.511 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:34:35.512 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:34:35.512 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:34:40.379 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system
2025-07-18 14:34:40.380 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:40.380 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:40.380 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system
2025-07-18 14:34:40.406 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:34:40.406 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:34:40.406 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:40.406 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:40.406 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:40.407 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/system.js
2025-07-18 14:34:40.407 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:40.407 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:40.406 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:40.407 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:34:40.407 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:40.407 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:40.407 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:34:40.409 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/system.js
2025-07-18 14:34:40.409 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:34:40.414 [http-nio-8888-exec-3] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:34:40.498 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/system/users?page=1&size=10
2025-07-18 14:34:40.519 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/system/users?page=1&size=10
2025-07-18 14:34:40.519 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /error?page=1&size=10
2025-07-18 14:34:40.520 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:40.520 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:34:40.524 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:34:40.525 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:40.525 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:40.525 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:34:40.534 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:34:40.534 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:40.534 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:40.536 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:34:40.536 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:34:40.536 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:40.536 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:40.536 [http-nio-8888-exec-5] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:40.536 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:40.536 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:34:40.536 [http-nio-8888-exec-9] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:34:40.537 [http-nio-8888-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:34:41.952 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:34:41.952 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:41.952 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:41.954 [http-nio-8888-exec-8] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:34:41.957 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /.well-known/appspecific/com.chrome.devtools.json
2025-07-18 14:34:41.957 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:34:41.957 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:34:41.959 [http-nio-8888-exec-6] ERROR com.sleephome.security.jwt.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-18 14:35:56.836 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 14:35:56.837 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:35:56.837 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:35:56.838 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 14:35:56.843 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:35:56.843 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:35:56.843 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:35:56.844 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:35:56.858 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:35:56.858 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:35:56.858 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:35:56.859 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:35:56.859 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:35:56.859 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:35:56.859 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:35:56.859 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:36:02.727 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:36:02.728 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:02.728 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:02.728 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:36:02.855 [http-nio-8888-exec-6] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:36:03.866 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:36:03.866 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:03.866 [http-nio-8888-exec-10] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:03.867 [http-nio-8888-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:36:03.883 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:36:03.883 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:36:03.883 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:03.883 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:03.883 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:03.883 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:03.884 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:36:03.884 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:36:07.091 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-07-18 14:36:07.092 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:07.092 [http-nio-8888-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:07.092 [http-nio-8888-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-07-18 14:36:07.096 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /doctor/login
2025-07-18 14:36:07.096 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:07.096 [http-nio-8888-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:07.097 [http-nio-8888-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /doctor/login
2025-07-18 14:36:07.111 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:36:07.111 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:07.111 [http-nio-8888-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:07.112 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:36:07.112 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:07.112 [http-nio-8888-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:07.112 [http-nio-8888-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:36:07.113 [http-nio-8888-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:36:11.851 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-07-18 14:36:11.852 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:11.852 [http-nio-8888-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:11.852 [http-nio-8888-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-07-18 14:36:11.940 [http-nio-8888-exec-6] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-07-18 14:36:12.951 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-07-18 14:36:12.951 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:12.951 [http-nio-8888-exec-9] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:12.952 [http-nio-8888-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-07-18 14:36:12.971 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/notification.js
2025-07-18 14:36:12.971 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-07-18 14:36:12.972 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:12.972 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-18 14:36:12.972 [http-nio-8888-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:12.972 [http-nio-8888-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 72038BDA6E28D74DD9320646ABC521CF
2025-07-18 14:36:12.972 [http-nio-8888-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/notification.js
2025-07-18 14:36:12.973 [http-nio-8888-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-07-18 14:56:07.006 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-18 14:56:07.018 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
